name: Builds

on:
  workflow_call:
    outputs:
      front-mynotary-built:
        description: "Whether front-mynotary was built"
        value: ${{ jobs.build-front-mynotary.outputs.front-mynotary-built }}
    secrets:
      GCP_SERVICE_ACCOUNT_KEY:
        required: true

env:
  GCP_PROJECT: mynotary-preproduction
  CONFIGURATION: preproduction

jobs:
  build-front-mynotary:
    name: 🛠️ Build front-mynotary
    runs-on: ubuntu-latest
    outputs:
      front-mynotary-built: ${{ steps.check-artifact.outputs.front-mynotary-built }}
    environment: 'preproduction'
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup
      - uses: nrwl/nx-set-shas@v4
      - name: Build front-mynotary
        shell: bash
        run: pnpm nx affected --target build --exclude='*,!tag:scope:front-mynotary' --configuration $CONFIGURATION
      - name: Post build tasks
        if: ${{ hashFiles('dist/') != '' }}
        shell: bash
        run: pnpm nx run front-mynotary:post-build
      - name: Check if front-mynotary build exists
        if: ${{ hashFiles('dist/') != '' }}
        id: check-artifact
        shell: bash
        run: echo "front-mynotary-built=true" >> "$GITHUB_OUTPUT"
      - name: Upload front mynotary build
        if: ${{ hashFiles('dist/') != '' }}
        uses: actions/upload-artifact@v4
        with:
          name: front-mynotary-build
          path: dist
          retention-days: 1

  build-backend-apps:
    name: 🛠️ Build api - ${{ matrix.type }}
    runs-on: ubuntu-latest
    environment: 'preproduction'
    strategy:
      fail-fast: false
      matrix:
        type: ['api-auth', 'api-emails', 'api-files', 'api-mynotary', 'api-signatures', 'jobs']
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
      - uses: ./.github/actions/setup
      - name: Authenticate to GCP
        uses: google-github-actions/auth@v2
        with:
          credentials_json: ${{ secrets.GCP_SERVICE_ACCOUNT_KEY }}
          export_environment_variables: true
          create_credentials_file: true
      - name: Use GCP Auth in Docker
        run: gcloud auth configure-docker europe-west9-docker.pkg.dev
      - uses: nrwl/nx-set-shas@v4
      - name: Build affected and push docker images
        run: pnpm nx affected --target deploy-docker-image --exclude='*,!tag:scope:${{ matrix.type }}' --parallel=4
