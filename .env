# Environment variables declared in this file are automatically made available to Prisma.
# See the documentation for more detail: https://pris.ly/d/prisma-schema#accessing-environment-variables-from-the-schema

# Prisma supports the native connection string format for PostgreSQL, MySQL, SQLite, SQL Server, MongoDB and CockroachDB.
# See the documentation for all the connection string options: https://pris.ly/d/connection-strings

#DATABASE_URL="postgresql://api:api@localhost:5432/mynotary"

# Update this version value to force <PERSON><PERSON><PERSON> to reload the new version.
# Should be updated on each deployment with major changes.
NX_PUBLIC_FRONT_MYNOTARY_VERSION=2.0.79

GCP_CLOUD_STORAGE_BUCKET=mynotary-development
GCP_CLOUD_STORAGE_TEMPORARY_BUCKET=temporary-development
GCP_CLOUD_STORAGE_BUCKET_ZIP=zip-files-development
GCP_CLOUD_STORAGE_BUCKET_TRASH=trash-development
LEGACY_PUBLIC_KEY=LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF3NzVHbDlYYTU2RXR1bVRVbldtNQowLytJb0dvZXNGTWRtRmtpZUdPL0YyTHQxOEVOWEFDT2Y4Si9idzBXSFJoeXRYSUJvb0FQMUo5SFJiTTNycldlCm4yaVZXTVhCVGpsMnJtY0xxMWNqNXN6OVI3RC9hTDdoSEdqTk52WnJBckE2M0x3TWF0b3d4Slc0YUZDazR0d3gKVWJvdWVDTlVsTkhxd2o1UmxnM29GbTBQY2ZjaDFrSWFkNlRGWlNwODE2VnJ0bGtMUHNPVlZxQWU1dmZId2pZUwpiNnNXY1JIWHlNNmk3bkVybURRVTVORmNiWUVUcUJBelo4ZHFKcVVBL3BwWUpLeHFSOVdKQThmRStqa2pWZk5yCmxZY3hCY2NGMzNvMFUzWUx4bVFPMjl5bm9ETjdHYURSd0d4K3ovWWlBOUJtZS9KVWYzYVBNOFBrTkJYYWJtR0EKeFFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg==
FIREBASE_API_KEY="AIzaSyASDp00rxv9_A60nd4N41vR8BeIag9k074"
FIREBASE_PROJECT_ID=mynotary-development
TABLEAU_CLIENT_ID=TABLEAU_CLIENT_ID
TABLEAU_SECRET_ID=TABLEAU_SECRET_ID
TABLEAU_SECRET_KEY=TABLEAU_SECRET_KEY
ZOHO_CLIENT_ID=ZOHO_CLIENT_ID
ZOHO_CLIENT_SECRET=ZOHO_CLIENT_SECRET
ZOHO_ORGANIZATION_ID=ZOHO_ORGANIZATION_ID
ZOHO_PRODUCT_V4="3033897000000668156"
ZOHO_TVA_20="3033897000000075769"
ZOHO_TVA_DOM_TOM="3033897000000518048"
ZOHO_TVA_GUYANE="3033897000004765019"
MN_APP_URL="http://localhost:9002"
UNIS_CLIENT_ID=UNIS_CLIENT_ID
UNIS_CLIENT_SECRET=UNIS_CLIENT_SECRET
API_JAVA_URL="http://localhost:8111"
API_MYNOTARY_URL="http://localhost:3000/api-mynotary/v1"
GCP_PROJECT=mynotary-development
AR24_TOKEN="9968b587cafb2a4304fe7c43e92c00840533b017"
AR24_URL=https://sandbox.ar24.fr/api
AR24_SENDER_USER_ID="50919"
MYNOTARY_PUBLIC_KEY="LS0tLS1CRUdJTiBQVUJMSUMgS0VZLS0tLS0KTUlJQklqQU5CZ2txaGtpRzl3MEJBUUVGQUFPQ0FROEFNSUlCQ2dLQ0FRRUF1MmNWQkVtM1owejZLT2xtSE1zVwp2b2RTWHhNQi9nMVkxbTlxWmp5YWI2T0lRckM1d0haOCtrdEtXU05sbnFZWlZpT2pyalFDeWNhU01hQmRZL0I2CkwrTTRHQTRZampoM2NVZUJvQzcvdVJ6SEE2OEVtTzByZTZNSVRrVFRLUUFEeStwUDdMQVp1cVgreW9vVDNMVlcKM1RWdEs1VmlTUGEvV1FDbmVZS1UyaGNoQnlJS3NFN1J2anZTbjNNc21sdW9pMHlVS3NON09ra0gxejF5cVpoawppMGxiSnhiaXg4ZzUvMzNBY04rQnRydnhOQ0VjWkMwdStEb1F3MThNaFBqQTVYN3ptdmxkaEJHdkZsdThiTEI4Cjl1SWFPNXRzQW1yQy9RUGc3c2RuMGtsWnRtMndKSkVTRWN2SW5OZ2dEeWdJcUxrcU1KZUNIOG5PUDhHMzdpZGkKdlFJREFRQUIKLS0tLS1FTkQgUFVCTElDIEtFWS0tLS0tCg=="
MYNOTARY_PRIVATE_KEY="****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************"
MYNOTARY_API_KEY=fake-mynotary-key
API_FILES_V2_URL=http://localhost:3002/api-files/v1
YOUSIGN_API_KEY=DZR8CLBIFi7rU482K85nf546AKFpNWTi
ORPI_TELEMAC_TOKEN=FAKE_VALUE_ORPI_TELEMAC_TOKEN
PORTALYS_URL_API_ADSN=http://dev.portalys.fr:3059/api-adsn
PORTALYS_URL_API_ETATCIVIL=http://dev.portalys.fr:3052/api-etatscivils
PORTALYS_URL_API_TELEACTES=http://dev.portalys.fr:3051/api-teleactes
PORTALYS_URL_ANF_STOCK=http://anf-stock.notaires.fr/fno-ihm
MX_PANEL_PROJECTID=MX_PANEL_PROJECTID
MX_PANEL_PROJECT_TOKEN=MX_PANEL_PROJECT_TOKEN
MX_PANEL_SERVICE_ACCOUNT_ID=MX_PANEL_SERVICE_ACCOUNT_ID
MX_PANEL_SERVICE_ACCOUNT_SECRET=MX_PANEL_SERVICE_ACCOUNT_SECRET
LEARN_WORLDS_CLIENT_ID=LEARN_WORLDS_CLIENT_ID
LEARN_WORLDS_CLIENT_SECRET=LEARN_WORLDS_CLIENT_SECRET
MX_ORGANIZATIONS_TABLE_ID=MX_ORGANIZATIONS_TABLE_ID
DOMAIN_ENV=development
PORTALYS_URL_ANF=PORTALYS_URL_ANF
PORTALYS_URL_PLANETE_ANONYME=PORTALYS_URL_PLANETE_ANONYME
PORTALYS_PLANETE_ID=PORTALYS_PLANETE_ID
PORTALYS_URL_PLANETE=PORTALYS_URL_PLANETE
PORTALYS_USER_ID=PORTALYS_USER_ID

# Nx 18 enables using plugins to infer targets by default
# This is disabled for existing workspaces to maintain compatibility
# For more info, see: https://nx.dev/concepts/inferred-tasks
NX_ADD_PLUGINS=false
STRIPE_API_KEY=STRIPE_API_KEY
API_V2_EMAIL_URL=http://localhost:3005/api-email/v1
GCP_APPLICATION_API_KEY=GCP_APPLICATION_API_KEY
SLACK_TOKEN=SLACK_TOKEN
