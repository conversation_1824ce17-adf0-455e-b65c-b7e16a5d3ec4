import { Injectable } from '@nestjs/common';
import { RegisterEntriesRepository } from './register-entries.repository';
import { RegisterEntryStatus, RegisterEntry, RegisterEntryType } from './register-entries';
import { RegisterAuthorizationsService } from './register-authorizations.service';
import { PermissionType } from '@mynotary/crossplatform/roles/api';

@Injectable()
export class RegisterEntriesService {
  constructor(
    private registerEntriesRepository: RegisterEntriesRepository,
    private registerAuthorizationsService: RegisterAuthorizationsService
  ) {}

  async findRegisterEntry(args: FindRegisterEntryArgs): Promise<RegisterEntry | null> {
    const entries = await this.registerEntriesRepository.getRegisterEntries({
      contractId: args.contractId,
      page: 0,
      pageSize: 1,
      status: args.status
    });

    if (entries.length === 0) {
      return null;
    }

    return entries[0];
  }

  async updateRegisterEntry(updateEntry: UpdateRegisterEntryArgs): Promise<void> {
    await this.registerEntriesRepository.updateRegisterEntry(updateEntry);
  }

  async getRegisterEntries(args: GetRegisterEntriesArgs): Promise<RegisterEntry[]> {
    if (args.organizationId && args.userId) {
      const hasPermission = await this.registerAuthorizationsService.hasRegisterAccess({
        organizationId: args.organizationId,
        permissionType: PermissionType.READ_ORGANIZATION_REGISTER,
        registerType: args.type,
        shouldBeOwner: false,
        userId: args.userId
      });

      if (!hasPermission) {
        const modifiedArgs = { ...args };
        delete modifiedArgs.organizationId;
        return await this.registerEntriesRepository.getRegisterEntries(modifiedArgs);
      }
    }

    return await this.registerEntriesRepository.getRegisterEntries(args);
  }
}

export interface UpdateRegisterEntryArgs {
  id: string;
  observations?: string;
  status?: RegisterEntryStatus;
}

export interface FindRegisterEntryArgs {
  contractId: string;
  status: RegisterEntryStatus;
}

interface GetRegisterEntriesArgs {
  contractId?: string;
  entryNumber?: number;
  legalOperationId?: string;
  organizationId?: string;
  page: number;
  pageSize: number;
  search?: string;
  status?: RegisterEntryStatus;
  type: RegisterEntryType;
  userId?: string;
}
