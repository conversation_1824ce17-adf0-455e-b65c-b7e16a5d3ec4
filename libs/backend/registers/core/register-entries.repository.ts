import { RegisterEntryUpdate, RegisterEntry, RegisterEntryType, RegisterEntryStatus } from './register-entries';

export abstract class RegisterEntriesRepository {
  abstract getRegisterEntries(args: GetRegisterEntriesArgs): Promise<RegisterEntry[]>;
  abstract updateRegisterEntry(args: RegisterEntryUpdate): Promise<void>;
}

export interface GetRegisterEntriesArgs {
  contractId?: string;
  entryNumber?: number;
  legalOperationId?: string;
  organizationId?: string;
  page?: number;
  pageSize?: number;
  registerId?: string;
  search?: string;
  status?: RegisterEntryStatus;
  type?: RegisterEntryType;
  userId?: string;
}
