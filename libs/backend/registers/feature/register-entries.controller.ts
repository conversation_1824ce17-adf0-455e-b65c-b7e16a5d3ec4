import { Controller, Get, Query, UseGuards, UnprocessableEntityException } from '@nestjs/common';
import { RegisterEntriesService } from '@mynotary/backend/registers/core';
import { RegisterEntryListDto, RegisterTypeDto, RegisterStatusDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { convertEnum } from '@mynotary/crossplatform/shared/util';
import { RegisterEntryType } from '@mynotary/backend/registers/core';
import { HasRegisterEntriesAccess } from '@mynotary/backend/registers/authorization';
import { queryResolver } from '@mynotary/backend/shared/auth-util';

@Controller()
export class RegisterEntriesController {
  constructor(private registerEntriesService: RegisterEntriesService) {}

  @UseGuards(
    HasRegisterEntriesAccess({
      legalOperationIdResolver: queryResolver('legalOperationId'),
      organizationIdResolver: queryResolver('organizationId')
    })
  )
  @Get('/register-entries')
  async getRegisterEntries(@Query() query: GetRegisterEntriesDto): Promise<RegisterEntryListDto> {
    if (query?.entryNumber != null && query?.search != null) {
      throw new UnprocessableEntityException('Cannot search by entryNumber and search text at the same time');
    }

    const args = {
      ...query,
      type: convertEnum(RegisterEntryType, query.type)
    };
    const entries = await this.registerEntriesService.getRegisterEntries(args);

    return {
      items: entries.map((entry) => {
        return {
          ...entry,
          legalOperationId: entry.operationId?.toString() ?? '',
          status: convertEnum(RegisterStatusDto, entry.status),
          type: convertEnum(RegisterTypeDto, entry.type)
        };
      }),
      total: entries.length
    };
  }
}

interface GetRegisterEntriesDto {
  entryNumber?: number;
  legalOperationId?: string;
  organizationId?: string;
  page: number;
  pageSize: number;
  search?: string;
  type: RegisterTypeDto;
  userId: string;
}
