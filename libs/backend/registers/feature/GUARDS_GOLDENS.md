# Guards Goldens

  > ⚠ This file is auto-generated. Do not edit by hand.
## management-registers.controller.ts
```ts
@UseGuards(
    OrGuard([
      [
        HasManagementRegisterAccess({
          organizationIdResolver: queryResolver('organizationId'),
          permissionType: PermissionType.READ_ORGANIZATION_REGISTER,
          shouldBeOwner: false
        })
      ],
      [IsAdmin()]
    ])
  )
@Get('/management-registers')

@UseGuards(
    OrGuard([
      [
        HasManagementRegisterAccess({
          organizationIdResolver: bodyResolver('organizationId'),
          permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER,
          shouldBeOwner: true
        })
      ],
      [IsAdmin()]
    ])
  )
@Put('/management-registers/:id')

```
## receivership-registers.controller.ts
```ts
@UseGuards(
    OrGuard([
      [
        HasReceivershipRegisterAccess({
          organizationIdResolver: queryResolver('organizationId'),
          permissionType: PermissionType.READ_ORGANIZATION_REGISTER,
          shouldBeOwner: false
        })
      ],
      [IsAdmin()]
    ])
  )
@Get('/receivership-registers')

@UseGuards(
    OrGuard([
      [
        HasReceivershipRegisterAccess({
          organizationIdResolver: bodyResolver('organizationId'),
          permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER,
          shouldBeOwner: true
        })
      ],
      [IsAdmin()]
    ])
  )
@Put('/receivership-registers/:id')

```
## register-entries.controller.ts
```ts
@UseGuards(
    HasRegisterEntriesAccess({
      legalOperationIdResolver: queryResolver('legalOperationId'),
      organizationIdResolver: queryResolver('organizationId')
    })
  )
@Get('/register-entries')

```
## transaction-registers.controller.ts
```ts
@UseGuards(
    OrGuard([
      [
        HasTransactionRegisterAccess({
          organizationIdResolver: queryResolver('organizationId'),
          permissionType: PermissionType.READ_ORGANIZATION_REGISTER,
          shouldBeOwner: false
        })
      ],
      [IsAdmin()]
    ])
  )
@Get('/transaction-registers')

@UseGuards(
    OrGuard([
      [
        HasTransactionRegisterAccess({
          organizationIdResolver: bodyResolver('organizationId'),
          permissionType: PermissionType.CREATE_ORGANIZATION_REGISTER,
          shouldBeOwner: true
        })
      ],
      [IsAdmin()]
    ])
  )
@Put('/transaction-registers/:id')

```
