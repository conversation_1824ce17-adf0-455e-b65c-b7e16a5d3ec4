import { Invoice, InvoiceConfig, InvoiceConfigField } from '@mynotary/crossplatform/invoices/core';
import { InvalidInputError } from '@mynotary/crossplatform/shared/util';

export type InvoiceConfigUpdate = {
  config: InvoiceConfigField;
  id: string;
};

export type InvoiceConfigUpdateCurrentNumber = {
  currentNumber: number;
  id: string;
};

export type InvoiceConfigNew = {
  organizationId: string;
  userId: string;
} & InvoiceConfigField;

export interface InvoiceExtended extends Invoice {
  creatorName: string;
  invoiceNumber: number;
  invoicePrefix: string;
}

export interface InvoiceConfigExtended extends InvoiceConfig {
  currentNumber: number;
}

export class InvoiceConfigAlreadyExistsError extends InvalidInputError {
  name = InvoiceConfigAlreadyExistsError.name;

  constructor(organizationId: string, cause?: unknown) {
    super({
      cause,
      displayedMessage: `La configuration pour l'organisation avec l'id "${organizationId}" existe déjà`,
      message: `config within organizationId "${organizationId}" already exists`
    });
  }
}

export interface InvoiceReports {
  amountHt: string;
  amountTtc: string;
  amountTva: string;
  companyName: string;
  creationTime: string;
  creatorName: string;
  discounts: string;
  expirationTime: string;
  invoiceLabel: string;
  negociatorName: string;
  operationLabel: string;
  registerEntryNumber: string;
  status: string;
  tva: string;
  type: string;
}
