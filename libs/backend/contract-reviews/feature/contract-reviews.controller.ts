import { Body, Controller, Delete, Param, Post, UseGuards } from '@nestjs/common';
import { ContractReviewRequestNewArgs, ContractReviewsService } from '@mynotary/backend/contract-reviews/core';
import {
  ContractReviewDto,
  ContractReviewRequestNewDto,
  TaskDeletedDto,
  TaskDto,
  TaskTypeDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { HasContractPermission } from '@mynotary/backend/legals/api';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { convertEnum } from '@mynotary/crossplatform/shared/util';
import { bodyResolver } from '@mynotary/backend/shared/auth-util';

@Controller()
export class ContractReviewsController {
  constructor(private contractReviewsService: ContractReviewsService) {}

  @UseGuards(
    HasContractPermission(PermissionType.CREATE_CONTRACT_TASK_ASK_REVIEW, {
      contractIdResolver: bodyResolver('contractId')
    })
  )
  @Post('/contract-review-requests')
  async createContractReviewRequest(@Body() body: ContractReviewRequestNewDto): Promise<TaskDto> {
    const reviewRequest: ContractReviewRequestNewArgs = {
      contractId: body.contractId,
      description: body.description ?? '',
      email: {
        content: body.emailContent,
        subject: body.emailSubject
      },
      emailAssignees: body.emailAssignees,
      title: body.title,
      userId: body.userId
    };
    const reviewResponse = await this.contractReviewsService.createContractReviewRequest(reviewRequest);
    const currentTaskReview = reviewResponse.reviewTasks[0];

    return {
      assignees: currentTaskReview.assignees,
      contractId: reviewResponse.contract.id,
      creationTime: currentTaskReview.creationTime,
      creatorEmail: reviewResponse.creator.email,
      creatorFirstname: reviewResponse.creator.firstname,
      creatorLastname: reviewResponse.creator.lastname,
      creatorPhone: reviewResponse.creator.phone,
      creatorProfilePictureFileId: reviewResponse.creator.profilePictureFileId,
      creatorUserId: reviewResponse.creator.id,
      description: currentTaskReview.description,
      id: currentTaskReview.id,
      legalOperationId: currentTaskReview.operationId,
      organizationAddress: reviewResponse.organization.address,
      organizationId: reviewResponse.organization.id,
      organizationName: reviewResponse.organization.name,
      seen: false,
      title: currentTaskReview.title,
      type: convertEnum(TaskTypeDto, currentTaskReview.type)
    };
  }

  @UseGuards(HasContractPermission(PermissionType.CREATE_CONTRACT_TASK_ASK_REVIEW))
  @Delete('/contract-review-requests/:contractId')
  async deleteContractReviewRequest(@Param('contractId') contractId: string): Promise<TaskDeletedDto> {
    return await this.contractReviewsService.deleteContractReviewRequest(contractId);
  }

  @UseGuards(HasContractPermission(PermissionType.VALIDATE_CONTRACT_REVIEW))
  @Post('/contract-reviews')
  async validateContractReview(@Body() body: ContractReviewDto): Promise<void> {
    await this.contractReviewsService.validateContractReview({
      contractId: body.contractId,
      userId: body.userId
    });
  }

  @UseGuards(HasContractPermission(PermissionType.VALIDATE_CONTRACT_REVIEW))
  @Delete('/contract-reviews/:contractId')
  async deleteContractReview(@Param('contractId') contractId: string): Promise<void> {
    await this.contractReviewsService.deleteContractReview(contractId);
  }
}
