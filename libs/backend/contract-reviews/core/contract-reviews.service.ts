import { Injectable } from '@nestjs/common';
import { ContractStatutError, LegalsApiService, Operation, Task } from '@mynotary/backend/legals/api';
import { Contract, ContractStatus, TaskType } from '@mynotary/crossplatform/legals/api';
import { Organization, OrganizationsApiService } from '@mynotary/backend/organizations/api';
import { NotificationApiService, TaskNotificationType } from '@mynotary/backend/notifications/api';
import { EmailNewData, EmailsApiService, EmailTemplateId } from '@mynotary/backend/emails/api';
import { UsersApiService } from '@mynotary/backend/users/api';
import { EnvService } from '@mynotary/backend/secrets/api';
import { User } from '@mynotary/crossplatform/shared/users-core';

@Injectable()
export class ContractReviewsService {
  constructor(
    private legalsApiService: LegalsApiService,
    private organizationsApiService: OrganizationsApiService,
    private notificationApiService: NotificationApiService,
    private emailsApiService: EmailsApiService,
    private usersApiService: UsersApiService,
    private envService: EnvService
  ) {}

  async createContractReviewRequest(args: ContractReviewRequestNewArgs) {
    const { contractId, description, email, emailAssignees, title, userId } = args;

    const contract = await this.legalsApiService.getContract(contractId);
    const assignees = await this.usersApiService.getUsers({ emails: emailAssignees });
    const creator = await this.usersApiService.getUser({ id: userId, type: 'by-id' });
    const organization = await this.organizationsApiService.getOrganization({
      organizationId: contract.organizationId
    });
    const operation = await this.legalsApiService.getOperation(contract.legalOperationId);

    if (![ContractStatus.REDACTION, ContractStatus.VALIDATION_PENDING].includes(contract.status)) {
      throw new ContractStatutError(`Cannot create validation in status ${contract.status}`);
    }

    let contractReview: ContractReviewCreated | null = null;

    try {
      const reviewTask = await this.legalsApiService.createTask({
        assignees: emailAssignees,
        contractId,
        creatorUserId: userId,
        description,
        email: {
          content: email.content,
          subject: email.subject
        },
        legalComponentId: contract.legalOperationId,
        organizationId: contract.organizationId,
        title,
        type: TaskType.REVIEW_CONTRACT
      });

      contractReview = {
        assignees,
        contract,
        creator,
        email: {
          content: email.content,
          subject: email.subject
        },
        operation,
        organization,
        reviewTasks: [reviewTask]
      };

      await this.legalsApiService.updateContract({
        currentContractReviewTaskId: reviewTask.id,
        id: contractId,
        status: ContractStatus.REVIEW_PENDING
      });

      await this.notifyContractReviewRequested(contractReview);
      return contractReview;
    } catch (error) {
      if (contractReview?.reviewTasks[0].id) {
        await this.legalsApiService.deleteTask(contractReview?.reviewTasks[0].id);
      }

      await this.legalsApiService.updateContract({
        currentContractReviewTaskId: null,
        id: contractId,
        status: contractReview?.contract.status
      });

      throw error;
    }
  }

  async deleteContractReviewRequest(contractId: string): Promise<{ deletedTaskIds: string[] }> {
    const contract = await this.legalsApiService.getContract(contractId);

    const allowedStatus = [ContractStatus.VALIDATION_PENDING, ContractStatus.REVIEW_PENDING, ContractStatus.REVIEWED];

    if (!allowedStatus.includes(contract.status as ContractStatus)) {
      throw new ContractStatutError();
    }

    const tasks = await this.legalsApiService.getTasks({
      contractId,
      types: [TaskType.VALIDATE_CONTRACT, TaskType.REVIEW_CONTRACT]
    });

    const reviewTasks = tasks.filter((task) => task.type === TaskType.REVIEW_CONTRACT);
    const validationTasksUncompleted = tasks.filter(
      (task) => task.type === TaskType.VALIDATE_CONTRACT && task.completionDate == null
    );

    const deletedTaskIds: string[] = [];

    for (const task of reviewTasks) {
      await this.legalsApiService.deleteTask(task.id);
      deletedTaskIds.push(task.id);
    }

    if (validationTasksUncompleted.length > 0) {
      await this.legalsApiService.updateContract({
        currentContractReviewTaskId: null,
        id: contractId,
        status: ContractStatus.VALIDATION_PENDING
      });
    } else {
      await this.legalsApiService.updateContract({
        currentContractReviewTaskId: null,
        id: contractId,
        status: ContractStatus.REDACTION
      });
    }

    return { deletedTaskIds };
  }

  async deleteContractReview(contractId: string) {
    const contract = await this.legalsApiService.getContract(contractId);
    const contractTasks = await this.legalsApiService.getTasks({
      contractId,
      types: [TaskType.VALIDATE_CONTRACT, TaskType.REVIEW_CONTRACT]
    });

    const contractReview = {
      contract,
      reviewTasks: contractTasks.filter((task) => task.type === TaskType.REVIEW_CONTRACT),
      validationTasks: contractTasks.filter((task) => task.type === TaskType.VALIDATE_CONTRACT)
    };

    if (contract.status !== ContractStatus.REVIEWED) {
      throw new ContractStatutError();
    }

    const targetStatus = getDeletedReviewTargetStatus(contractReview);

    await this.legalsApiService.updateContract({
      id: contract.id,
      status: targetStatus
    });

    const currentReviewTask = contractReview.reviewTasks.find((task) => task.completionDate != null);

    if (currentReviewTask != null) {
      await this.legalsApiService.updateTask({
        completionTime: null,
        id: currentReviewTask.id
      });
    }
  }

  private async notifyContractReviewRequested(contractReview: ContractReviewCreated): Promise<void> {
    let link = `${this.envService.url.mnAppUrl}/operation/${contractReview.contract.legalOperationId}/contrats/${contractReview.contract.id}`;
    if (contractReview.operation.parentId) {
      link = `${this.envService.url.mnAppUrl}/operation/${contractReview.operation.parentId}/operations/${contractReview.contract.legalOperationId}/contrats/${contractReview.contract.id}`;
    }

    const reviewTaskId = contractReview.reviewTasks[0].id;

    await this.notificationApiService.createNotification({
      contractId: contractReview.contract.id,
      creatorFirstname: contractReview.creator.firstname,
      creatorId: contractReview.creator.id,
      creatorLastname: contractReview.creator.lastname,
      operationId: contractReview.contract.legalOperationId,
      receivers: contractReview.assignees.map((assignee) => assignee.email),
      taskId: reviewTaskId,
      title: 'Demande de révision de contrat',
      type: TaskNotificationType.TASK_REVIEW_CONTRACT_CREATED,
      userIds: contractReview.assignees.map((assignee) => assignee.id)
    });

    const emailData: EmailNewData = {
      appUrl: link,
      contractId: contractReview.contract.id,
      emailContent: contractReview.email.content,
      operationId: contractReview.contract.legalOperationId,
      organizationId: contractReview.contract.organizationId,
      sender: {
        email: contractReview.creator.email,
        firstname: contractReview.creator.firstname,
        lastname: contractReview.creator.lastname,
        organizationName: contractReview.organization.name,
        phone: contractReview.creator.phone
      },
      subject: contractReview.email.subject,
      taskId: reviewTaskId,
      templateId: EmailTemplateId.TASK_REVIEW_CONTRACT
    };

    for (const assignee of contractReview.assignees) {
      await this.emailsApiService.sendEmail({
        data: emailData,
        receiver: assignee.email
      });
    }
  }

  async validateContractReview({ contractId, userId }: ContractReviewArgs): Promise<void> {
    const contract = await this.legalsApiService.getContract(contractId);

    const allowedStatuses = [
      ContractStatus.REDACTION,
      ContractStatus.VALIDATION_PENDING,
      ContractStatus.REVIEW_PENDING
    ];

    if (!allowedStatuses.includes(contract.status)) {
      throw new ContractStatutError(`Cannot validate review in status ${contract.status}`);
    }

    await this.legalsApiService.updateContract({
      id: contractId,
      status: ContractStatus.REVIEWED
    });

    const reviewTasks = await this.legalsApiService.getTasks({
      contractId,
      types: [TaskType.REVIEW_CONTRACT]
    });

    for (const task of reviewTasks) {
      if (task.completionDate == null) {
        await this.legalsApiService.updateTask({
          completionTime: new Date().toISOString(),
          id: task.id
        });

        const user = await this.usersApiService.getUser({ id: userId, type: 'by-id' });

        await this.notificationApiService.createNotification({
          contractId,
          creatorFirstname: user.firstname,
          creatorId: user.id,
          creatorLastname: user.lastname,
          operationId: task.operationId,
          taskId: task.id,
          title: task.title,
          type: TaskNotificationType.TASK_REVIEW_CONTRACT_COMPLETED,
          userIds: [task.creatorId]
        });
      }
    }
  }
}

export interface ContractReviewRequestNewArgs {
  contractId: string;
  description: string;
  email: ReviewEmail;
  emailAssignees: string[];
  title: string;
  userId: string;
}

interface ContractReviewArgs {
  contractId: string;
  userId: string;
}

interface ContractReview {
  contract: Contract;
  reviewTasks: Task[];
  validationTasks: Task[];
}

export interface ContractReviewCreated {
  assignees: User[];
  contract: Contract;
  creator: User;
  email: ReviewEmail;
  operation: Operation;
  organization: Organization;
  reviewTasks: Task[];
}

interface ReviewEmail {
  content: string;
  subject: string;
}

function getDeletedReviewTargetStatus({ reviewTasks, validationTasks }: ContractReview) {
  const hasCompletedTasks = validationTasks.some((task) => task.completionDate != null);

  if (reviewTasks.length > 0) {
    return ContractStatus.REVIEW_PENDING;
  }

  if (hasCompletedTasks) {
    return ContractStatus.VALIDATION_PENDING;
  }

  return ContractStatus.REDACTION;
}
