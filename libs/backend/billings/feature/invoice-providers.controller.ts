import { Controller, Get, Query, UseGuards, Param, StreamableFile } from '@nestjs/common';
import { InvoicesService } from '@mynotary/backend/billings/core';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { InvoiceTypeDto, ProviderInvoicesListDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { convertInvoicesToDto } from './billings.dto-converters';
import { InvoiceType } from '@mynotary/crossplatform/billings/core';
import { convertEnum } from '@mynotary/crossplatform/shared/util';
import { queryResolver } from '@mynotary/backend/shared/auth-util';
import { HasOrganizationPermission } from '@mynotary/backend/authorizations/api';
@Controller()
export class InvoiceProvidersController {
  constructor(private invoicesService: InvoicesService) {}

  @UseGuards(
    HasOrganizationPermission(PermissionType.READ_ORGANIZATION_BILLING, {
      organizationIdResolver: queryResolver('organizationId')
    })
  )
  @Get('/provider-invoices')
  async getInvoices(@Query() query: { organizationId: string }): Promise<ProviderInvoicesListDto> {
    const invoices = await this.invoicesService.getInvoices({ organizationId: query.organizationId });

    return convertInvoicesToDto(invoices);
  }

  @UseGuards(
    HasOrganizationPermission(PermissionType.READ_ORGANIZATION_BILLING, {
      organizationIdResolver: queryResolver('organizationId')
    })
  )
  @Get('/invoice-file/:invoiceId')
  async getInvoice(
    @Param('invoiceId') invoiceId: string,
    @Query() query: { organizationId: string; type: InvoiceTypeDto }
  ): Promise<StreamableFile> {
    const buffer = await this.invoicesService.getInvoiceFile({
      invoiceId,
      organizationId: query.organizationId,
      type: convertEnum(InvoiceType, query.type)
    });

    return new StreamableFile(buffer);
  }
}
