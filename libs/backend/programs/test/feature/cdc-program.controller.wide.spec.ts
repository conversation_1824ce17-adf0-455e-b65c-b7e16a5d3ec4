// noinspection JSNonASCIINames

import { ProgramController } from '@mynotary/backend/programs/feature';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { findCsvLegalProgramConfig } from '@mynotary/crossplatform/csv-legal-records/api';
import { assertNotNull, Dictionary } from '@mynotary/crossplatform/shared/util';
import { ProgramSynthesisUpsertDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { provideProgramsTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';

describe('CDC Controller', () => {
  it('should reject update when the csv does not match the config', async () => {
    const { client, program } = await createProgram();

    const response = await client.post(`/program-synthesis`).send({
      programId: program.id,
      rows: [{ NOT_IN_CONFIG_HEADER: 'NOT_IN_CONFIG_HEADER' }, { NOT_IN_CONFIG_HEADER: 'lot-1' }]
    } satisfies ProgramSynthesisUpsertDto);

    expect(response.status).toBe(400);
  });

  it('should not update the lot when reserved', async () => {
    const { client, createRecordSpy, lot1, program, testingRepos, updateRecordAnswerSpy } = await createProgram();

    await testingRepos.operations.createLotReservation({ programId: program.id, recordId: lot1.id });

    const response = await client.post(`/program-synthesis`).send({
      programId: program.id,
      rows: [
        {
          ...restRowsHeader()
        },
        {
          ...restRows(),
          'N° Lot Gestion - UG': '00UG-1',
          'N° appartement': '2',
          'N° lot EDD': 'lot-1'
        }
      ]
    } satisfies ProgramSynthesisUpsertDto);

    expect(response.status).toBe(201);
    expect(updateRecordAnswerSpy).toHaveBeenCalledTimes(0);
    expect(createRecordSpy).toHaveBeenCalledTimes(0);
    const lot1Update = await testingRepos.records.selectRecord({ recordId: lot1.id });
    expect(lot1Update.answer['programme_numero_porte']?.value).toBe(undefined);
  });

  it('should update the program with the csv data', async () => {
    const { client, createRecordSpy, lot1, program, testingRepos, updateRecordAnswerSpy } = await createProgram();

    const response = await client.post(`/program-synthesis`).send({
      programId: program.id,
      rows: [
        {
          ...restRowsHeader()
        },
        {
          ...restRows(),
          'N° Lot Gestion - UG': '00UG-1',
          'N° appartement': '2',
          'N° lot EDD': 'lot-1',
          'Surf Carrez': '100'
        },
        {
          ...restRows(),
          'N° Lot Gestion - UG': 'UG-3',
          'N° appartement': '3',
          'N° lot EDD': 'lot-3'
        }
      ]
    } satisfies ProgramSynthesisUpsertDto);

    expect(response.status).toBe(201);
    expect(updateRecordAnswerSpy).toHaveBeenCalledTimes(2);
    expect(createRecordSpy).toHaveBeenCalledTimes(0);
    const lot1Update = await testingRepos.records.selectRecord({ recordId: lot1.id });

    expect(lot1Update.answer['programme_numero_porte']?.value).toBe('2');
    expect(lot1Update.answer['mesurage_carrez_superficie']?.value).toBe(100);
    expect(lot1Update.answer['mesurage_carrez_statut']?.value).toBe('oui');
  });

  it('should update the program with the lot annexe', async () => {
    const { client, createRecordSpy, lot1, lot2, program, testingRepos } = await createProgram();

    /* eslint-disable sort-keys-fix/sort-keys-fix */
    const response = await client.post(`/program-synthesis`).send({
      programId: program.id,
      rows: [
        { ...restRowsHeader() },
        {
          ...restRows(),
          'N° Lot Gestion - UG': '00UG-1',
          'N° lot EDD': 'lot-1',
          'N° appartement': '1',
          'N° Lot Gestion - UG Pkg 1': 'UG-PKG-1',
          'N° lot PKG EDD 1': 'lot-parking-1',
          'N° Pkg 1': '1',
          'Désignation pkg 1': 'Parking appartenant au lot principal 1',
          'Adresse': '49 rue de la paix',
          'CP': '69002',
          'Ville': 'Lyon'
        },
        {
          ...restRows(),
          'N° Lot Gestion - UG': 'UG-3',
          'N° lot EDD': 'lot-3',
          'N° appartement': '3',
          'N° Lot Gestion - UG Pkg 1': '',
          'N° lot PKG EDD 1': '',
          'N° Pkg 1': '',
          'Désignation pkg 1': '',
          'Adresse': '',
          'CP': '',
          'Ville': ''
        }
      ]
    } satisfies ProgramSynthesisUpsertDto);

    const parkingLotUpdate = await testingRepos.records.selectRecord({ recordId: lot2.id });
    const lot1Update = await testingRepos.records.selectRecord({ recordId: lot1.id });

    expect(response.status).toBe(201);
    expect(lot1Update.answer['adresse']?.value).toStrictEqual({
      address: '49 rue de la paix',
      formattedAddress: '49 rue de la paix, Lyon (69002), France',
      zip: '69002',
      country: 'France',
      city: 'Lyon'
    });
    expect(lot1Update.answer['nature_bien']?.value).toBe('nature_bien_appartement');

    expect(createRecordSpy).toHaveBeenCalledTimes(0);
    expect(parkingLotUpdate.answer['designation']?.value).toBe('Parking appartenant au lot principal 1');
  });

  it('should not update if modifying an annexe identifier', async () => {
    const { client, program } = await createProgram();

    const response = await client.post(`/program-synthesis`).send({
      programId: program.id,
      rows: [
        { ...restRowsHeader() },
        {
          ...restRows(),
          'N° Lot Gestion - UG': '00UG-1',
          'N° appartement': '2',
          'N° lot EDD': 'lot-1',
          'N° Lot Gestion - UG Pkg 1': 'UG-MODIFIED-PKG-1'
        }
      ]
    } satisfies ProgramSynthesisUpsertDto);

    expect(response.status).toBe(400);
  });

  it('should throw if missing columns', async () => {
    const { client, program } = await createProgram();

    const response = await client.post(`/program-synthesis`).send({
      programId: program.id,
      rows: [
        {
          'N° Lot Gestion - UG': 'N° Lot Gestion - UG',
          'N° lot EDD': 'N° lot EDD',
          'N° appartement': 'N° appartement'
        },
        {
          'N° Lot Gestion - UG': '00UG-1',
          'N° lot EDD': 'lot-1',
          'N° appartement': '2'
        },
        {
          'N° Lot Gestion - UG': 'UG-3',
          'N° lot EDD': 'lot-3',
          'N° appartement': '3'
        }
      ]
    } satisfies ProgramSynthesisUpsertDto);

    expect(response.status).toBe(400);
  });

  it('should throw when duplicate unique identifier', async () => {
    const { client, program } = await createProgram();

    const response = await client.post(`/program-synthesis`).send({
      programId: program.id,
      rows: [
        {
          'N° Lot Gestion - UG': 'N° Lot Gestion - UG',
          'N° lot EDD': 'N° lot EDD',
          'N° appartement': 'N° appartement'
        },
        {
          'N° Lot Gestion - UG': '00UG-1',
          'N° lot EDD': 'lot-1',
          'N° appartement': '2'
        },
        {
          'N° Lot Gestion - UG': 'UG-3',
          'N° lot EDD': 'lot-3',
          'N° appartement': '3'
        },
        {
          'N° Lot Gestion - UG': 'UG-3',
          'N° lot EDD': 'lot-3',
          'N° appartement': '3'
        }
      ]
    } satisfies ProgramSynthesisUpsertDto);

    expect(response.status).toBe(400);
  });

  it('should update the price of all caves', async () => {
    const { cave1, cave2, client, createRecordSpy, program, testingRepos } = await createProgramWithCaves();

    const response = await client.post(`/program-synthesis`).send({
      programId: program.id,
      rows: [
        { ...restRowsHeader() },
        {
          ...restRows(),
          'N° Lot Gestion - UG': '00UG-1',
          'N° lot EDD': 'lot-1',
          'N° appartement': '2',
          'Prix de mise en vente CAVE': '20000',
          'N°UG Cave 1': 'UG-CAVE-1',
          'N° lot EDD Cave 1': 'cave-1',
          'N° Cave 1': '1',
          'N°UG Cave 2': 'UG-CAVE-2',
          'N° lot EDD Cave 2': 'cave-2',
          'N° Cave 2': '2'
        }
      ]
    } satisfies ProgramSynthesisUpsertDto);

    expect(response.status).toBe(201);

    const cave1Update = await testingRepos.records.selectRecord({ recordId: cave1.id });
    const cave2Update = await testingRepos.records.selectRecord({ recordId: cave2.id });

    expect(cave1Update.answer['programme_prix_vente_libre']?.value).toBe('20000');
    expect(cave2Update.answer['programme_prix_vente_libre']?.value).toBe('20000');
    expect(cave1Update.answer['nature_bien']?.value).toBe('nature_bien_cave');
    expect(cave2Update.answer['nature_bien']?.value).toBe('nature_bien_cave');
    expect(createRecordSpy).toHaveBeenCalledTimes(0);
  });

  async function createProgramWithCaves() {
    const { client, testingRepos, ...rest } = await setup();

    const member = await testingRepos.createMember({});

    const program = await testingRepos.operations.createOperation({
      templateId: 'OPERATION__CDC__IMMOBILIER__PROGRAMME',
      organizationId: member.organizationId,
      userId: member.userId
    });

    const lot1 = await createLotInProgram({
      answer: {
        customCsvConfig: {
          value: {
            externalId: '00UG-1'
          }
        },
        numero_lot: {
          value: 'lot-1'
        },
        numero_commercialisation_lot: {
          value: '00UG-1'
        },
        programme_prix_vente_libre: {
          value: 10000
        }
      },
      member,
      programId: program.id,
      testingRepos
    });

    const cave1 = await createLotInProgram({
      answer: {
        customCsvConfig: {
          value: {
            externalId: '00UG-1-cave-1-UG-CAVE-1'
          }
        },
        numero_lot: {
          value: 'cave-1'
        },
        numero_commercialisation_lot: {
          value: 'UG-CAVE-1'
        },
        programme_prix_vente_libre: {
          value: 10000
        }
      },
      member,
      programId: program.id,
      testingRepos
    });

    const cave2 = await createLotInProgram({
      answer: {
        customCsvConfig: {
          value: {
            externalId: '00UG-1-cave-2-UG-CAVE-2'
          }
        },
        numero_lot: {
          value: 'cave-2'
        },
        numero_commercialisation_lot: {
          value: 'UG-CAVE-2'
        },
        programme_prix_vente_libre: {
          value: 10000
        }
      },
      member,
      programId: program.id,
      testingRepos
    });

    return {
      client,
      cave1,
      cave2,
      lot1,
      member,
      program,
      testingRepos,
      ...rest
    };
  }

  async function createProgram() {
    const { client, testingRepos, ...rest } = await setup();

    const member = await testingRepos.createMember({});

    const program = await testingRepos.operations.createOperation({
      templateId: 'OPERATION__CDC__IMMOBILIER__PROGRAMME',
      organizationId: member.organizationId,
      userId: member.userId
    });

    const lot1 = await createLotInProgram({
      answer: {
        customCsvConfig: {
          value: {
            externalId: '00UG-1'
          }
        },
        numero_lot: {
          value: 'lot-1'
        },
        numero_commercialisation_lot: {
          value: '00UG-1'
        }
      },
      member,
      programId: program.id,
      testingRepos
    });

    const lot2 = await createLotInProgram({
      answer: {
        customCsvConfig: {
          value: {
            externalId: '00UG-1-parking-1-UG-PKG-1'
          }
        },
        numero_lot: {
          value: 'lot-parking-1'
        },
        numero_commercialisation_lot: {
          value: 'UG-PKG-1'
        }
      },
      member,
      programId: program.id,
      testingRepos
    });

    const lot3 = await createLotInProgram({
      answer: {
        customCsvConfig: {
          value: {
            externalId: '00UG-3'
          }
        },
        numero_lot: {
          value: 'lot-3'
        },
        numero_commercialisation_lot: {
          value: '00UG-3'
        }
      },
      member,
      programId: program.id,
      testingRepos
    });

    return {
      client,
      lot1,
      lot2,
      lot3,
      member,
      program,
      testingRepos,
      ...rest
    };
  }

  async function createLotInProgram({
    answer,
    member,
    programId,
    testingRepos
  }: {
    answer: AnswerDict;
    member: { organizationId: string; userId: string };
    programId: string;
    testingRepos: TestingRepositories;
  }) {
    const lot = await testingRepos.records.createRecord({
      answer,
      organizationId: member.organizationId,
      templateId: 'RECORD__BIEN__LOT_HABITATION',
      userId: member.userId
    });

    await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: programId,
      organizationId: member.organizationId,
      toId: lot.id,
      legalLinkTemplateId: 'LINK__OPERATION__CDC_HABITAT__IMMOBILIER__PROGRAMME__LOTS',
      type: 'BIEN_VENDU',
      userId: member.userId
    });

    return lot;
  }

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: ProgramController,
      providers: provideProgramsTest()
    });

    const testingRepos = getService(TestingRepositories);
    const recordsService = getService(LegalsApiService);

    const updateRecordAnswerSpy = jest.spyOn(recordsService, 'updateLegalRecordAnswer');
    const createRecordSpy = jest.spyOn(recordsService, 'createRecord');

    return {
      client,
      testingRepos,
      updateRecordAnswerSpy,
      createRecordSpy
    };
  }
});

export const getHeaderColumnIds = () => {
  const csvLegalProgramConfig = findCsvLegalProgramConfig('OPERATION__CDC__IMMOBILIER__PROGRAMME');

  assertNotNull(csvLegalProgramConfig, 'Csv config not found');

  const headerColumnIds: string[] = csvLegalProgramConfig.columns.flatMap((column) => {
    if (column.type === 'LOT_ANNEXE') {
      return column.children.map((childColumnConfig) => childColumnConfig.columnId);
    }
    if (column.type === 'ADDRESS_GROUP') {
      return column.children.map((childColumnConfig) => childColumnConfig.columnId);
    }
    return column.columnId;
  });

  return headerColumnIds;
};

export const restRowsHeader = () => {
  const headerColumnIds = getHeaderColumnIds();

  const header: Dictionary<string> = {};

  headerColumnIds.forEach((columnId) => {
    header[columnId] = columnId;
  });

  return header;
};

export const restRows = () => {
  const headerColumnIds = getHeaderColumnIds();

  const defaultRows: Dictionary<string> = {};

  headerColumnIds.forEach((columnId) => {
    defaultRows[columnId] = '';
  });

  return defaultRows;
};
