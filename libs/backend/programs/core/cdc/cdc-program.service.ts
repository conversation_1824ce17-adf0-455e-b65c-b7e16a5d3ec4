import { Injectable } from '@nestjs/common';
import { ProgramsRepository } from '../programs.repository';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { findCsvLegalProgramConfig } from '@mynotary/crossplatform/csv-legal-records/api';
import { convertCdcCsvRowToProgramLotUpdates } from './convert-cdc-csv-row-to-program-lot-updates';
import { LegalRecord } from '@mynotary/crossplatform/records/api';
import { filter, find, forEach, some } from 'lodash';
import { ReservationStatus } from '@mynotary/crossplatform/programs/core';
import { InvalidProgramCsvError } from './cdc-programs.errors';
import { getCdcCsvErrors } from './get-cdc-csv-errors';
import { CsvRow } from './cdc';
import { getLegalOperationTemplate } from '@mynotary/crossplatform/legal-operation-templates/api';
import { isProgramLotUpdate } from '../programs';
import { LegalData } from '@mynotary/crossplatform/legals/api';
import { LegalsApiService } from '@mynotary/backend/legals/api';

@Injectable()
export class CdcProgramService {
  constructor(
    private programsRepository: ProgramsRepository,
    private legalsApiService: LegalsApiService
  ) {}

  async upsertProgramSynthesis({ programId, rows }: { programId: string; rows: CsvRow[] }): Promise<void> {
    const program = await this.legalsApiService.getLegal(programId);
    const legalOperationTemplate = getLegalOperationTemplate(program.templateId);

    const legalData = await this.legalsApiService.getLegalData({ legalId: programId });
    const lotReservations = await this.programsRepository.getLotReservations(programId);

    const lots: LegalRecord[] = this.getBienVenduLegalRecords(legalData);
    const linkId = this.getBienVenduLinkId(legalData);

    const config = findCsvLegalProgramConfig(legalOperationTemplate.id);
    assertNotNull(config, `Config not found for program template ${program.templateId}`);

    const errors = getCdcCsvErrors({ config, lots, rows });

    if (errors.length > 0) {
      throw new InvalidProgramCsvError({ errors, programId });
    }

    const lotsUpsert = rows.slice(1).flatMap((row) => convertCdcCsvRowToProgramLotUpdates({ config, lots: lots, row }));

    for (const lotUpsert of lotsUpsert) {
      if (isProgramLotUpdate(lotUpsert)) {
        const hasReservation = some(
          lotReservations,
          (reservation) =>
            reservation.legalRecordId === lotUpsert.id && reservation.reservationStatus === ReservationStatus.RESERVED
        );

        if (!hasReservation) {
          const existing = lots.find((lot) => lot.id === lotUpsert.id);
          assertNotNull(existing, `Lot ${lotUpsert.id} not found`);
          await this.legalsApiService.updateLegalRecordAnswer({
            answer: lotUpsert.answer,
            legalRecordId: lotUpsert.id
          });
        }
      } else {
        const recordCreated = await this.legalsApiService.createRecord({
          ...lotUpsert,
          answer: lotUpsert.answer,
          creatorId: program.creatorId,
          organizationId: program.organizationId,
          templateId: lotUpsert.legalRecordTemplateId
        });

        const branch = {
          fromLegalId: programId,
          legalLinkId: linkId,
          reverseType: 'BIEN_VENDU',
          toLegalId: recordCreated.id,
          type: 'BIEN_VENDU'
        };

        await this.legalsApiService.createLegalBranch(branch);
      }
    }
  }

  private getBienVenduLegalRecords(legalData: LegalData): LegalRecord[] {
    const bienVenduBranches = filter(legalData.legalBranches, (legalBranch) => legalBranch.type === 'BIEN_VENDU');
    const lots: LegalRecord[] = [];

    forEach(bienVenduBranches, (branch) => {
      const targetRecord = find(legalData.legalRecords, (record) => record.id === branch.toLegalId);

      if (targetRecord) {
        lots.push(targetRecord);
      }
    });
    return lots;
  }

  private getBienVenduLinkId(legalData: LegalData) {
    const linkId = find(legalData.legalBranches, (legalBranch) => legalBranch.type === 'BIEN_VENDU')?.legalLinkId;
    assertNotNull(linkId, `Link not found for BIEN_VENDU branch`);
    return linkId;
  }
}
