import { Injectable } from '@nestjs/common';
import { FeatureOrganizationsApiService } from '@mynotary/backend/feature-organizations/api';
import { Exception } from '@mynotary/crossplatform/shared/util';
import {
  ContractValidatorsRepository,
  CreateContractValidatorsFeatureArgs,
  UpdateContractValidatorsArgs
} from './contract-validators.repository';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { ContractValidators } from './contract-validators';
import { FeatureType } from '@mynotary/crossplatform/features/api';

@Injectable()
export class ContractValidatorsService {
  constructor(
    private featureOrganizationsApiService: FeatureOrganizationsApiService,
    private contractValidatorsRepository: ContractValidatorsRepository,
    private legalsApiService: LegalsApiService
  ) {}

  async findContractValidators(args: GetContractValidatorsArgs): Promise<ContractValidators | null> {
    try {
      return await this.getContractValidator(args);
    } catch {
      return null;
    }
  }

  async getContractValidator(args: GetContractValidatorsArgs): Promise<ContractValidators> {
    let organizationId = args.organizationId;
    if (args.organizationId == null && args.operationId != null) {
      const operation = await this.legalsApiService.getOperation(args.operationId);
      organizationId = operation.organizationId;
    }

    if (organizationId == null) {
      throw new Exception(`organizationId is null`, { cause: args });
    }

    const feature = await this.featureOrganizationsApiService.getFeature({
      featureType: FeatureType.VALIDATION_CUSTOMIZATION,
      organizationId: organizationId
    });

    const validators = await this.contractValidatorsRepository.getContractValidators(feature.id);
    return {
      id: validators.id,
      locked: validators.locked,
      users: validators.users
    };
  }

  async updateContractValidators(args: UpdateContractValidatorsArgs): Promise<void> {
    await this.contractValidatorsRepository.updateContractValidators(args);
  }

  async createContractValidatorsFeature(args: CreateContractValidatorsFeatureArgs): Promise<void> {
    await this.contractValidatorsRepository.createContractValidatorsFeature(args);
  }
}

export interface GetContractValidatorsArgs {
  operationId?: string;
  organizationId?: string;
}
