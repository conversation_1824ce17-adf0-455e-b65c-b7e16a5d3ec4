import { Injectable } from '@nestjs/common';
import {
  ContractValidators,
  ContractValidatorsService,
  CreateContractValidatorsFeatureArgs,
  GetContractValidatorsArgs
} from '@mynotary/backend/contract-validators/core';

@Injectable()
export class ContractValidatorsApiService {
  constructor(private contractValidatorsService: ContractValidatorsService) {}

  async createContractValidatorsFeature(args: CreateContractValidatorsFeatureArgs): Promise<void> {
    await this.contractValidatorsService.createContractValidatorsFeature(args);
  }

  async findContractValidators(args: GetContractValidatorsArgs): Promise<ContractValidators | null> {
    return await this.contractValidatorsService.findContractValidators(args);
  }
}
