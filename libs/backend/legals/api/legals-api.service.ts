import { Injectable } from '@nestjs/common';
import {
  ContractsService,
  CreateLegalBranchArgs,
  GetLegalDataArgs,
  GetTaskArgs,
  HasLegalRecordAccessArgs,
  LegalBranchesService,
  LegalLinkNew,
  LegalLinksService,
  LegalOperationCreationsService,
  LegalOperationNew,
  LegalRecordAuthorizationsRepository,
  LegalRecordsService,
  LegalsService,
  OperationsService,
  UpdateLegalRecordAnswerArgs,
  RecordNew,
  Task,
  TaskNew,
  TasksService,
  UpdateContractArgs,
  UpdateTaskArgs
} from '@mynotary/backend/legals/core';
import { LegalRecord } from '@mynotary/crossplatform/records/api';

@Injectable()
export class LegalsApiService {
  constructor(
    private legalsService: LegalsService,
    private operationsService: OperationsService,
    private legalRecordsService: LegalRecordsService,
    private authLegalRecordsRepository: LegalRecordAuthorizationsRepository,
    private legalBranchesService: LegalBranchesService,
    private tasksService: TasksService,
    private legalLinksService: LegalLinksService,
    private legalOperationCreationsService: LegalOperationCreationsService,
    private contractsService: ContractsService
  ) {}

  // Legals
  async getLegal(id: string) {
    return this.legalsService.getLegal(id);
  }

  // Legal Operation
  async getOperation(operationId: string) {
    return await this.operationsService.getOperation(operationId);
  }

  async getOperationLabel(operationId: string): Promise<string> {
    return await this.operationsService.getOperationLabel(operationId);
  }

  async createLegalOperationWithDefaultValues(args: LegalOperationNew): Promise<{ id: string }> {
    return await this.legalOperationCreationsService.createLegalOperationWithDefaultValues(args);
  }

  // Legal Records
  async createRecord(recordNew: RecordNew): Promise<LegalRecord> {
    return await this.legalRecordsService.createRecord(recordNew);
  }

  async getLegalRecord(recordId: string): Promise<LegalRecord> {
    return await this.legalRecordsService.getLegalRecord(recordId);
  }

  async updateLegalRecordAnswer(args: UpdateLegalRecordAnswerArgs): Promise<void> {
    await this.legalRecordsService.updateLegalRecordAnswer(args);
  }

  async hasLegalRecordAccess(args: HasLegalRecordAccessArgs): Promise<boolean> {
    return await this.authLegalRecordsRepository.hasLegalRecordAccess(args);
  }

  // Legal Branches
  async getLegalData(args: GetLegalDataArgs) {
    return this.legalsService.getLegalData(args);
  }

  async createLegalBranch(args: CreateLegalBranchArgs) {
    return this.legalBranchesService.createLegalBranch(args);
  }

  // Tasks
  async createTask(taskNew: TaskNew): Promise<Task> {
    return await this.tasksService.createTask(taskNew);
  }

  async deleteTask(taskId: string): Promise<void> {
    return await this.tasksService.deleteTask(taskId);
  }

  async getTasks(args: GetTaskArgs): Promise<Task[]> {
    return await this.tasksService.getTasks(args);
  }

  async updateTask(args: UpdateTaskArgs): Promise<void> {
    return await this.tasksService.updateTask(args);
  }

  // Legal Links
  async createLegalLink(args: LegalLinkNew) {
    return this.legalLinksService.createLegalLink(args);
  }

  // Contracts
  async getContract(contractId: string) {
    return await this.contractsService.getContractById(contractId);
  }

  async getContracts(operationId: string) {
    return await this.contractsService.getContracts({ operationId });
  }

  async updateContract(args: UpdateContractArgs) {
    return await this.contractsService.updateContract(args);
  }
}
