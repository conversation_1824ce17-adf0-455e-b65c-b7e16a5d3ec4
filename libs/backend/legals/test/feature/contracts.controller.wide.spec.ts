import {
  ContractArchiveDto,
  ContractDto,
  ContractNewDto,
  ContractResponseDto,
  ContractStatusDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { ContractController } from '@mynotary/backend/legals/feature';
import { provideLegalsTest } from '../index';
import { LegalOperationTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { find } from 'lodash';
import { LegalData } from '@mynotary/crossplatform/legals/core';
import { LegalLink } from '@mynotary/crossplatform/legal-links/api';
import { LegalRecord } from '@mynotary/crossplatform/records/api';
import { LegalBranch } from '@mynotary/crossplatform/legal-branches/api';
import { EventsApiService } from '@mynotary/backend/events/api';
import { ContractsService, LegalOperationCreationsService, LegalRecordsService } from '@mynotary/backend/legals/core';

describe(ContractController.name, () => {
  it('should archive contract', async () => {
    const { client, contract, testingRepos } = await setup();

    const response = await client
      .put(`/contracts/${contract.id}/archive`)
      .send({ archived: true } satisfies ContractArchiveDto);
    const updatedContract = await testingRepos.contracts.getContract(contract.id);

    expect(response.statusCode).toBe(200);
    expect(updatedContract.archiveTime).toEqual(expect.any(Date));
  });

  it('should unarchive contract', async () => {
    const { client, contract, testingRepos } = await setup();

    const response = await client.put(`/contracts/${contract.id}/archive`).send({ archived: false });
    const updatedContract = await testingRepos.contracts.getContract(contract.id);

    expect(response.statusCode).toBe(200);
    expect(updatedContract.archiveTime).toBeNull();
  });

  it('should create a simple contract and verify status is REDACTION', async () => {
    const { client, member, operation } = await setup();

    const contractData = {
      label: 'Test contract',
      legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
      legalOperationId: operation.id,
      userId: member.userId
    };

    const response = await client.post('/contracts').send(contractData);
    const contractResponseDto: ContractResponseDto = response.body;

    expect(response.statusCode).toBe(201);

    expect(contractResponseDto.contract).toEqual({
      archiveTime: null,
      contractFileId: null,
      creationTime: expect.any(String),
      creatorId: member.userId,
      id: expect.any(String),
      label: 'Test contract',
      legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
      legalOperationId: operation.id,
      legalOperationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
      legalOperationlabel: 'Dossier test creation contrat',
      organizationId: member.organizationId,
      status: ContractStatusDto.REDACTION
    } satisfies ContractResponseDto['contract']);
  });

  it('should create an imported contract and verify status is DRAFT', async () => {
    const { client, member, operation } = await setup();

    const contractData = {
      label: 'imported contract',
      legalContractTemplateId: 'IMPORT',
      legalOperationId: operation.id,
      userId: member.userId
    } satisfies ContractNewDto;

    const response = await client.post('/contracts').send(contractData);
    const contractDto: ContractDto = response.body.contract;

    expect(response.statusCode).toBe(201);
    expect(contractDto.status).toBe(ContractStatusDto.DRAFT);
  });

  it('should create a contract with a specific branch and verify legal data', async () => {
    const { client, legalOperationCreationService, member } = await setup();
    const legalOperationTemplateId: LegalOperationTemplateId = 'OPERATION__IMMOBILIER__VENTE_ANCIEN';

    const operation = await legalOperationCreationService.createLegalOperationWithDefaultValues({
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    });

    const contractData = {
      label: 'Test contract',
      legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
      legalOperationId: operation.id,
      userId: member.userId
    } satisfies ContractNewDto;

    const response = await client.post('/contracts').send(contractData);

    expect(response.statusCode).toBe(201);
    const contractLegalData: LegalData = response.body.legalData;

    expect(contractLegalData.legalBranches).toHaveLength(1);
    expect(contractLegalData.legalRecords).toHaveLength(1);
    expect(contractLegalData.legalLinks).toHaveLength(1);

    const legalBranch = contractLegalData.legalBranches[0];
    const legalRecord = contractLegalData.legalRecords[0];
    const legalLink = contractLegalData.legalLinks[0];

    expect(legalBranch).toEqual({
      contractId: response.body.contract.id,
      fromLegalId: operation.id,
      fromLegalTemplateId: legalOperationTemplateId,
      id: expect.any(String),
      legalLinkId: legalLink.id,
      legalLinkTemplateId: legalLink.templateId,
      reverseType: 'FICHE_OFFRE',
      toLegalId: legalRecord.id,
      toLegalTemplateId: legalRecord.templateId,
      type: 'FICHE_OFFRE'
    } satisfies LegalBranch);

    expect(legalRecord).toEqual({
      answer: {},
      answerId: expect.any(String),
      createdAt: expect.any(String),
      creator: { firstname: 'Foo', id: member.userId, lastname: 'BAR' },
      id: expect.any(String),
      organizationId: member.organizationId,
      templateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT'
    } satisfies LegalRecord);

    expect(legalLink).toEqual({
      creatorId: member.userId,
      id: expect.any(String),
      legalBranchIds: [legalBranch.id],
      organizationId: member.organizationId,
      templateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRE'
    } satisfies LegalLink);
  });

  it('should create a contract with a specific branch and default answer', async () => {
    const { client, contractsService, legalOperationCreationService, legalRecordsService, member, testingRepos } =
      await setup();
    const legalOperationTemplateId: LegalOperationTemplateId = 'OPERATION__IMMOBILIER__VENTE_ANCIEN';

    const operationReference = await legalOperationCreationService.createLegalOperationWithDefaultValues({
      isOperationReference: true,
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    });

    const contractReference = await contractsService.createContract({
      defaultAnswers: [],
      label: 'Test contract',
      legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
      legalOperationId: operationReference.id,
      organizationId: member.organizationId,
      userId: member.userId
    });

    const referenceLegalBranches = await testingRepos.legalBranches.getLegalBranches({
      fromLegalId: operationReference.id
    });
    const targetReferenceBranch = referenceLegalBranches.find(
      (branch) => branch.type === 'FICHE_OFFRE' && branch.specificContractId === contractReference.contract.id
    );
    assertNotNull(targetReferenceBranch?.toLegalId, 'Branch FICHE_OFFRE not found in operation reference');

    await legalRecordsService.updateLegalRecordAnswer({
      answer: {
        offre_developpee: { locked: true, value: 'oui' }
      },
      legalRecordId: targetReferenceBranch.toLegalId
    });

    const operation = await legalOperationCreationService.createLegalOperationWithDefaultValues({
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    });

    const contractData = {
      label: 'Test contract',
      legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
      legalOperationId: operation.id,
      userId: member.userId
    } satisfies ContractNewDto;

    const response = await client.post('/contracts').send(contractData);
    const contractDto: ContractDto = response.body.contract;

    const legalBranches = await testingRepos.legalBranches.getLegalBranches({
      fromLegalId: operation.id
    });

    const ficheOffre = find(
      legalBranches,
      (branch) => branch.type === 'FICHE_OFFRE' && branch.specificContractId === contractDto.id
    );
    assertNotNull(ficheOffre);

    expect(ficheOffre.toLegalRecordAnswer).toEqual({
      offre_developpee: { locked: true, value: 'oui' }
    });
  });

  it('should create an event if a contract is created', async () => {
    const { client, eventApiService, member, operation } = await setup();

    const eventApiServiceSpy = jest.spyOn(eventApiService, 'createEvents');

    const contractData = {
      label: 'Test contract',
      legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE',
      legalOperationId: operation.id,
      userId: member.userId
    };

    const response = await client.post('/contracts').send(contractData);
    const contractDto: ContractDto = response.body.contract;
    expect(eventApiServiceSpy).toHaveBeenCalledTimes(1);
    expect(eventApiServiceSpy).toHaveBeenCalledWith({
      data: {
        contractId: contractDto.id,
        operationId: operation.id,
        organizationId: member.organizationId,
        userId: member.userId
      },
      eventType: 'CONTRACT_CREATED',
      organizationId: member.organizationId
    });
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: ContractController,
      providers: provideLegalsTest()
    });

    const testingRepos = getService(TestingRepositories);
    const eventApiService = getService(EventsApiService);
    const legalRecordsService = getService(LegalRecordsService);
    const legalOperationCreationService = getService(LegalOperationCreationsService);
    const contractsService = getService(ContractsService);

    await testingRepos.templates.createLegalOperationRequiredTemplates('OPERATION__IMMOBILIER__VENTE_ANCIEN');
    await testingRepos.templates.createTemplate('LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS');
    await testingRepos.templates.createTemplate('RECORD__PERSONNE__PHYSIQUE');

    const member = await testingRepos.createMember({});

    const contract = await testingRepos.contracts.createMandat({
      userId: member.userId
    });

    const operation = await testingRepos.operations.createVenteAncien({
      labelOperation: 'Dossier test creation contrat',
      organizationId: member.organizationId,
      userId: member.userId
    });

    return {
      client,
      contract,
      contractsService,
      eventApiService,
      legalOperationCreationService,
      legalRecordsService,
      member,
      operation,
      testingRepos
    };
  }
});
