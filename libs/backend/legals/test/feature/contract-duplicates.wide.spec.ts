import {
  AnnexedDocumentStateDto,
  ContractResponseDto,
  ContractStatusDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { ContractController } from '@mynotary/backend/legals/feature';
import { provideLegalsTest } from '../index';
import { LegalContractTemplateId, LegalOperationTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { ContractStatus } from '@mynotary/crossplatform/legals/core';
import {
  EditedClausesService,
  ContractsService,
  LegalBranchesService,
  LegalOperationCreationsService,
  LegalRecordsService,
  LegalsService
} from '@mynotary/backend/legals/core';
import { LegalBranch } from '@mynotary/crossplatform/legal-branches/api';
import { LegalRecord } from '@mynotary/crossplatform/records/api';
import { LegalLink } from '@mynotary/crossplatform/legal-links/api';
import { FileInfo, FileStatus } from '@mynotary/crossplatform/files-client/api';

describe(`${ContractController.name} - duplication`, () => {
  it('should duplicate a contract with specific legal data', async () => {
    const { client, contract, contractLegalData, member } = await setupWithOffreAchat();

    const response = await client.post('/contract-duplicates').send({ contractId: contract.id, userId: member.userId });
    expect(response.statusCode).toBe(201);

    expect(response.body.contract.status).toBe(ContractStatusDto.REDACTION);

    const duplicatedContract: ContractResponseDto['contract'] = response.body.contract;
    const duplicatedLegalData: ContractResponseDto['legalData'] = response.body.legalData;

    const offrantLegalRecordId = contractLegalData.legalBranches.find((branch) => branch.type === 'OFFRANT')?.toLegalId;
    assertNotNull(offrantLegalRecordId);

    expect(duplicatedContract).toEqual({
      archiveTime: null,
      contractFileId: null,
      creationTime: expect.any(String),
      creatorId: member.userId,
      id: expect.any(String),
      label: 'Copie de Test contract',
      legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
      legalOperationId: contract.organizationId,
      legalOperationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
      legalOperationlabel: 'Test operation',
      organizationId: contract.organizationId,
      status: ContractStatusDto.REDACTION
    } satisfies ContractResponseDto['contract']);

    expect(duplicatedLegalData?.legalBranches).toHaveLength(contractLegalData.legalBranches.length);
    expect(duplicatedLegalData?.legalRecords).toHaveLength(contractLegalData.legalRecords.length);
    expect(duplicatedLegalData?.legalLinks).toHaveLength(contractLegalData.legalLinks.length);

    expect(duplicatedLegalData?.legalBranches).toEqual(
      expect.arrayContaining([
        {
          contractId: duplicatedContract.id,
          fromLegalId: duplicatedContract.legalOperationId,
          fromLegalTemplateId: duplicatedContract.legalOperationTemplateId as LegalOperationTemplateId,
          id: expect.any(String),
          legalLinkId: expect.any(String),
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRE',
          reverseType: 'FICHE_OFFRE',
          toLegalId: expect.any(String),
          toLegalTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT',
          type: 'FICHE_OFFRE'
        },
        {
          contractId: duplicatedContract.id,
          fromLegalId: duplicatedContract.legalOperationId,
          fromLegalTemplateId: duplicatedContract.legalOperationTemplateId as LegalOperationTemplateId,
          id: expect.any(String),
          legalLinkId: expect.any(String),
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS',
          reverseType: 'OFFRANT',
          toLegalId: offrantLegalRecordId,
          toLegalTemplateId: 'RECORD__PERSONNE__PHYSIQUE',
          type: 'OFFRANT'
        }
      ] satisfies LegalBranch[])
    );

    expect(duplicatedLegalData?.legalRecords).toEqual(
      expect.arrayContaining([
        {
          answer: {
            offre_developpee: { locked: true, value: 'oui' }
          },
          answerId: expect.any(String),
          createdAt: expect.any(String),
          creator: { firstname: 'Foo', id: member.userId, lastname: 'BAR' },
          id: expect.any(String),
          organizationId: member.organizationId,
          templateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT'
        },
        {
          answer: {
            email: { locked: false, value: '<EMAIL>' },
            nom: { locked: false, value: 'DOE' },
            prenoms: { locked: false, value: 'John' }
          },
          answerId: expect.any(String),
          createdAt: expect.any(String),
          creator: { firstname: 'Foo', id: member.userId, lastname: 'BAR' },
          id: offrantLegalRecordId,
          organizationId: member.organizationId,
          templateId: 'RECORD__PERSONNE__PHYSIQUE'
        }
      ] satisfies LegalRecord[])
    );

    expect(duplicatedLegalData?.legalLinks).toEqual(
      expect.arrayContaining([
        {
          creatorId: member.userId,
          id: expect.any(String),
          legalBranchIds: [expect.any(String)],
          organizationId: member.organizationId,
          templateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS'
        },
        {
          creatorId: member.userId,
          id: expect.any(String),
          legalBranchIds: [expect.any(String)],
          organizationId: member.organizationId,
          templateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRE'
        }
      ] satisfies LegalLink[])
    );
  });

  it('should duplicate a contract with clauses', async () => {
    const { client, contract, getService, member } = await setupWithOffreAchat();

    const editedClausesService = getService(EditedClausesService);

    await editedClausesService.createEditedClauses([
      {
        clauseId: 'fake-clause-id',
        content: [{ children: [{ text: 'Hello' }], type: 'p' }],
        contractId: contract.id,
        userId: member.userId
      }
    ]);

    const response = await client.post('/contract-duplicates').send({ contractId: contract.id, userId: member.userId });
    expect(response.statusCode).toBe(201);

    const duplicatedContract: ContractResponseDto['contract'] = response.body.contract;

    const clauses = await editedClausesService.getEditedClauses({ contractId: duplicatedContract.id });
    expect(clauses).toHaveLength(1);
    expect(clauses[0]).toEqual({
      clauseId: 'fake-clause-id',
      content: [{ children: [{ text: 'Hello' }], type: 'p' }],
      contractId: duplicatedContract.id,
      userId: member.userId
    });
  });

  it('should duplicate a contract with annexed documents', async () => {
    const { client, contract, member, testingRepos } = await setupWithOffreAchat();

    // Create annexed documents for the contract
    const document1 = await testingRepos.files.createFile();
    const document2 = await testingRepos.files.createFile();

    const ficheOffrantRecord = await testingRepos.records.createRecord({
      answer: {
        nom: { locked: true, value: 'Offrant' }
      },
      organizationId: member.organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE',
      userId: member.userId
    });

    const ficheOffreRecord = await testingRepos.records.createRecord({
      answer: {
        fiche_offre_numero: { locked: true, value: '1' }
      },
      organizationId: member.organizationId,
      templateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT',
      userId: member.userId
    });

    await testingRepos.annexedDocuments.createAnnexedDocument({
      contractId: contract.id,
      documentId: document1.id,
      recordId: ficheOffreRecord.id,
      state: AnnexedDocumentStateDto.ADD
    });

    await testingRepos.annexedDocuments.createAnnexedDocument({
      contractId: contract.id,
      documentId: document2.id,
      recordId: ficheOffrantRecord.id,
      state: AnnexedDocumentStateDto.ADD
    });

    const response = await client.post('/contract-duplicates').send({ contractId: contract.id, userId: member.userId });
    expect(response.statusCode).toBe(201);

    const duplicatedContract = response.body.contract;

    const annexedDocuments = await testingRepos.annexedDocuments.getAnnexedDocumentsByContractId(duplicatedContract.id);

    expect(annexedDocuments).toHaveLength(2);

    annexedDocuments.forEach((doc) => {
      expect(doc.contractId).toBe(duplicatedContract.id);
    });

    const doc1 = annexedDocuments.find((doc) => doc.documentId === document1.id);
    const doc2 = annexedDocuments.find((doc) => doc.documentId === document2.id);

    expect(doc1?.state).toBe(AnnexedDocumentStateDto.ADD);
    expect(doc2?.state).toBe(AnnexedDocumentStateDto.ADD);
  });

  it('should duplicate an imported contract', async () => {
    const { client, contract, member, testingRepos } = await setupContract('IMPORT');

    const files: FileInfo[] = [
      {
        bucketName: 'fake',
        checksum: 'fake-checksum',
        contentType: 'application/pdf',
        id: 'fake-file-id',
        name: 'file.pdf',
        size: 1234,
        status: FileStatus.Success,
        uploadTime: 100000000
      }
    ];

    await testingRepos.contracts.updateContract({
      files: files,
      id: contract.id,
      status: ContractStatus.SIGNATURE_COMPLETED
    });

    const response = await client.post('/contract-duplicates').send({ contractId: contract.id, userId: member.userId });
    expect(response.status).toBe(201);

    const duplicatedContract: ContractResponseDto['contract'] = response.body.contract;

    expect(duplicatedContract.status).toBe(ContractStatusDto.DRAFT);
    expect(duplicatedContract.files).toEqual(files);
  });
});

/**
 * Create a member, an operation "Vente ancien" and a contract with the given template.
 */
async function setupContract(legalContractTemplateId: LegalContractTemplateId) {
  const { client, getService } = await createTestingWideApp({
    bypassAuth: true,
    controller: ContractController,
    providers: provideLegalsTest()
  });

  const testingRepos = getService(TestingRepositories);
  const legalOperationCreationService = getService(LegalOperationCreationsService);
  const contractsService = getService(ContractsService);

  const member = await testingRepos.createMember({});
  await testingRepos.templates.createLegalOperationRequiredTemplates('OPERATION__IMMOBILIER__VENTE_ANCIEN');
  await testingRepos.templates.createTemplate('RECORD__PERSONNE__PHYSIQUE');

  const legalOperation = await legalOperationCreationService.createLegalOperationWithDefaultValues({
    label: 'Test operation',
    legalOperationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
    organizationId: member.organizationId,
    userId: member.userId
  });

  const contractWithLegalData = await contractsService.createContract({
    defaultAnswers: [],
    label: 'Test contract',
    legalContractTemplateId,
    legalOperationId: legalOperation.id,
    organizationId: member.organizationId,
    userId: member.userId
  });

  return {
    client,
    contract: contractWithLegalData.contract,
    getService,
    member,
    testingRepos
  };
}

/**
 * Create a member, an operation "Vente ancien" and a contract "Offre d'achat".
 * The contract has the following data :
 * - An "Offrant" legal record which is specific to the contract.
 * - Some answers in the "Fiche offre" legal record which is specific to the contract.
 * - A status set to SIGNATURE_COMPLETED
 * - A fake contract file id set
 */
async function setupWithOffreAchat() {
  const { client, contract, getService, member, testingRepos } = await setupContract(
    'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT'
  );
  const legalRecordsService = getService(LegalRecordsService);
  const contractsService = getService(ContractsService);
  const legalBranchesService = getService(LegalBranchesService);
  const legalsService = getService(LegalsService);

  const legalOperationBranches = await testingRepos.legalBranches.getLegalBranches({
    fromLegalId: contract.legalOperationId
  });
  const offrantLegalBranch = legalOperationBranches.find((branch) => branch.type === 'OFFRANT');
  assertNotNull(offrantLegalBranch);

  const offrantLegalRecord = await legalRecordsService.createRecord({
    answer: {
      email: { locked: false, value: '<EMAIL>' },
      nom: { locked: false, value: 'DOE' },
      prenoms: { locked: false, value: 'John' }
    },
    creatorId: member.userId,
    organizationId: member.organizationId,
    templateId: 'RECORD__PERSONNE__PHYSIQUE'
  });

  const contractWithLegalData = await contractsService.createContract({
    defaultAnswers: [],
    label: 'Test contract',
    legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
    legalOperationId: contract.legalOperationId,
    organizationId: member.organizationId,
    userId: member.userId
  });

  const offreLegalBranch = contractWithLegalData.legalData?.legalBranches.find(
    (branch) => branch.type === 'FICHE_OFFRE'
  );
  assertNotNull(offreLegalBranch?.toLegalId);
  await legalRecordsService.updateLegalRecordAnswer({
    answer: {
      offre_developpee: { locked: true, value: 'oui' }
    },
    legalRecordId: offreLegalBranch.toLegalId
  });

  await legalBranchesService.createLegalBranch({
    contractId: contractWithLegalData.contract.id,
    fromLegalId: contractWithLegalData.contract.legalOperationId,
    legalLinkId: offrantLegalBranch.legalLinkId,
    toLegalId: offrantLegalRecord.id,
    type: 'OFFRANT'
  });

  // Fetch legal data after adding "Offrant"
  const contractLegalData = await legalsService.getLegalData({
    contractId: contractWithLegalData.contract.id,
    legalId: contractWithLegalData.contract.legalOperationId
  });

  await testingRepos.contracts.updateContract({
    contractFileId: 'fake-contract-file-id',
    id: contract.id,
    status: ContractStatus.SIGNATURE_COMPLETED
  });

  return {
    client,
    contract: contractWithLegalData.contract,
    contractLegalData,
    getService,
    member,
    testingRepos
  };
}
