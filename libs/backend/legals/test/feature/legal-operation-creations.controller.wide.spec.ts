import { LegalOperationLightDto, LegalOperationNewDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { provideLegalsTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import {
  isLegalRecordTemplateId,
  LegalOperationTemplateId,
  LegalRecordTemplateId
} from '@mynotary/crossplatform/legal-templates/api';
import { LegalOperationCreationsController } from '@mynotary/backend/legals/feature';
import {
  LegalOperationCreationsService,
  LegalRecordsService,
  LegalOperationDuplicateReferenceError
} from '@mynotary/backend/legals/core';
import { every, forEach, groupBy, map, uniq } from 'lodash';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { DefaultRecordsApiService } from '@mynotary/backend/default-records/api';
import {
  createDefaultLabelPattern,
  getLegalOperationTemplate
} from '@mynotary/crossplatform/legal-operation-templates/api';

describe(LegalOperationCreationsController.name, () => {
  /**
   * Legal operation creation
   */

  it('should create a legal operation with required records', async () => {
    const { createLegalOperation, member, testingRepos } = await setup();
    const legalOperationTemplateId: LegalOperationTemplateId = 'OPERATION__IMMOBILIER__VENTE_ANCIEN';

    const legalOperation = await createLegalOperation({
      label: 'Test de label',
      labelPattern: 'fake_label_pattern',
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    });

    const legalBranches = await testingRepos.legalBranches.getLegalBranches({ fromLegalId: legalOperation.id });

    // Check if all the required links are created
    const legalLinkTempalteIds = uniq(map(legalBranches, (legalBranch) => legalBranch.legalLinkTemplateId));
    expect(legalLinkTempalteIds).toHaveLength(15);
    const requiredLegalLinkTemplateIds = [
      'LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS',
      'LINK__OPERATION__IMMOBILIER__VENTE__VISITEURS',
      'LINK__OPERATION__IMMOBILIER__VENTE__VISITE',
      'LINK__OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
      'LINK__OPERATION__IMMOBILIER__VENTE__REPRESENTANTS',
      'LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS',
      'LINK__OPERATION__IMMOBILIER__VENTE__OFFRE',
      'LINK__OPERATION__IMMOBILIER__VENTE__ACQUEREUR_CESSIONNAIRE',
      'LINK__OPERATION__IMMOBILIER__VENTE__APPORTEUR',
      'LINK__OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
      'LINK__OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES',
      'LINK__OPERATION__IMMOBILIER__VENTE__AGENTS',
      'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES',
      'LINK__OPERATION__IMMOBILIER__VENTE__AGENCE_DELEGATAIRE',
      'LINK__OPERATION__IMMOBILIER__VENTE__NOTAIRES'
    ];
    const allLinksFound = every(legalLinkTempalteIds, (legalLinkTemplateId) =>
      requiredLegalLinkTemplateIds.includes(legalLinkTemplateId)
    );
    expect(allLinksFound).toBe(true);

    // Check that all the branches are created
    const legalBranchTypes = map(legalBranches, (legalBranch) => legalBranch.type);
    expect(legalBranchTypes).toHaveLength(28);
    const requiredLegalBranchType = [
      'VENDEUR',
      'VISITEUR',
      'FICHE_VISITE',
      'ACQUEREUR',
      'REPRESENTANT',
      'OFFRANT',
      'FICHE_OFFRE',
      'ACQUEREUR_CESSIONNAIRE',
      'APPORTEUR',
      'BIEN_VENDU',
      'OFFRE_ACHAT',
      'CONDITIONS_GENERALES',
      'SUBSTITUTION',
      'DELEGATION',
      'MANDAT',
      'FINANCEMENT_ACQUEREUR',
      'PROCURATION_ACHAT',
      'PROCURATION_VENTE',
      'REMISE_ALUR',
      'RESILIATION',
      'TRACFIN',
      'VISITE',
      'MANDAT_COMMERCIAL',
      'BAIL_COMMERCIAL',
      'AGENT_IMMOBILIER',
      'MANDATAIRE',
      'AGENCE_DELEGATAIRE',
      'NOTAIRE_IMMOBILIER'
    ];
    const allBranchesFound = every(legalBranchTypes, (legalBranchType) =>
      requiredLegalBranchType.includes(legalBranchType)
    );
    expect(allBranchesFound).toBe(true);

    // Check if all the required records are created
    const legalRecordTemplateIds: LegalRecordTemplateId[] = [];
    forEach(legalBranches, (legalBranch) => {
      if (isLegalRecordTemplateId(legalBranch.toLegalTemplateId)) {
        legalRecordTemplateIds.push(legalBranch.toLegalTemplateId);
      }
    });
    expect(legalRecordTemplateIds).toHaveLength(16);
    const requiredLegalRecordTemplateIds = [
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__VISITE',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__GENERAL',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__SUBSTITUTION',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__DELEGATION',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__MANDAT',
      'RECORD__OPERATION__IMMOBILIER__FINANCEMENT',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__PROCURATION_ACHAT',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__PROCURATION_VENTE',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__REMISE_ALUR',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__RESILIATION',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__TRACFIN',
      'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__VISITE',
      'RECORD__OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__MANDAT_COMMERCIAL',
      'RECORD__OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL'
    ];
    const allRecordsFound = every(legalRecordTemplateIds, (legalRecordTemplateId) =>
      requiredLegalRecordTemplateIds.includes(legalRecordTemplateId)
    );
    expect(allRecordsFound).toBe(true);

    // Check if branches are grouped correctly by link
    const legalBranchesGroupByLegalLinkTemplateId = groupBy(
      legalBranches,
      (legalBranch) => legalBranch.legalLinkTemplateId
    );
    forEach(legalBranchesGroupByLegalLinkTemplateId, (legalBranches, legalLinkTemplateId) => {
      if (legalLinkTemplateId === 'LINK__OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES') {
        expect(legalBranches).toHaveLength(14);
      } else {
        expect(legalBranches).toHaveLength(1);
      }
    });

    const currentOperation = await testingRepos.operations.selectOperation({ operationId: legalOperation.id });

    expect(currentOperation).toMatchObject({
      creatorUserId: member.userId,
      label: 'Test de label',
      labelPattern: 'fake_label_pattern',
      legalOperationTemplateId,
      organizationId: member.organizationId
    });
  });

  /**
   * Default records
   */

  it('should add default legal records', async () => {
    const { createLegalOperation, member, testingRepos } = await setup();
    const legalOperationTemplateId: LegalOperationTemplateId = 'OPERATION__IMMOBILIER__VENTE_ANCIEN';

    const defaultMandataireRecord = await testingRepos.records.createRecord({
      organizationId: member.organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE__INTERMEDIAIRE_IMMOBILIER',
      userId: member.userId
    });
    await testingRepos.defaultRecords.createDefaultRecord({
      isOrganizationRecord: false,
      linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES',
      operationTemplateId: legalOperationTemplateId,
      organizationId: member.organizationId,
      recordId: defaultMandataireRecord.id,
      userId: member.userId
    });

    const otherUser = await testingRepos.users.createUser();
    const otherDefaultMandataireRecord = await testingRepos.records.createRecord({
      organizationId: member.organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE__INTERMEDIAIRE_IMMOBILIER',
      userId: member.userId
    });
    await testingRepos.defaultRecords.createDefaultRecord({
      isOrganizationRecord: false,
      linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES',
      operationTemplateId: legalOperationTemplateId,
      organizationId: member.organizationId,
      recordId: otherDefaultMandataireRecord.id,
      userId: otherUser.id
    });

    const defaultAgenceRecord = await testingRepos.records.createRecord({
      organizationId: member.organizationId,
      templateId: 'RECORD__PERSONNE__MORALE__AGENT_IMMOBILIER',
      userId: member.userId
    });
    await testingRepos.defaultRecords.createDefaultRecord({
      isOrganizationRecord: true,
      linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__AGENTS',
      operationTemplateId: legalOperationTemplateId,
      organizationId: member.organizationId,
      recordId: defaultAgenceRecord.id,
      userId: member.userId
    });

    const legalOperation = await createLegalOperation({
      label: 'Test de label',
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    });

    const legalBranches = await testingRepos.legalBranches.getLegalBranches({ fromLegalId: legalOperation.id });

    const targetMandataire = legalBranches.find((legalBranch) => legalBranch.toLegalId === defaultMandataireRecord.id);
    expect(targetMandataire).toBeDefined();
    expect(targetMandataire?.type).toBe('MANDATAIRE');

    const otherTargetMandataire = legalBranches.find(
      (legalBranch) => legalBranch.toLegalId === otherDefaultMandataireRecord.id
    );
    expect(otherTargetMandataire).toBeUndefined();

    const targetAgence = legalBranches.find((testingBranch) => testingBranch.toLegalId === defaultAgenceRecord.id);
    expect(targetAgence).toBeDefined();
    expect(targetAgence?.type).toBe('AGENT_IMMOBILIER');
  });

  it('should not add default legal records if operation serve as reference', async () => {
    const { createLegalOperation, legalOperationCreationsService, member } = await setup();
    const legalOperationTemplateId: LegalOperationTemplateId = 'OPERATION__IMMOBILIER__VENTE_ANCIEN';

    const addDefaultRecordsSpy = jest.spyOn(legalOperationCreationsService, 'addDefaultNewBranch');

    await createLegalOperation({
      isOperationReference: true,
      label: 'Test de label',
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    });

    expect(addDefaultRecordsSpy).not.toHaveBeenCalled();
  });

  it('should not add default legal records if the default record not matching link template to', async () => {
    const { createLegalOperation, member, testingRepos } = await setup();
    const legalOperationTemplateId: LegalOperationTemplateId = 'OPERATION__IMMOBILIER__VENTE_ANCIEN';

    const defaultMandataireRecord = await testingRepos.records.createRecord({
      organizationId: member.organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE__AGENT_IMMOBILIER',
      userId: member.userId
    });
    await testingRepos.defaultRecords.createDefaultRecord({
      isOrganizationRecord: false,
      linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES',
      operationTemplateId: legalOperationTemplateId,
      organizationId: member.organizationId,
      recordId: defaultMandataireRecord.id,
      userId: member.userId
    });

    const legalOperation = await createLegalOperation({
      label: 'Test',
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    });

    const legalBranches = await testingRepos.legalBranches.getLegalBranches({ fromLegalId: legalOperation.id });

    const targetMandataire = legalBranches.find((legalBranch) => legalBranch.toLegalId === defaultMandataireRecord.id);
    expect(targetMandataire).toBeUndefined();
  });

  /**
   * Default answers
   */

  it('should add default answers', async () => {
    const { createLegalOperation, legalRecordsService, member, testingRepos } = await setup();

    const operationReference = await createLegalOperation({
      isOperationReference: true,
      legalOperationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
      organizationId: member.organizationId,
      userId: member.userId
    });

    const referenceLegalBranches = await testingRepos.legalBranches.getLegalBranches({
      fromLegalId: operationReference.id
    });
    const targetReferenceBranch = referenceLegalBranches.find((branch) => branch.type === 'MANDAT');
    assertNotNull(targetReferenceBranch?.toLegalId, 'Branch MANDAT not found in operation reference');

    await legalRecordsService.updateLegalRecordAnswer({
      answer: {
        mandat_type: { locked: true, value: 'exclusif' }
      },
      legalRecordId: targetReferenceBranch.toLegalId
    });

    const legalOperation = await createLegalOperation({
      legalOperationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
      organizationId: member.organizationId,
      userId: member.userId
    });
    const legalBranches = await testingRepos.legalBranches.getLegalBranches({ fromLegalId: legalOperation.id });
    const targetBranch = legalBranches.find((branch) => branch.type === 'MANDAT');
    assertNotNull(targetBranch?.toLegalId, 'Branch MANDAT not found in legal operation');
    const record = await legalRecordsService.getLegalRecord(targetBranch.toLegalId);

    expect(record.answer.mandat_type.value).toBe('exclusif');
  });

  /**
   * Legal operation reference
   */

  it('should throw if operation reference already exists and user tries to create a new one', async () => {
    const { client, createLegalOperation, member } = await setup();
    const legalOperationTemplateId: LegalOperationTemplateId = 'OPERATION__IMMOBILIER__VENTE_ANCIEN';

    await createLegalOperation({
      isOperationReference: true,
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    });

    const response = await client.post(`/legal-operations`).send({
      isOperationReference: true,
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    } satisfies LegalOperationNewDto);

    expect(response.status).toBe(400);
    expect(response.body.type).toEqual(LegalOperationDuplicateReferenceError.name);
  });

  it('should throw if operation reference has a parentOperationId', async () => {
    const { client, createLegalOperation, member } = await setup();
    const legalOperationTemplateId: LegalOperationTemplateId = 'OPERATION__IMMOBILIER__VENTE_ANCIEN';

    const parentOperation = await createLegalOperation({
      legalOperationTemplateId,
      organizationId: member.organizationId,
      userId: member.userId
    });

    const response = await client.post(`/legal-operations`).send({
      isOperationReference: true,
      legalOperationTemplateId,
      organizationId: member.organizationId,
      parentOperationId: parentOperation.id,
      userId: member.userId
    } satisfies LegalOperationNewDto);

    expect(response.status).toBe(422);
  });

  /**
   * Sub operation creation
   */

  it('should create a sub operation', async () => {
    const { createLegalOperation, member, testingRepos } = await setup();
    await testingRepos.templates.createLegalOperationRequiredTemplates('OPERATION__DUVAL__IMMOBILIER__PROGRAMME_VEFA');
    await testingRepos.templates.createLegalOperationRequiredTemplates(
      'OPERATION__DUVAL__IMMOBILIER__VEFA_RESERVATION'
    );

    const parentOperation = await createLegalOperation({
      legalOperationTemplateId: 'OPERATION__DUVAL__IMMOBILIER__PROGRAMME_VEFA',
      organizationId: member.organizationId,
      userId: member.userId
    });

    const subOperation = await createLegalOperation({
      legalOperationTemplateId: 'OPERATION__DUVAL__IMMOBILIER__VEFA_RESERVATION',
      organizationId: member.organizationId,
      parentOperationId: parentOperation.id,
      userId: member.userId
    });

    const operation = await testingRepos.operations.selectOperation({ operationId: subOperation.id });
    const legalBraches = await testingRepos.legalBranches.getLegalBranches({ fromLegalId: parentOperation.id });

    const venteBranch = legalBraches.find((branch) => branch.type === 'VENTE' && branch.toLegalId === subOperation.id);

    expect(subOperation.id).toBeDefined();
    expect(operation?.parentId).toEqual(parentOperation.id);
    expect(venteBranch).toBeDefined();
    expect(venteBranch?.toLegalId).toEqual(subOperation.id);
    expect(venteBranch?.legalLinkTemplateId).toEqual('LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__VENTE_VEFA_DUVAL');
  });

  it('should rollback if sub operation creation failed during legal branch creation', async () => {
    const { client, createLegalOperation, legalOperationCreationsService, member, testingRepos } = await setup();
    await testingRepos.templates.createLegalOperationRequiredTemplates('OPERATION__DUVAL__IMMOBILIER__PROGRAMME_VEFA');

    let legalOperationCreated: string | null = null;
    jest.spyOn(legalOperationCreationsService, 'linkSubLegalOperationToParent').mockImplementationOnce((args) => {
      legalOperationCreated = args.subLegalOperationId;
      return legalOperationCreationsService.linkSubLegalOperationToParent(args);
    });

    const parentOperation = await createLegalOperation({
      legalOperationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
      organizationId: member.organizationId,
      userId: member.userId
    });

    const response = await client.post(`/legal-operations`).send({
      legalOperationTemplateId: 'OPERATION__DUVAL__IMMOBILIER__VEFA_RESERVATION',
      organizationId: member.organizationId,
      parentOperationId: parentOperation.id,
      userId: member.userId
    } satisfies LegalOperationNewDto);

    expect(response.status).toBe(404);
    expect(legalOperationCreated).toBeDefined();
    assertNotNull(legalOperationCreated, 'Sub operation not created');
    const legalOperation = await testingRepos.operations.selectOperation({ operationId: legalOperationCreated });

    expect(legalOperation.deleted).toEqual(true);
  });

  it('should force creation with default label pattern if label and label pattern are not provided', async () => {
    const { client, member, testingRepos } = await setup();
    const template = getLegalOperationTemplate('OPERATION__IMMOBILIER__VENTE_ANCIEN');
    await testingRepos.templates.createLegalOperationRequiredTemplates(template.id);

    const response = await client.post(`/legal-operations`).send({
      legalOperationTemplateId: template.id,
      organizationId: member.organizationId,
      userId: member.userId
    } satisfies LegalOperationNewDto);

    expect(response.status).toBe(201);
    const legalOperation = await testingRepos.operations.selectOperation({ operationId: response.body.id });

    const expectedResult = createDefaultLabelPattern(template);
    const actualResult = JSON.parse(legalOperation.labelPattern ?? '[]');

    expect(actualResult).toEqual(expectedResult);
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: LegalOperationCreationsController,
      providers: provideLegalsTest()
    });

    const testingRepos = getService(TestingRepositories);

    const member = await testingRepos.createMember();
    await testingRepos.templates.createLegalOperationRequiredTemplates('OPERATION__IMMOBILIER__VENTE_ANCIEN');

    const legalOperationCreationsService = getService(LegalOperationCreationsService);
    const legalRecordsService = getService(LegalRecordsService);
    const defaultRecordsApiService = getService(DefaultRecordsApiService);

    return {
      client,
      createLegalOperation: async (body: TestDto): Promise<LegalOperationLightDto> => {
        const response = await client.post(`/legal-operations`).send(body);
        return response.body;
      },
      defaultRecordsApiService,
      legalOperationCreationsService,
      legalRecordsService,
      member,
      testingRepos
    };
  }
});

/**
 * Since legalOperationTemplateId is type as string in openapi spec, we need to extend the dto to to have a better type
 * checking and auto-completion.
 */
type TestDto = LegalOperationNewDto & { legalOperationTemplateId: LegalOperationTemplateId };
