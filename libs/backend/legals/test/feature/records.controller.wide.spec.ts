import { LegalRecordDto, RecordNewDto, RecordUpdateDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { RecordsController } from '@mynotary/backend/legals/feature';
import { provideLegalsTest } from '../index';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { InternalEntityType } from '@mynotary/backend/external-apps/api';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { LegalRecordTemplateId } from '@mynotary/crossplatform/legal-templates/api';

describe(RecordsController.name, () => {
  it('should create record', async () => {
    const { client, organizationId, userId } = await setup();

    const response = await client.post(`/records`).send({
      answer: {
        nom: { value: 'DUPONT' },
        prenoms: { value: 'Jean' },
        sexe: { value: 'homme' }
      },
      creatorId: userId,
      organizationId: organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE'
    } satisfies RecordNewDto);

    const body: LegalRecordDto = response.body;
    expect(response.statusCode).toBe(201);

    expect(body).toEqual({
      answer: {
        nom: { value: 'DUPONT' },
        prenoms: { value: 'Jean' },
        sexe: { value: 'homme' }
      },
      answerId: expect.any(String),
      createdAt: expect.any(String),
      creator: {
        firstname: expect.any(String),
        id: userId,
        lastname: expect.any(String)
      },
      id: expect.any(String),
      organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE'
    } satisfies LegalRecordDto);
  });

  it(`should update record answer`, async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const response = await client.post(`/records`).send({
      answer: {
        nom: { value: 'DUPONT' },
        prenoms: { value: 'Jean' },
        sexe: { value: 'homme' },
        telephone: { value: '0123456789' }
      },
      creatorId: userId,
      organizationId: organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE'
    } satisfies RecordNewDto);

    const updateResponse = await client.put(`/records/${response.body.id}`).send({
      answer: {
        date_de_naissance: { value: '1990-01-01' },
        nom: { value: 'MICHU' }
      }
    } satisfies RecordUpdateDto);

    expect(updateResponse.statusCode).toBe(200);
    const record = await testingRepos.records.getRecord({ recordId: response.body.id });

    expect(record.answer).toEqual({
      date_de_naissance: { value: '1990-01-01' },
      nom: { value: 'MICHU' },
      prenoms: { value: 'Jean' },
      sexe: { value: 'homme' },
      telephone: { value: '0123456789' }
    });
  });

  it(`should return 404 if update on record not found`, async () => {
    const { client } = await setup();

    const NOT_FOUND_ID = '-1';
    const updateResponse = await client.put(`/records/${NOT_FOUND_ID}`).send({
      answer: {
        sexe: { value: 'femme' }
      }
    } satisfies RecordUpdateDto);

    expect(updateResponse.statusCode).toBe(404);
  });

  it(`should deny update record template when template mismatch`, async () => {
    const { client, organizationId, userId } = await setup();

    const response = await client.post(`/records`).send({
      answer: {
        nom: { value: 'DUPONT' }
      },
      creatorId: userId,
      organizationId: organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE'
    } satisfies RecordNewDto);

    const updateResponse = await client.put(`/records/${response.body.id}`).send({
      templateId: 'RECORD__BIEN__INDIVIDUEL_HABITATION' as LegalRecordTemplateId
    } satisfies RecordUpdateDto);

    expect(updateResponse.statusCode).toBe(400);
  });

  it(`should update record template when correct template is set`, async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    await testingRepos.templates.createTemplate('RECORD__PERSONNE__MORALE');

    const response = await client.post(`/records`).send({
      answer: {
        nom: { value: 'DUPONT' }
      },
      creatorId: userId,
      organizationId: organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE'
    } satisfies RecordNewDto);

    const updateResponse = await client.put(`/records/${response.body.id}`).send({
      templateId: 'RECORD__PERSONNE__MORALE'
    } satisfies RecordUpdateDto);

    expect(updateResponse.statusCode).toBe(200);
    const record = await testingRepos.records.getRecord({ recordId: response.body.id });

    expect(record.answer).toEqual({
      nom: { value: 'DUPONT' }
    });
    expect(record.templateId).toEqual('RECORD__PERSONNE__MORALE');
  });

  it('should merge answers', async () => {
    const { client, recordId, testingRepos } = await setupWithRecord();

    const response = await client.put(`/records/${recordId}`).send({
      answer: {
        lastname: { value: 'Doe' }
      }
    } satisfies RecordUpdateDto);

    expect(response.statusCode).toBe(200);

    const recordUpdated = await testingRepos.records.getRecord({ recordId });
    expect(recordUpdated.answer).toEqual({
      firstname: { value: 'John' },
      lastname: { value: 'Doe' }
    });
  });

  it('should update answer value', async () => {
    const { client, recordId, testingRepos } = await setupWithRecord();

    const response = await client.put(`/records/${recordId}`).send({
      answer: {
        firstname: { value: 'Doe' }
      }
    } satisfies RecordUpdateDto);

    expect(response.statusCode).toBe(200);
    const recordUpdated = await testingRepos.records.getRecord({ recordId });
    expect(recordUpdated.answer).toEqual({
      firstname: { value: 'Doe' }
    });
  });

  it('should not update answer value if it was locked', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const record = await testingRepos.records.createRecord({
      answer: { firstname: { locked: true, value: 'John' } },
      organizationId,
      userId
    });

    const response = await client.put(`/records/${record.id}`).send({
      answer: { firstname: { value: 'Doe' } }
    } satisfies RecordUpdateDto);

    expect(response.statusCode).toBe(200);

    const updatedRecord = await testingRepos.records.getRecord({ recordId: record.id });

    expect(updatedRecord.answer).toEqual({ firstname: { locked: true, value: 'John' } });
  });

  it('should update answer value and add locked value', async () => {
    const { client, recordId, testingRepos } = await setupWithRecord();
    const updateAnswer = { firstname: { locked: true, value: 'Doe' } };

    const response = await client.put(`/records/${recordId}`).send({
      answer: updateAnswer
    } satisfies RecordUpdateDto);

    expect(response.statusCode).toBe(200);
    const updatedRecord = await testingRepos.records.getRecord({ recordId });

    expect(updatedRecord.answer).toEqual({ firstname: { locked: true, value: 'Doe' } });
  });

  it('should merge many property', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();
    const srcAnswer = { age: { value: 30 }, firstname: { value: 'John' } };

    const record = await testingRepos.records.createRecord({
      answer: srcAnswer,
      organizationId,
      userId
    });

    const updateAnswer = { email: { value: '<EMAIL>' }, lastname: { value: 'Doe' } };

    const response = await client.put(`/records/${record.id}`).send({
      answer: updateAnswer
    } satisfies RecordUpdateDto);

    expect(response.statusCode).toBe(200);

    const updatedRecord = await testingRepos.records.getRecord({ recordId: record.id });

    expect(updatedRecord.answer).toEqual({
      age: { value: 30 },
      email: { value: '<EMAIL>' },
      firstname: { value: 'John' },
      lastname: { value: 'Doe' }
    });
  });

  it('should merge arrays with custom merge strategy', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const previousAnswer: AnswerDict = {
      questionId1: { value: 'oldValue1' },
      questionId2: { value: ['oldValue1', 'oldValue2'] }
    };

    const newAnswer: AnswerDict = {
      questionId2: { value: ['newValue1', 'newValue2'] }
    };

    const record = await testingRepos.records.createRecord({
      answer: previousAnswer,
      organizationId,
      userId
    });

    const response = await client.put(`/records/${record.id}`).send({
      answer: newAnswer
    } satisfies RecordUpdateDto);

    expect(response.statusCode).toBe(200);

    const updatedRecord = await testingRepos.records.getRecord({ recordId: record.id });

    expect(updatedRecord.answer).toEqual({
      questionId1: { value: 'oldValue1' },
      questionId2: { value: ['newValue1', 'newValue2'] }
    });
  });

  it('should add file to additional documents', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const srcAnswer = {
      firstname: { locked: true, value: 'John' },
      informations_generales: { additionalDocuments: { 'uuid-mocked': { label: 'Attestation' } } }
    };

    const record = await testingRepos.records.createRecord({
      answer: srcAnswer,
      organizationId,
      userId
    });

    const updateAnswer = { 'uuid-mocked': { value: { file_id: 1 } } };

    const response = await client.put(`/records/${record.id}`).send({
      answer: updateAnswer
    } satisfies RecordUpdateDto);

    expect(response.statusCode).toBe(200);

    const updatedRecord = await testingRepos.records.getRecord({ recordId: record.id });
    expect(updatedRecord.answer).toEqual({
      'firstname': { locked: true, value: 'John' },
      'informations_generales': { additionalDocuments: { 'uuid-mocked': { label: 'Attestation' } } },
      'uuid-mocked': { value: { file_id: 1 } }
    });
  });

  it('should remove file from additional documents', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const srcAnswer = {
      'firstname': { locked: true, value: 'John' },
      'informations_generales': { additionalDocuments: { 'uuid-mocked': { label: 'Attestation' } } },
      'uuid-mocked': { value: { file_id: 1 } }
    };

    const record = await testingRepos.records.createRecord({
      answer: srcAnswer,
      organizationId,
      userId
    });

    const updateAnswer = { 'uuid-mocked': { value: { file_id: null } } };

    const response = await client.put(`/records/${record.id}`).send({
      answer: updateAnswer
    } satisfies RecordUpdateDto);

    expect(response.statusCode).toBe(200);

    const updatedRecord = await testingRepos.records.getRecord({ recordId: record.id });
    expect(updatedRecord.answer).toEqual({
      'firstname': { locked: true, value: 'John' },
      'informations_generales': { additionalDocuments: { 'uuid-mocked': { label: 'Attestation' } } },
      'uuid-mocked': {
        value: { file_id: null }
      }
    });
  });

  it('should fetch a record with associated links/branches/records', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    // GIVEN
    const fromLegalRecord = await testingRepos.records.createRecord({
      organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE',
      userId
    });
    const toLegalRecord = await testingRepos.records.createRecord({
      organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE',
      userId
    });
    const linkRecord = await testingRepos.records.createRecord({
      organizationId,
      templateId: 'RECORD__LIEN__SITUATION_MARITALE__MARIAGE',
      userId
    });

    // createLegalBranch will create a legal link
    const legalBranch = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: fromLegalRecord.id,
      legalLinkTemplateId: 'LINK__SITUATION_MARITALE__MARIAGE',
      organizationId,
      recordId: linkRecord.id,
      toId: toLegalRecord.id,
      type: 'EPOUX',
      userId
    });

    // WHEN
    const response = await client.get(`/legal-record-data/${fromLegalRecord.id}`).send();

    // THEN
    expect(response.statusCode).toBe(200);
    expect(response.body).toEqual({
      legalBranches: [
        {
          fromLegalId: fromLegalRecord.id,
          fromLegalTemplateId: 'RECORD__PERSONNE__PHYSIQUE',
          id: legalBranch.id,
          legalLinkId: expect.any(String),
          legalLinkTemplateId: 'LINK__SITUATION_MARITALE__MARIAGE',
          toLegalId: toLegalRecord.id,
          toLegalTemplateId: 'RECORD__PERSONNE__PHYSIQUE',
          type: 'EPOUX'
        }
      ],
      legalLinks: [
        {
          creatorId: userId,
          id: expect.any(String),
          legalBranchIds: [legalBranch.id],
          legalRecordId: expect.any(String),
          organizationId,
          templateId: 'LINK__SITUATION_MARITALE__MARIAGE'
        }
      ],
      legalRecords: [
        {
          answer: {},
          answerId: expect.any(String),
          createdAt: expect.any(String),
          creator: {
            firstname: 'Foo',
            id: expect.any(String),
            lastname: 'BAR'
          },
          id: fromLegalRecord.id,
          organizationId,
          templateId: 'RECORD__PERSONNE__PHYSIQUE'
        },
        {
          answer: {},
          answerId: expect.any(String),
          createdAt: expect.any(String),
          creator: {
            firstname: 'Foo',
            id: expect.any(String),
            lastname: 'BAR'
          },
          id: toLegalRecord.id,
          organizationId,
          templateId: 'RECORD__PERSONNE__PHYSIQUE'
        },
        {
          answer: {},
          answerId: expect.any(String),
          createdAt: expect.any(String),
          creator: {
            firstname: 'Foo',
            id: expect.any(String),
            lastname: 'BAR'
          },
          id: linkRecord.id,
          organizationId,
          templateId: 'RECORD__LIEN__SITUATION_MARITALE__MARIAGE'
        }
      ]
    });
  });

  it('should not delete a record if it has associated default records', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const record = await testingRepos.records.createRecord({
      answer: { firstname: { value: 'John' } },
      organizationId,
      userId
    });

    await testingRepos.defaultRecords.createDefaultRecord({
      isOrganizationRecord: true,
      linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS',
      operationTemplateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
      organizationId,
      recordId: record.id,
      userId
    });

    const response = await client.delete(`/records/${record.id}`).send();

    expect(response.statusCode).toBe(400);
  });

  it('should delete a record  and associated element successfully', async () => {
    const { client, organizationId, testingRepos, userId } = await setup();

    const record = await testingRepos.records.createRecord({
      answer: {
        firstname: { value: 'John' }
      },
      organizationId,
      userId
    });

    const linkedRecord = await testingRepos.records.createRecord({
      answer: {
        firstname: { value: 'FRANK' }
      },
      organizationId,
      userId
    });

    const maritalStatusLink = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: record.id,
      legalLinkTemplateId: 'LINK__SITUATION_MARITALE__MARIAGE',
      organizationId,
      toId: linkedRecord.id,
      type: 'EPOUX',
      userId
    });

    const operation = await testingRepos.operations.createOperation({
      organizationId,
      templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
      userId
    });

    const operationVendorLink = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: operation.id,
      legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS',
      organizationId,
      toId: record.id,
      type: 'VENDEURS',
      userId
    });

    const externalAssociation = await testingRepos.externalApps.createAssociation({
      externalId: 'externalId',
      internalType: InternalEntityType.LEGAL_COMPONENT,
      myNotaryId: record.id,
      organizationId,
      type: 'FOO'
    });

    const response = await client.delete(`/records/${record.id}`).send();

    const maritalBranch = await testingRepos.legalBranches.findLegalBranch({ branchId: maritalStatusLink.id });
    const maritalLink = await testingRepos.legalLinks.findLegalLink({ linkId: maritalStatusLink.linkId });
    const vendorBranch = await testingRepos.legalBranches.findLegalBranch({ branchId: operationVendorLink.id });
    const vendorLink = await testingRepos.legalLinks.findLegalLink({ linkId: operationVendorLink.linkId });
    const deletedAssociation = await testingRepos.externalApps.findAssociation(externalAssociation.id);

    expect(response.statusCode).toBe(200);

    const deletedRecord = await testingRepos.records.getRecord({ recordId: record.id });
    expect(deletedRecord.deleted).toBe(true);

    expect(maritalBranch.id).toBeUndefined();
    expect(maritalLink.id).toBeUndefined();
    expect(vendorBranch.id).toBeUndefined();
    expect(vendorLink.id).toBeUndefined();
    expect(deletedAssociation).toBeNull();
  });

  it('should retrieve legal record infos', async () => {
    const { client } = await setup();

    const response = await client.get(`/legal-record-infos?legalRecordTemplateId=RECORD__PERSONNE__PHYSIQUE`).send();

    expect(response.statusCode).toBe(200);
    expect(response.body.questions.length).toBeGreaterThan(10);
    expect(response.body.documents.length).toBeGreaterThan(10);
  });

  async function setupWithRecord() {
    const { client, organizationId, testingRepos, userId } = await setup();

    const record = await testingRepos.records.createRecord({
      answer: {
        firstname: { value: 'John' }
      },
      organizationId,
      userId
    });

    return { client, recordId: record.id, testingRepos, userId };
  }

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: RecordsController,
      providers: provideLegalsTest()
    });

    const testingRepos = getService(TestingRepositories);
    await testingRepos.templates.createTemplate('RECORD__PERSONNE__PHYSIQUE');

    const { organizationId, userId } = await testingRepos.createMember({});

    return { client, organizationId, testingRepos, userId };
  }
});
