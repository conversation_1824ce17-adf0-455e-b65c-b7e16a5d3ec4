import { OperationDuplicatesController } from '@mynotary/backend/legals/feature';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { provideLegalsTest } from '../index';
import { ContractsService, LegalRecordsService } from '@mynotary/backend/legals/core';
import { ContractStatusDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';

describe(OperationDuplicatesController.name, () => {
  it('should prevent duplicating a program', async () => {
    const { client, member, testingRepos } = await setup();

    const program = await testingRepos.operations.createOperation({
      labelOperation: 'operation',
      organizationId: member.organizationId,
      templateId: 'OPERATION__IMMOBILIER__PSLA_PROGRAMME',
      userId: member.id
    });

    const response = await client
      .post(`/operation-duplicates`)
      .send({ creatorUserId: member.id, operationId: program.id });

    expect(response.statusCode).toBe(400);
  });

  it('should prevent duplicating a child operation', async () => {
    const { client, member, testingRepos } = await setup();

    const program = await testingRepos.operations.createOperation({
      labelOperation: 'operation',
      organizationId: member.organizationId,
      templateId: 'OPERATION__IMMOBILIER__PSLA_PROGRAMME',
      userId: member.id
    });

    const childOrganization = await testingRepos.operations.createOperation({
      labelOperation: 'operation',
      organizationId: member.organizationId,
      parentOperationId: program.id,
      templateId: 'OPERATION__IMMOBILIER__PSLA_PROGRAMME',
      userId: member.id
    });

    const response = await client
      .post(`/operation-duplicates`)
      .send({ creatorUserId: member.id, operationId: childOrganization.id });

    expect(response.statusCode).toBe(400);
  });

  it('should create a duplicated operation', async () => {
    const {
      branchMandat,
      branchVendeur,
      client,
      folder,
      mandatRecord,
      member,
      newFile1,
      newFile2,
      operation,
      operationToDuplicate,
      testingRepos,
      vendeurRecord
    } = await setupOperation();

    const response = await client
      .post(`/operation-duplicates`)
      .send({ creatorUserId: member.id, operationId: operation.id });

    expect(response.statusCode).toBe(201);

    const operationDuplicated = await testingRepos.operations.selectOperation({
      operationId: response.body.operationId
    });

    const operationDuplicatedData = {
      archived: operationDuplicated.archived,
      deleted: operationDuplicated.deleted,
      label: operationDuplicated.label,
      labelPattern: operationDuplicated.labelPattern,
      organizationId: operationDuplicated.organizationId,
      parentId: operationDuplicated.parentId,
      statusId: operationDuplicated.statusId,
      templateId: operationDuplicated.legalOperationTemplateId,
      userId: operationDuplicated.userId
    };

    const legalData = await testingRepos.legalBranches.getLegalBranches({
      fromLegalId: operationDuplicated.id
    });

    const vendeurDuplicated = legalData.find((d) => d.type === 'VENDEUR' && d.toLegalId != null);
    const mandatDuplicated = legalData.find((d) => d.type === 'MANDAT' && d.toLegalId != null);

    expect(operationDuplicatedData).toEqual({
      archived: false,
      deleted: false,
      label: operationToDuplicate.label,
      labelPattern: operationToDuplicate.labelPattern,
      organizationId: operationToDuplicate.organizationId,
      parentId: undefined,
      statusId: null,
      templateId: operationToDuplicate.legalOperationTemplateId,
      userId: member.id
    });
    expect(vendeurDuplicated).toMatchObject({
      fromLegalId: expect.not.stringMatching(branchVendeur.fromLegalId),
      legalLinkId: expect.not.stringMatching(branchVendeur.linkId),
      legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS',
      toLegalId: vendeurRecord.id,
      toLegalRecordAnswer: {
        email: {
          locked: true,
          value: '<EMAIL>'
        }
      },
      type: 'VENDEUR'
    });

    expect(mandatDuplicated).toMatchObject({
      fromLegalId: expect.not.stringMatching(branchVendeur.fromLegalId),
      legalLinkId: expect.not.stringMatching(branchMandat.linkId),
      legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES',
      toLegalId: expect.not.stringMatching(mandatRecord.id),
      toLegalRecordAnswer: {
        contrat_numero_registre: { locked: false, value: '' },
        mandat_numero: { locked: false, value: '' },
        mandat_numero_gestion: { locked: false, value: '' },
        mandat_numero_transaction: { locked: false, value: '' },
        mandat_vente_calcul: { locked: false, value: 'recherche_pourcentage' }
      },
      type: 'MANDAT'
    });

    const drive = await testingRepos.drives.getDrive({
      operationId: response.body.operationId
    });

    expect(drive.files).toMatchObject([
      {
        documentLabel: 'document_label',
        fileId: newFile1.id,
        folderId: expect.not.stringMatching(folder.driveFolderId),
        operationId: expect.not.stringMatching(operation.id),
        userId: member.id
      },
      {
        fileId: newFile2.id,
        operationId: expect.not.stringMatching(operation.id),
        userId: member.id
      }
    ]);

    expect(drive.folders).toMatchObject([
      {
        label: 'folder',
        operationId: expect.not.stringMatching(operation.id),
        userId: member.id
      }
    ]);
  });

  /**
   * This test simulates the duplication of a legal operation that contains a "Offre d'achat" contract.
   *
   * The test verifies that during duplication:
   *
   * 1. For the "FICHE_OFFRE" - created automatically when the contract is created:
   *    - The answers are correctly copied to the new legal record
   *    - The legal record ID is different but the data is preserved
   *    - The relationship is correctly established with the new contract*
   * 2. For the "OFFRANT" - added manually to the contract:
   *    - The legal record ID remains the same (same physical person)
   *    - The legalLinkId is updated to match the one from the duplicated operation
   *    - The relationship is correctly established with the new contract
   */
  it('should duplicate an operation with contracts', async () => {
    const { client, firstUser, getService, member, operation, testingRepos } = await setupOperation();

    const contractsService = getService(ContractsService);
    const recordsService = getService(LegalRecordsService);

    const contract = await contractsService.createContractWithDefaultValue({
      label: 'Test contract in operation',
      legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
      legalOperationId: operation.id,
      userId: firstUser.id
    });
    const srcLegalData = await testingRepos.legalBranches.getLegalBranches({
      fromLegalId: operation.id
    });
    const srcFicheOffreData = srcLegalData.find(
      (d) => d.type === 'FICHE_OFFRE' && d.specificContractId === contract.contract.id
    );
    assertNotNull(srcFicheOffreData?.toLegalId, 'offreSrc is null');
    await recordsService.updateLegalRecordAnswer({
      answer: { offre_developpee: { value: 'oui' } },
      legalRecordId: srcFicheOffreData.toLegalId
    });

    const srcOffrantLegalRecord = await testingRepos.records.createRecord({
      answer: { nom: { locked: true, value: 'Offrant' } },
      organizationId: member.organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE',
      userId: member.userId
    });

    const srcOffrantLegalBranch = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: operation.id,
      legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS',
      organizationId: member.organizationId,
      specificContractId: contract.contract.id,
      toId: srcOffrantLegalRecord.id,
      type: 'OFFRANT',
      userId: firstUser.id
    });

    const response = await client
      .post(`/operation-duplicates`)
      .send({ creatorUserId: member.id, operationId: operation.id });

    expect(response.statusCode).toBe(201);

    const destLegalOperationId = response.body.operationId;
    const destContracts = await contractsService.getContracts({
      operationId: destLegalOperationId
    });

    const destLegalData = await testingRepos.legalBranches.getLegalBranches({
      fromLegalId: destLegalOperationId
    });

    expect(destContracts).toHaveLength(1);
    expect(destContracts[0]).toMatchObject({
      creatorId: member.id,
      label: 'Copie de Test contract in operation',
      legalContractTemplateId: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT',
      legalOperationId: destLegalOperationId,
      organizationId: member.organizationId,
      status: ContractStatusDto.REDACTION
    });

    const destFicheOffreDataOperation = destLegalData.find(
      (d) => d.type === 'FICHE_OFFRE' && d.specificContractId == null
    );
    const destFicheOffreDataContractSpecific = destLegalData.find(
      (d) => d.type === 'FICHE_OFFRE' && destContracts[0].id === d.specificContractId
    );

    expect(destFicheOffreDataOperation?.legalLinkId).not.toEqual(srcFicheOffreData.legalLinkId);
    expect(destFicheOffreDataContractSpecific).toEqual({
      fromLegalId: destLegalOperationId,
      id: expect.any(String),
      legalLinkId: destFicheOffreDataOperation?.legalLinkId,
      legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRE',
      specificContractId: destContracts[0].id,
      toLegalId: expect.not.stringMatching(srcFicheOffreData.toLegalId),
      toLegalRecordAnswer: { offre_developpee: { value: 'oui' } },
      toLegalTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT',
      type: 'FICHE_OFFRE'
    });

    const destOffrantDataOperation = destLegalData.find((d) => d.type === 'OFFRANT' && d.specificContractId == null);
    const destOffrantDataContractSpecific = destLegalData.find(
      (d) => d.type === 'OFFRANT' && d.specificContractId === destContracts[0].id
    );

    assertNotNull(srcOffrantLegalBranch.toId, 'srcOffrantLegalBranch.toId is null');
    expect(destOffrantDataOperation?.legalLinkId).not.toEqual(srcOffrantLegalBranch.linkId);
    expect(destOffrantDataContractSpecific).toEqual({
      fromLegalId: destLegalOperationId,
      id: expect.any(String),
      legalLinkId: destOffrantDataOperation?.legalLinkId,
      legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS',
      specificContractId: destContracts[0].id,
      toLegalId: srcOffrantLegalRecord.id,
      toLegalRecordAnswer: { nom: { locked: true, value: 'Offrant' } },
      toLegalTemplateId: 'RECORD__PERSONNE__PHYSIQUE',
      type: 'OFFRANT'
    });
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: OperationDuplicatesController,
      providers: provideLegalsTest()
    });

    const testingRepos = getService(TestingRepositories);

    const member = await testingRepos.createMember();
    await testingRepos.templates.createLegalOperationRequiredTemplates('OPERATION__IMMOBILIER__VENTE_ANCIEN');
    await testingRepos.templates.createTemplate('RECORD__PERSONNE__PHYSIQUE');

    return {
      client,
      getService,
      member,
      testingRepos
    };
  }

  /**
   * Create a member, a first user, and an operation with vendeur and mandat records, folders and files
   */
  async function setupOperation() {
    const { client, getService, member, testingRepos } = await setup();

    const firstUser = await testingRepos.users.createUser();

    const LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS = 'LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS';
    const LINK__OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES = 'LINK__OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES';
    const LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS = 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS';
    const VENDEUR_BRANCH_TYPE = 'VENDEUR';
    const MANDAT_BRANCH_TYPE = 'MANDAT';
    const OFFRANT_BRANCH_TYPE = 'OFFRANT';

    const answerPhysique = {
      email: {
        locked: true,
        value: '<EMAIL>'
      }
    };

    const operation = await testingRepos.operations.createOperation({
      labelOperation: 'operation',
      labelPattern: 'Vente ancien',
      organizationId: member.organizationId,
      templateId: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
      userId: firstUser.id
    });

    const newFile1 = await testingRepos.files.createFile();
    const newFile2 = await testingRepos.files.createFile();

    const folder = await testingRepos.drives.createDriveFolder({
      label: 'folder',
      operationId: operation.id,
      userId: firstUser.id
    });

    await testingRepos.drives.createDriveFile({
      documentLabel: 'document_label',
      fileId: newFile1.id,
      folderId: folder.driveFolderId,
      operationId: operation.id,
      userId: firstUser.id
    });

    await testingRepos.drives.createDriveFile({
      fileId: newFile2.id,
      operationId: operation.id,
      userId: firstUser.id
    });

    const operationToDuplicate = await testingRepos.operations.selectOperation({
      operationId: operation.id
    });

    const vendeurRecord = await testingRepos.records.createRecord({
      answer: answerPhysique,
      organizationId: member.organizationId,
      templateId: 'RECORD__PERSONNE__PHYSIQUE',
      userId: member.userId
    });

    const mandatRecord = await testingRepos.records.createRecord({
      answer: {
        contrat_numero_registre: { locked: true, value: '1' },
        mandat_numero: { locked: true, value: '4' },
        mandat_numero_gestion: { locked: true, value: '2' },
        mandat_numero_transaction: { locked: true, value: '3' },
        mandat_vente_calcul: { locked: true, value: 'recherche_pourcentage' }
      },
      organizationId: member.organizationId,
      templateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__MANDAT',
      userId: member.userId
    });

    const branchVendeur = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: operation.id,
      legalLinkTemplateId: LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS,
      organizationId: member.organizationId,
      toId: vendeurRecord.id,
      type: VENDEUR_BRANCH_TYPE,
      userId: firstUser.id
    });

    const branchMandat = await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: operation.id,
      legalLinkTemplateId: LINK__OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES,
      organizationId: member.organizationId,
      toId: mandatRecord.id,
      type: MANDAT_BRANCH_TYPE,
      userId: firstUser.id
    });

    await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: operation.id,
      legalLinkTemplateId: LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS,
      organizationId: member.organizationId,
      type: OFFRANT_BRANCH_TYPE,
      userId: firstUser.id
    });

    await testingRepos.legalBranches.createLegalBranch({
      fromLegalId: operation.id,
      legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRE',
      organizationId: member.organizationId,
      type: 'FICHE_OFFRE',
      userId: firstUser.id
    });

    return {
      branchMandat,
      branchVendeur,
      client,
      firstUser,
      folder,
      getService,
      mandatRecord,
      member,
      newFile1,
      newFile2,
      operation,
      operationToDuplicate,
      testingRepos,
      vendeurRecord
    };
  }
});
