import { assertIsAnswer } from '@mynotary/crossplatform/records/api';
import {
  Body,
  Controller,
  Delete,
  Get,
  Param,
  Post,
  Put,
  Query,
  UnprocessableEntityException,
  UseGuards
} from '@nestjs/common';
import {
  LegalRecordDto,
  RecordFormDescriptionDto,
  RecordNewDto,
  RecordUpdateDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { LegalRecordsService, LegalsService } from '@mynotary/backend/legals/core';
import {
  getLegalRecordFormDescription,
  getLegalRecordTemplate
} from '@mynotary/crossplatform/legal-record-templates/api';
import { bodyResolver, pathResolver } from '@mynotary/backend/shared/auth-util';
import { some } from 'lodash';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { HasOrganizationAccess, IsGcpApplication, IsSelf, OrGuard } from '@mynotary/backend/authorizations/api';
import {
  HasLegal<PERSON>ecordAccess,
  HasLegalRecordAnonymousAccess,
  HasOrganizationAnonymousAccess
} from '@mynotary/backend/legals/authorization';

@Controller()
export class RecordsController {
  constructor(
    private recordsService: LegalRecordsService,
    private legalsService: LegalsService
  ) {}

  @UseGuards(
    OrGuard([
      [
        IsSelf({ userIdResolver: bodyResolver('creatorId') }),
        HasOrganizationAccess({ organizationIdResolver: bodyResolver('organizationId') })
      ],
      [HasOrganizationAnonymousAccess()],
      [IsGcpApplication()]
    ])
  )
  @Post('/records')
  async createRecord(@Body() body: RecordNewDto): Promise<LegalRecordDto> {
    const legalRecordTemplate = getLegalRecordTemplate(body.templateId);

    if (legalRecordTemplate == null) {
      throw new UnprocessableEntityException('Invalid templateId');
    }
    return await this.recordsService.createRecord({ ...body, templateId: legalRecordTemplate.id });
  }

  @UseGuards(
    OrGuard([
      [HasLegalRecordAccess()],
      [
        HasLegalRecordAnonymousAccess({
          legalRecordIdResolver: pathResolver('recordId')
        })
      ]
    ])
  )
  @Put('/records/:recordId')
  async updateRecord(@Param('recordId') recordId: string, @Body() body: RecordUpdateDto): Promise<void> {
    if (body.answer != null) {
      assertIsAnswer(body.answer);
    }

    if (body.templateId != null) {
      const legalRecordTemplate = getLegalRecordTemplate(body.templateId);

      if (legalRecordTemplate == null) {
        throw new UnprocessableEntityException('Invalid templateId');
      }

      await this.recordsService.updateLegalRecordTemplateId({
        legalRecordId: recordId,
        legalRecordTemplateId: legalRecordTemplate.id
      });
    }

    if (body.answer != null) {
      await this.recordsService.updateLegalRecordAnswer({
        answer: body.answer,
        legalRecordId: recordId
      });
    }
  }

  @UseGuards(HasLegalRecordAccess({ permissionType: PermissionType.DELETE_ORGANIZATION_RECORDS }))
  @Delete('/records/:recordId')
  async deleteRecord(@Param('recordId') recordId: string): Promise<void> {
    await this.recordsService.deleteRecord(recordId);
  }

  @UseGuards(HasLegalRecordAccess({ legalRecordIdResolver: pathResolver('legalRecordId') }))
  @Get('/legal-record-data/:legalRecordId')
  async getLegalData(@Param('legalRecordId') legalRecordId: string) {
    const record = await this.recordsService.getLegalRecord(legalRecordId);
    const legalData = await this.legalsService.getLegalData({ legalId: legalRecordId });

    if (!some(legalData.legalRecords, (r) => r.id === record.id)) {
      legalData.legalRecords.push(record);
    }
    return legalData;
  }

  @UseGuards(IsGcpApplication())
  @Get('/legal-record-infos')
  async getLegalRecordDescription(
    @Query('legalRecordTemplateId') legalRecordTemplateId: string
  ): Promise<RecordFormDescriptionDto> {
    const legalRecordTemplate = getLegalRecordTemplate(legalRecordTemplateId);

    if (legalRecordTemplate == null) {
      throw new UnprocessableEntityException('Invalid templateId');
    }

    return getLegalRecordFormDescription(legalRecordTemplate.id);
  }
}
