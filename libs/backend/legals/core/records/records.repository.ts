import { RecordNew } from './record';
import { AnswerDict, LegalRecord } from '@mynotary/crossplatform/records/api';
import { LegalRecordTemplateId } from '@mynotary/crossplatform/legal-templates/api';

export abstract class RecordsRepository {
  abstract getLegalRecords(recordIds: string[]): Promise<LegalRecord[]>;

  abstract createRecord(record: RecordNew): Promise<{ id: string }>;

  abstract updateLegalRecordAnswer(record: UpdateLegalRecordAnswerArgs): Promise<void>;

  abstract deleteRecord(recordId: string): Promise<void>;

  abstract updateLegalRecordTemplateId(args: UpdateLegalRecordTemplateId): Promise<void>;
}

export interface UpdateLegalRecordTemplateId {
  legalRecordId: string;
  legalRecordTemplateId: LegalRecordTemplateId;
}

export interface UpdateLegalRecordAnswerArgs {
  answer: AnswerDict;
  legalRecordId: string;
}
