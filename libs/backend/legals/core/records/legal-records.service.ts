import { Injectable } from '@nestjs/common';
import { RecordNew } from './record';
import { RecordsRepository, UpdateLegalRecordTemplateId, UpdateLegalRecordAnswerArgs } from './records.repository';
import { CannotDeleteDefaultRecordError, RecordTemplateMismatchError } from './records.error';
import { isPersonTemplateId, isPropertyTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { LegalRecord } from '@mynotary/crossplatform/records/api';
import { NotFoundError } from '@mynotary/crossplatform/shared/util';
import { DefaultRecordsApiService } from '@mynotary/backend/default-records/api';

@Injectable()
export class LegalRecordsService {
  constructor(
    private recordsRepository: RecordsRepository,
    private defaultRecordsApiService: DefaultRecordsApiService
  ) {}

  async createRecord(recordNew: RecordNew): Promise<LegalRecord> {
    const newRecord = await this.recordsRepository.createRecord(recordNew);
    return await this.getLegalRecord(newRecord.id);
  }

  async getLegalRecords(legalRecordIds: string[]) {
    return await this.recordsRepository.getLegalRecords(legalRecordIds);
  }

  async getLegalRecord(legalRecordId: string) {
    const legalRecords = await this.recordsRepository.getLegalRecords([legalRecordId]);

    if (legalRecords.length === 0) {
      throw new NotFoundError({ id: legalRecordId, resource: 'LegalRecord' });
    }

    return legalRecords[0];
  }

  async updateLegalRecordTemplateId(args: UpdateLegalRecordTemplateId): Promise<void> {
    const currentRecord = await this.getLegalRecord(args.legalRecordId);
    if (!checkIsValidTemplate(currentRecord, args.legalRecordTemplateId)) {
      throw new RecordTemplateMismatchError({ recordId: args.legalRecordId, templateId: args.legalRecordTemplateId });
    }

    await this.recordsRepository.updateLegalRecordTemplateId(args);
  }

  async updateLegalRecordAnswer(args: UpdateLegalRecordAnswerArgs): Promise<void> {
    await this.recordsRepository.updateLegalRecordAnswer(args);
  }

  async deleteRecord(recordId: string): Promise<void> {
    const defaultRecords = await this.defaultRecordsApiService.getDefaultRecords({ recordId });

    if (defaultRecords.length > 0) {
      throw new CannotDeleteDefaultRecordError({ recordId });
    }

    await this.recordsRepository.deleteRecord(recordId);
  }
}

function checkIsValidTemplate(record: LegalRecord, targetTemplateId: string): boolean {
  return (
    (isPersonTemplateId(record.templateId) && isPersonTemplateId(targetTemplateId)) ||
    (isPropertyTemplateId(record.templateId) && isPropertyTemplateId(targetTemplateId))
  );
}
