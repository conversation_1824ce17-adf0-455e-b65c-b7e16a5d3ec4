import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { LegalRecordTemplateId } from '@mynotary/crossplatform/legal-templates/api';

export interface RecordNew {
  answer?: AnswerDict;
  creatorId: string;
  organizationId: string;
  templateId: LegalRecordTemplateId;
}

export interface Answer {
  locked?: boolean;
  value?: AnswerValue;
}

export type AnswerValue = Array<unknown> | number | object | string | null;
