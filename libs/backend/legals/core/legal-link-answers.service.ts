import { Injectable } from '@nestjs/common';
import {
  containsSpecificTypes,
  isLegalRecordTemplateId,
  LegalTemplateId
} from '@mynotary/crossplatform/legal-templates/api';
import { getLegalRecordTemplate } from '@mynotary/crossplatform/legal-record-templates/api';
import { getLegalLinkTemplate } from '@mynotary/crossplatform/legal-link-templates/api';
import { generateLegalLinkQuestionId, isOneSidedBranch } from '@mynotary/crossplatform/legal-links/api';
import { find } from 'lodash';
import { LegalRecordsService } from './records';

@Injectable()
export class LegalLinkAnswersService {
  constructor(private legalRecordsService: LegalRecordsService) {}

  /**
   * Update the answer of the legal link question.
   * Things to know about legal links question and answers:
   * - This answer is used to hold the type of link (eg: "mariage" for a "situation maritale" link).
   * See {@link generateLegalLinkQuestionId} for more information about why we use a question id.
   * - Target record config link can be null when the target record don't have this link type. eg: a representation is
   * between a "personne morale" and a "personne physique", but the answer on the personne physique don't need to be
   * updated since the link is not in the "personne physique" template.
   * - If the link is "one sided" (eg: representant and represente), we don't update the answer on the target record.
   * {@link isOneSidedBranch} for more info.
   */
  async updateLegalLinkQuestion(args: {
    branchType: string;
    contractId?: string;
    legalLinkTemplateId: string;
    legalRecordId?: string;
    legalRecordTemplateId?: LegalTemplateId;
    shouldDelete?: boolean;
  }) {
    if (!args.legalRecordId || !isLegalRecordTemplateId(args.legalRecordTemplateId)) {
      return;
    }

    const legalRecordTemplate = getLegalRecordTemplate(args.legalRecordTemplateId);
    const legalLinkTemplate = getLegalLinkTemplate(args.legalLinkTemplateId);

    const targetRecordConfigLink = find(legalRecordTemplate.config.recordLinks, (recordConfigLink) => {
      return containsSpecificTypes(recordConfigLink.specificTypes, legalLinkTemplate.specificTypes);
    });

    if (targetRecordConfigLink != null) {
      const questionId = generateLegalLinkQuestionId({
        contractId: args.contractId,
        recordConfigLink: targetRecordConfigLink
      });

      if (!isOneSidedBranch({ branchType: args.branchType, legalLinkTemplateId: legalLinkTemplate.id })) {
        await this.legalRecordsService.updateLegalRecordAnswer({
          answer: { [questionId]: { value: args.shouldDelete ? null : legalLinkTemplate.id } },
          legalRecordId: args.legalRecordId
        });
      }
    }
  }
}
