import { TaskReminderType, TaskType } from '@mynotary/crossplatform/legals/core';
import { TaskNotificationType } from '@mynotary/crossplatform/notifications/api';

export interface Task {
  assignees: TaskAssignee[];
  completionDate?: string;
  contractId?: string;
  creationTime: string;
  creatorEmail?: string;
  creatorFirstname: string;
  creatorId: string;
  creatorLastname: string;
  description: string;
  dueDate?: string;
  id: string;
  operationId: string;
  title: string;
  type: TaskType;
  uuid?: string;
}

export type TaskReminder = Task & {
  creatorEmail: string;
  creatorOrganizationName: string;
  creatorPhone: string;
  description: string;
  organizationId: string;
  parentOperationId?: string;
};

export type ReminderTask =
  | TaskNotificationType.TASK_CUSTOM_REMINDER
  | TaskNotificationType.TASK_DOCUMENT_REQUEST_REMINDER
  | TaskNotificationType.TASK_DOWNLOAD_FILES_REMINDER
  | TaskNotificationType.TASK_FILL_OPERATION_RECORDS_REMINDER
  | TaskNotificationType.TASK_READ_CONTRACT_PROJECT_REMINDER
  | TaskNotificationType.TASK_SHARE_OPERATION_RECORDS_REMINDER;

export interface TaskAssignee {
  email: string;
  firstname?: string;
  id?: string;
  lastname?: string;
  phone?: string;
}

export function getExpirableTaskTypes(): TaskType[] {
  return [
    TaskType.CUSTOM,
    TaskType.DOWNLOAD_FILES,
    TaskType.DOWNLOAD_FILES_OPERATION,
    TaskType.FILL_OPERATION_RECORDS,
    TaskType.SHARE_OPERATION_RECORD,
    TaskType.SCAN_DOCUMENT,
    TaskType.DOCUMENT_REQUEST,
    TaskType.READ_PROJECT_CONTRACT
  ];
}

export function isExpirableTask(task: Task): boolean {
  return getExpirableTaskTypes().includes(task.type);
}

export type TaskNew = TaskPaymentNew | TaskValidationNew | TaskReviewNew;

export type TaskPaymentNew = { orderId: string; type: TaskType.PAYMENT_REQUEST } & TaskCommonFields;

export type TaskValidationNew = { contractId: string; type: TaskType.VALIDATE_CONTRACT } & TaskCommonFields;

export type TaskReviewNew = { contractId: string; type: TaskType.REVIEW_CONTRACT } & TaskCommonFields;

type TaskCommonFields = {
  contractId?: string;
  creatorUserId: string;
  description: string;
  dueDate?: string;
  legalComponentId: string;
  organizationId: string;
  reminderType?: TaskReminderType;
  title: string;
} & TaskNewShare;

export type TaskNewShare = TaskNewWithEmailShare | TaskNewWithoutShare;

export type TaskNewWithEmailShare = {
  assignees: string[];
  email: TaskEmail;
};

// eslint-disable-next-line @typescript-eslint/no-empty-object-type
export type TaskNewWithoutShare = {};

export interface TaskEmail {
  content: string;
  subject: string;
}

export function isEmailSharedTask(task: TaskNewShare): task is TaskNewWithEmailShare {
  return 'email' in task;
}
