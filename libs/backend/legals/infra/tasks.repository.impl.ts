import { Prisma, PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { Injectable } from '@nestjs/common';
import {
  DateRange,
  GetTaskArgs,
  isEmailSharedTask,
  Task,
  TaskNew,
  TaskPaymentNew,
  TaskReminder,
  TaskReviewNew,
  TasksRepository,
  TaskValidationNew,
  UpdateTaskArgs
} from '@mynotary/backend/legals/core';
import { TaskType } from '@mynotary/crossplatform/legals/core';
import { assertNotNull, convertEnum, Exception, JSONValue, NotFoundError } from '@mynotary/crossplatform/shared/util';
import { fns } from '@mynotary/crossplatform/shared/dates-util';
import { LegacyJavaClient } from '@mynotary/backend/legacy-java/api';
import { JavaTaskType, TaskJava, TaskNewJavaDto } from '@mynotary/backend/legacy-java/api';
import { v4 as uuid } from 'uuid';

@Injectable()
export class TasksRepositoryImpl implements TasksRepository {
  constructor(
    private prisma: PrismaService,
    private legacyJavaClient: LegacyJavaClient
  ) {}

  async getTasksToRemind(): Promise<TaskReminder[]> {
    const todayUTC = new Date(); // Current date in UTC
    todayUTC.setUTCHours(0, 0, 0, 0); // Reset time to midnight UTC

    const oneDayBeforeStart = new Date(todayUTC);
    oneDayBeforeStart.setUTCDate(todayUTC.getUTCDate() + 1);
    const oneDayBeforeEnd = new Date(oneDayBeforeStart);
    oneDayBeforeEnd.setUTCDate(oneDayBeforeEnd.getUTCDate() + 1);

    const threeDaysBeforeStart = new Date(todayUTC);
    threeDaysBeforeStart.setUTCDate(todayUTC.getUTCDate() + 3);
    const threeDaysBeforeEnd = new Date(threeDaysBeforeStart);
    threeDaysBeforeEnd.setUTCDate(threeDaysBeforeEnd.getUTCDate() + 1);

    const fiveDaysBeforeStart = new Date(todayUTC);
    fiveDaysBeforeStart.setUTCDate(todayUTC.getUTCDate() + 5);
    const fiveDaysBeforeEnd = new Date(fiveDaysBeforeStart);
    fiveDaysBeforeEnd.setUTCDate(fiveDaysBeforeEnd.getUTCDate() + 1);

    const tasks = await this.prisma.task.findMany({
      distinct: ['id'],
      select: SELECTED_DB_TASK_FIELDS,
      where: {
        OR: [
          {
            AND: [
              { reminder_type: 'ONE_DAY_BEFORE' },
              {
                due_date: {
                  gte: oneDayBeforeStart.toISOString(),
                  lt: oneDayBeforeEnd.toISOString()
                }
              }
            ]
          },
          {
            AND: [
              { reminder_type: 'THREE_DAYS_BEFORE' },
              {
                due_date: {
                  gte: threeDaysBeforeStart.toISOString(),
                  lt: threeDaysBeforeEnd.toISOString()
                }
              }
            ]
          },
          {
            AND: [
              { reminder_type: 'FIVE_DAYS_BEFORE' },
              {
                due_date: {
                  gte: fiveDaysBeforeStart.toISOString(),
                  lt: fiveDaysBeforeEnd.toISOString()
                }
              }
            ]
          }
        ],
        completion_time: null
      }
    });

    const taskInfos = tasks.map(async (task) => {
      assertNotNull(task.legal_component.organization?.name, 'organization');
      const parentOperationId = task.legal_component.legal_component_operation?.parent_operation_id;

      const assignees = await this.findAssigneeUserId(task.task_assignee.map((assignee) => assignee.email));

      return {
        assignees: assignees.map((assignee) => ({
          email: assignee.email,
          id: assignee.id?.toString()
        })),
        creationTime: task.creation_time.toISOString(),
        creatorEmail: task.user.email,
        creatorFirstname: task.user.firstname,
        creatorId: task.creator_user_id.toString(),
        creatorLastname: task.user.lastname,
        creatorOrganizationName: task.legal_component.organization.name,
        creatorPhone: task.user.phone ?? '',
        description: task.description ?? '',
        dueDate: task.due_date ? fns.format(task.due_date, 'EEEE d MMMM yyyy') : undefined,
        id: task.id.toString(),
        operationId: task.legal_component.id.toString(),
        organizationId: task.legal_component.organization.id.toString(),
        parentOperationId: parentOperationId != null ? parentOperationId.toString() : undefined,
        title: task.title,
        type: convertEnum(TaskType, task.type),
        uuid: task.uuid ?? undefined
      };
    });

    return await Promise.all(taskInfos);
  }

  async getTasks(args: GetTaskArgs): Promise<Task[]> {
    const dbTasks: DbTask[] = await this.prisma.task.findMany({
      select: SELECTED_DB_TASK_FIELDS,
      where: {
        completion_time: sortDateByRange(args.completionDate),
        due_date: sortDateByRange(args.dueDate),
        reference:
          args.contractId != null
            ? {
                equals: parseInt(args.contractId),
                path: ['contractId']
              }
            : undefined,
        type:
          args.types != null
            ? {
                in: args.types
              }
            : undefined
      }
    });

    return dbTasks.map(convertDbToTask);
  }

  public async createTask(task: TaskNew): Promise<Task> {
    switch (task.type) {
      case TaskType.PAYMENT_REQUEST:
        return this.createJavaTask(task);
      case TaskType.VALIDATE_CONTRACT:
      case TaskType.REVIEW_CONTRACT:
        return this.createTaskDb(task);
    }
  }

  private async createJavaTask(task: TaskPaymentNew): Promise<Task> {
    const javaDto = convertTaskNewToTaskNewJavaDto(task);
    const data = await this.legacyJavaClient.createTask(javaDto);
    return convertTaskJavaToTask(data);
  }

  async createTaskDb(task: TaskValidationNew | TaskReviewNew): Promise<Task> {
    const { creatorUserId, description, legalComponentId, title, type } = task;

    return this.prisma.$transaction(async (prisma) => {
      const createdTask = await prisma.task.create({
        data: {
          creator_user_id: parseInt(creatorUserId),
          description,
          due_date: task.dueDate != null ? new Date(task.dueDate) : null,
          legal_component_id: parseInt(legalComponentId),
          reference: convertToDbTaskReference(task),
          title,
          type,
          uuid: createTaskUUID(task.type)
        },
        select: SELECTED_DB_TASK_FIELDS
      });

      const hasAssignee = isEmailSharedTask(task);

      if (hasAssignee) {
        await prisma.task_assignee.createMany({
          data: task.assignees.map((assignee) => ({
            email: assignee,
            task_id: createdTask.id
          }))
        });
      }

      return {
        ...convertDbToTask(createdTask),
        assignees: hasAssignee ? task.assignees.map((assignee) => ({ email: assignee })) : []
      };
    });
  }

  async updateTask(args: UpdateTaskArgs): Promise<void> {
    const { completionTime, expirationTime, id } = args;

    await this.prisma.task.update({
      data: {
        completion_time: completionTime == null ? completionTime : new Date(completionTime),
        expiration_time: expirationTime ? new Date(expirationTime) : undefined
      },
      where: {
        id: parseInt(id)
      }
    });
  }

  async deleteTask(taskId: string): Promise<boolean> {
    const task = await this.getTask(taskId);
    switch (task.type) {
      case TaskType.PAYMENT_REQUEST:
        await this.legacyJavaClient.deleteTask({
          legalComponentId: task.legalComponentId,
          taskId: parseInt(taskId)
        });
        return true;
      case TaskType.VALIDATE_CONTRACT:
      case TaskType.REVIEW_CONTRACT:
        await this.deleteTaskDb(taskId);
        return true;
      case TaskType.CUSTOM:
      case TaskType.DOWNLOAD_FILES:
      case TaskType.DOWNLOAD_FILES_OPERATION:
      case TaskType.FILL_OPERATION_RECORDS:
      case TaskType.SHARE_OPERATION_RECORD:
      case TaskType.DOCUMENT_REQUEST:
      case TaskType.READ_PROJECT_CONTRACT:
      case TaskType.SCAN_DOCUMENT:
        throw new Exception('Not implemented');
    }
  }

  private async findAssigneeUserId(emails: string[]): Promise<{ email: string; id?: number }[]> {
    const assignees = await this.prisma.user.findMany({
      select: {
        email: true,
        id: true
      },
      where: {
        email: {
          in: emails
        }
      }
    });

    return emails.map((email) => {
      const assignee = assignees.find((assignee) => assignee.email === email);
      return {
        email,
        id: assignee?.id
      };
    });
  }

  private async getTask(taskId: string) {
    const task = await this.prisma.task.findUnique({
      select: {
        legal_component_id: true,
        type: true,
        uuid: true
      },
      where: {
        id: parseInt(taskId)
      }
    });

    if (task == null) {
      throw new NotFoundError({ id: taskId, resource: 'task' });
    }

    return {
      legalComponentId: task.legal_component_id,
      taskUuid: task.uuid,
      type: convertEnum(TaskType, task.type)
    };
  }

  private async deleteTaskDb(taskId: string) {
    await this.prisma.$transaction(async (prisma) => {
      await prisma.task_assignee.deleteMany({
        where: {
          task_id: parseInt(taskId)
        }
      });
      await prisma.task.delete({
        where: {
          id: parseInt(taskId)
        }
      });
    });
  }
}

function sortDateByRange(dateRange?: DateRange | null): { gte?: string; lte?: string } | undefined {
  if (dateRange == null) {
    return undefined;
  }
  return {
    gte: dateRange.fromDate,
    lte: dateRange.toDate
  };
}

function convertDbToTask(dbTask: DbTask): Task {
  const reference = dbTask.reference as JSONValue;
  const contractId = reference?.contractId != null ? reference?.contractId.toString() : undefined;

  return {
    assignees: dbTask.task_assignee.map((dbAssignee) => ({
      email: dbAssignee.email
    })),
    completionDate: dbTask.completion_time?.toISOString() ?? undefined,
    contractId,
    creationTime: dbTask.creation_time.toISOString(),
    creatorEmail: dbTask.user.email,
    creatorFirstname: dbTask.user.firstname,
    creatorId: dbTask.creator_user_id.toString(),
    creatorLastname: dbTask.user.lastname,
    description: dbTask.description ?? '',
    dueDate: dbTask.due_date?.toISOString() ?? undefined,
    id: dbTask.id.toString(),
    operationId: dbTask.legal_component_id.toString(),
    title: dbTask.title,
    type: convertEnum(TaskType, dbTask.type),
    uuid: dbTask.uuid ?? undefined
  };
}

const SELECTED_DB_TASK_FIELDS = Prisma.validator<Prisma.taskSelect>()({
  completion_time: true,
  creation_time: true,
  creator_user_id: true,
  description: true,
  due_date: true,
  id: true,
  legal_component: {
    select: {
      id: true,
      legal_component_operation: {
        select: {
          parent_operation_id: true
        }
      },
      organization: {
        select: {
          id: true,
          name: true
        }
      }
    }
  },
  legal_component_id: true,
  reference: true,
  reminder_type: true,
  task_assignee: {
    select: {
      email: true
    }
  },
  title: true,
  type: true,
  user: {
    select: {
      email: true,
      firstname: true,
      lastname: true,
      phone: true
    }
  },
  uuid: true
});

type DbTask = Prisma.taskGetPayload<{ select: typeof SELECTED_DB_TASK_FIELDS }>;

function convertTaskNewToTaskNewJavaDto(task: TaskPaymentNew): TaskNewJavaDto {
  const taskFields = shareField(task);
  return {
    ...taskFields,
    contractId: task.contractId,
    creatorUserId: parseInt(task.creatorUserId),
    description: task.description,
    dueDate: task.dueDate != null ? new Date(task.dueDate).getTime() : undefined,
    legalComponentId: parseInt(task.legalComponentId),
    organizationId: parseInt(task.organizationId),
    reference: { orderId: task.orderId },
    reminderType: task.reminderType,
    title: task.title,
    type: JavaTaskType.PAYMENT_REQUEST
  };
}

function convertTaskJavaToTask(task: TaskJava): Task {
  return {
    assignees: task.assignees.map((assignee) => ({
      email: assignee.user.email
    })),
    contractId: task.contractId?.toString() ?? undefined,
    creationTime: new Date(task.creationTime).toISOString(),
    creatorEmail: task.creatorUser.email,
    creatorFirstname: task.creatorUser.firstname,
    creatorId: task.creatorUserId.toString(),
    creatorLastname: task.creatorUser.lastname,
    description: task.description,
    dueDate: task.dueDate != null ? new Date(task.dueDate).toISOString() : undefined,
    id: task.id.toString(),
    operationId: task.legalComponentId.toString(),
    title: task.title,
    type: convertEnum(TaskType, task.type),
    uuid: task.uuid
  };
}

function shareField(task: TaskNew) {
  if (isEmailSharedTask(task)) {
    return {
      assignees: task.assignees,
      email: {
        content: task.email.content,
        subject: task.email.subject
      }
    };
  } else {
    return {
      assignees: [],
      email: null
    };
  }
}

function convertToDbTaskReference<K extends keyof DbTaskReferenceMap>(
  taskNew: Extract<TaskNew, { type: K }>
): DbTaskReferenceMap[K] {
  const converter = taskReferenceConverter[taskNew.type] as (
    task: Extract<TaskNew, { type: K }>
  ) => DbTaskReferenceMap[K];

  return converter(taskNew);
}

type DbTaskReferenceMap = {
  [TaskType.VALIDATE_CONTRACT]: { contractId: number };
  [TaskType.REVIEW_CONTRACT]: { contractId: number };
  [TaskType.PAYMENT_REQUEST]: { orderId: string };
};

const taskReferenceConverter: {
  [K in keyof DbTaskReferenceMap]: (taskNew: Extract<TaskNew, { type: K }>) => DbTaskReferenceMap[K];
} = {
  [TaskType.VALIDATE_CONTRACT]: (taskNew) => ({ contractId: parseInt(taskNew.contractId) }),
  [TaskType.REVIEW_CONTRACT]: (taskNew) => ({ contractId: parseInt(taskNew.contractId) }),
  [TaskType.PAYMENT_REQUEST]: (taskNew) => ({ orderId: taskNew.orderId })
};

function createTaskUUID(taskType: TaskType): string | undefined {
  return [
    TaskType.FILL_OPERATION_RECORDS,
    TaskType.SHARE_OPERATION_RECORD,
    TaskType.DOWNLOAD_FILES,
    TaskType.DOWNLOAD_FILES_OPERATION,
    TaskType.SCAN_DOCUMENT,
    TaskType.DOCUMENT_REQUEST,
    TaskType.READ_PROJECT_CONTRACT,
    TaskType.PAYMENT_REQUEST
  ].includes(taskType)
    ? uuid()
    : undefined;
}
