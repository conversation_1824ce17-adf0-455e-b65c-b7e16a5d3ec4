import { answer, legal_component, legal_component_record, PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { AnswerDict, assertIsAnswer, LegalRecord } from '@mynotary/crossplatform/records/api';
import { Injectable } from '@nestjs/common';
import {
  RecordsRepository,
  RecordNew,
  UpdateLegalRecordAnswerArgs,
  UpdateLegalRecordTemplateId,
  mergeAnswers
} from '@mynotary/backend/legals/core';
import { NotFoundError } from '@mynotary/crossplatform/shared/util';
import { LegalRecordTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { map, parseInt, uniq } from 'lodash';
import { InternalEntityType } from '@mynotary/backend/external-apps/api';

@Injectable()
export class RecordsRepositoryImpl implements RecordsRepository {
  constructor(protected prisma: PrismaService) {}

  async getLegalRecords(recordIds: string[]): Promise<LegalRecord[]> {
    const dbRecords = await this.prisma.legal_component.findMany({
      include: {
        legal_component_record: {
          include: {
            answer: true
          }
        },
        user: {
          select: {
            firstname: true,
            id: true,
            lastname: true
          }
        }
      },
      where: {
        id: {
          in: uniq(recordIds.map((id) => parseInt(id)))
        }
      }
    });

    return map(dbRecords, (dbRecord) => dbRecordToRecord(dbRecord));
  }

  async createRecord(newRecord: RecordNew) {
    const template = await this.prisma.legal_component_template.findUnique({
      select: {
        id: true
      },
      where: {
        id_str: newRecord.templateId
      }
    });

    if (template == null) {
      throw new NotFoundError({ id: newRecord.templateId, resource: 'Template' });
    }

    const record = await this.prisma.legal_component.create({
      data: {
        creator_user_id: parseInt(newRecord.creatorId),
        organization_id: parseInt(newRecord.organizationId),
        template_id: template.id,
        template_id_str: newRecord.templateId
      }
    });

    const answer = await this.prisma.answer.create({
      data: {
        /**
         * @hack force type to JsonObject which is not compatible with AnswerDict
         * for some awkward reason.
         */
        // eslint-disable-next-line @typescript-eslint/no-explicit-any
        answer: newRecord.answer as any
      }
    });
    await this.prisma.legal_component_record.create({
      data: {
        answer_id: answer.id,
        legal_component_id: record.id
      }
    });

    return {
      id: record.id.toString()
    };
  }

  /**
   * Updates a legal record's answer data while preventing data loss from concurrent modifications.
   *
   * Problem Solved:
   * This function addresses a data loss issue that occurs when multiple concurrent updates are made
   * to the same legal record. This typically happens when users select multiple documents and upload
   * the same file simultaneously.
   *
   * Root Cause:
   * When two different attributes (e.g., attribut_1 and attribut_2) are added to the answer object
   * concurrently, both requests start by selecting the current answer state. Each request sees an
   * incomplete version missing the other's attribute. The last request to complete overwrites the
   * previous changes, resulting in data loss.
   *
   * Solution Implemented:
   * The function uses FOR UPDATE within the same SQL transaction to ensure that parallel requests
   * wait for each other to complete.
   */
  async updateLegalRecordAnswer(args: UpdateLegalRecordAnswerArgs): Promise<void> {
    await this.prisma.$transaction(async (prisma) => {
      const answer = await prisma.$queryRaw<{ answer: AnswerDict; id: number }[]>`
        SELECT a.id, a.answer FROM legal_component_record lcr
        INNER JOIN public.answer a ON a.id = lcr.answer_id
        WHERE legal_component_id=${parseInt(args.legalRecordId)} FOR UPDATE;
    `;

      if (answer == null || answer.length === 0) {
        throw new NotFoundError({ id: args.legalRecordId, resource: 'LegalRecord' });
      }

      const mergedAnswer = mergeAnswers(answer[0].answer, args.answer);

      await prisma.legal_component_record.update({
        data: {
          /**
           * @hack force type to JsonObject which is not compatible with AnswerDict
           * for some awkward reason.
           */
          // eslint-disable-next-line @typescript-eslint/no-explicit-any
          answer: { update: { answer: mergedAnswer as any } }
        },
        where: { legal_component_id: parseInt(args.legalRecordId) }
      });
    });
  }

  async updateLegalRecordTemplateId(args: UpdateLegalRecordTemplateId): Promise<void> {
    const updatedTemplate = await this.prisma.legal_component_template.findUniqueOrThrow({
      select: { id: true, id_str: true },
      where: { id_str: args.legalRecordTemplateId }
    });

    await this.prisma.legal_component.update({
      data: {
        template_id: updatedTemplate.id,
        template_id_str: updatedTemplate.id_str
      },
      where: { id: parseInt(args.legalRecordId) }
    });
  }

  async deleteRecord(recordId: string): Promise<void> {
    await this.prisma.$transaction(async (prisma) => {
      await prisma.legal_component.update({
        data: {
          deleted: true
        },
        where: {
          id: parseInt(recordId)
        }
      });

      const branches = await prisma.legal_component_branch.findMany({
        select: {
          id: true,
          link_id: true
        },
        where: {
          OR: [{ from_id: parseInt(recordId) }, { to_id: parseInt(recordId) }]
        }
      });

      await prisma.legal_component_branch.deleteMany({
        where: {
          id: {
            in: branches.map((branch) => branch.id)
          }
        }
      });

      for (const linkId of uniq(branches.map((branch) => branch.link_id))) {
        const remainingBranches = await prisma.legal_component_branch.findMany({ where: { link_id: linkId } });
        if (!remainingBranches || remainingBranches.length === 0) {
          await prisma.legal_component_link.delete({ where: { legal_component_id: linkId } });
          await prisma.legal_component.delete({ where: { id: linkId } });
        }
      }

      await prisma.internal_api_association.deleteMany({
        where: {
          from: recordId.toString(),
          internal_type: InternalEntityType.LEGAL_COMPONENT
        }
      });
    });
  }
}

function dbRecordToRecord(dbRecord: DbRecord): LegalRecord {
  const answer = dbRecord.legal_component_record?.answer.answer;

  assertIsAnswer(answer);

  return {
    answer,
    answerId: dbRecord.legal_component_record?.answer.id.toString() ?? '',
    createdAt: dbRecord.creation_time.toISOString(),
    creator: {
      firstname: dbRecord.user?.firstname.toString() ?? '',
      id: dbRecord.user?.id.toString() ?? '',
      lastname: dbRecord.user?.lastname.toString() ?? ''
    },
    id: dbRecord.id.toString(),
    organizationId: dbRecord?.organization_id?.toString() ?? '',
    templateId: dbRecord.template_id_str as LegalRecordTemplateId
  };
}

type DbRecord = legal_component & {
  legal_component_record: (legal_component_record & { answer: answer }) | null;
  user: DbRecordCreator | null;
};

type DbRecordCreator = { firstname: string; id: number; lastname: string };
