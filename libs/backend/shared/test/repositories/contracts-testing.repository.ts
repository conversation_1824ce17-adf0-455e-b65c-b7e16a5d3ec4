import { PrismaService } from '@mynotary/backend/shared/prisma-infra';
import { SubEntity } from '@mynotary/crossplatform/roles/api';

import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { FileInfo } from '@mynotary/crossplatform/files-client/api';

export class ContractsTestingRepository {
  constructor(private prisma: PrismaService) {}

  async createMandat(args: { operationId?: string; status?: string; userId: string }) {
    const contract = await this.prisma.operation_contract.create({
      data: {
        creator_user_id: parseInt(args.userId),
        label: 'Mandat',
        model_id: SubEntity.IMMOBILIER_VENTE_ANCIEN_MANDAT_SIMPLIFIE,
        operation_id: args.operationId ? parseInt(args.operationId) : 0,
        status: args.status
      },
      select: { creator_user_id: true, id: true, model_id: true, operation_id: true }
    });

    return { id: contract.id.toString(), userId: contract.creator_user_id.toString() };
  }

  async getContract(contractId: string) {
    const result = await this.prisma.operation_contract.findUnique({
      select: {
        archive_time: true,
        contract_file_id: true,
        creation_context: true,
        current_contract_reviews_task_id: true,
        current_contract_validation_task_id: true,
        current_signature_id: true,
        id: true,
        status: true
      },
      where: {
        id: parseInt(contractId)
      }
    });

    if (result === null) {
      throw new Error(`Contract not found for contractId: ${contractId}`);
    }

    return {
      archiveTime: result.archive_time,
      contractFileId: result.contract_file_id?.toString(),
      creationContext: result.creation_context,
      currentContractReviewTaskId:
        result.current_contract_reviews_task_id != null ? result.current_contract_reviews_task_id.toString() : null,
      currentContractValidationTaskId:
        result.current_contract_validation_task_id != null
          ? result.current_contract_validation_task_id.toString()
          : null,
      currentSignatureId: result.current_signature_id != null ? result.current_signature_id.toString() : null,
      id: result.id.toString(),
      status: result.status
    };
  }

  async getContractHistory({ contractId, userId }: { contractId: string; userId: string }) {
    const result = await this.prisma.contract_last_visited.findUnique({
      select: {
        contract_id: true,
        id: true,
        last_access_time: true,
        organization_id: true,
        user_id: true
      },
      where: {
        user_id_contract_id: {
          contract_id: parseInt(contractId),
          user_id: parseInt(userId)
        }
      }
    });

    if (result === null) {
      throw new Error(`Contract history not found for contractId: ${contractId} and userId: ${userId}`);
    }

    return {
      contractId: result.contract_id.toString(),
      id: result.id.toString(),
      lastAccessTime: result.last_access_time as Date,
      organizationId: result.organization_id?.toString(),
      userId: result.user_id.toString()
    };
  }

  async updateContract(args: {
    contractFileId?: string;
    currentContractReviewsTaskId?: string;
    currentContractValidationTaskId?: string;
    files?: FileInfo[];
    id: string;
    status?: ContractStatus;
  }) {
    await this.prisma.operation_contract.update({
      data: {
        contract_file_id: args.contractFileId,
        creation_context: args.files ? JSON.stringify({ files: args.files }) : undefined,
        current_contract_reviews_task_id: args.currentContractReviewsTaskId
          ? parseInt(args.currentContractReviewsTaskId)
          : null,
        current_contract_validation_task_id: args.currentContractValidationTaskId
          ? parseInt(args.currentContractValidationTaskId)
          : null,
        status: args.status
      },
      where: {
        id: parseInt(args.id)
      }
    });
  }

  async updateContractCurrentSignature(args: { contractId: string; currentSignatureId: string | null }) {
    await this.prisma.operation_contract.update({
      data: {
        current_signature_id: args.currentSignatureId != null ? parseInt(args.currentSignatureId) : null
      },
      where: {
        id: parseInt(args.contractId)
      }
    });
  }

  async createContractTemplate(args: { id: string; isMainContract?: boolean }) {
    await this.prisma.operation_contract_model.createMany({
      data: {
        config_path: 'fake config_path',
        id: args.id,
        jefferson_path: 'fake jefferson_path',
        label: args.id,
        main_contract: args.isMainContract ?? false
      },
      skipDuplicates: true
    });
  }
}
