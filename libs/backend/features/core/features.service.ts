import { Injectable } from '@nestjs/common';
import { FeatureOrganizationsApiService } from '@mynotary/backend/feature-organizations/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';

@Injectable()
export class FeaturesService {
  constructor(private featureOrganizationsApiService: FeatureOrganizationsApiService) {}

  async findFeatureIdByType({ featureType, organizationId }: FindFeatureIdByTypeArgs): Promise<string | null> {
    const features = await this.featureOrganizationsApiService.getFeatures({ organizationId });
    const feature = features.find((feature) => feature.type === featureType);
    return feature?.id ?? null;
  }
}

interface FindFeatureIdByTypeArgs {
  featureType: FeatureType;
  organizationId: string;
}
