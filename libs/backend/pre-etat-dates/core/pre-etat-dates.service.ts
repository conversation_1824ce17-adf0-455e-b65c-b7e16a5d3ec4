import { Injectable } from '@nestjs/common';
import { OrdersApiService } from '@mynotary/backend/orders/api';
import { PreEtatDatesProvider } from './pre-etat-dates.provider';
import { Exception } from '@mynotary/crossplatform/shared/util';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { DrivesApiService } from '@mynotary/backend/drives/api';
import { OrderERP, OrderPreEtatDate, OrderStatus, OrderType } from '@mynotary/crossplatform/orders/api';
import { formatDateTime } from '@mynotary/crossplatform/shared/dates-util';

@Injectable()
export class PreEtatDatesService {
  constructor(
    private preEtatDatesProvider: PreEtatDatesProvider,
    private ordersApiService: OrdersApiService,
    private legalsApiService: LegalsApiService,
    private drivesApiService: DrivesApiService
  ) {}

  async createPreEtatDates(args: CreatePreEtatDatesArgs) {
    const order = await this.ordersApiService.getOrder({
      id: args.orderId
    });

    if (order.status === OrderStatus.COMPLETED) {
      throw new Exception(`Order ${order.id} is already completed`);
    }

    assertIsOrderPreEtatDate(order.data);
    const orderPreEtatDates = order.data;

    try {
      const preEtatDates = await this.preEtatDatesProvider.createPreEtatDates(orderPreEtatDates);

      await this.ordersApiService.updateOrder({
        data: {
          providerId: preEtatDates.providerId,
          type: OrderType.PRE_ETAT_DATE
        },
        id: order.id,
        invoiceFileId: args.invoiceFileId
      });

      await this.ordersApiService.updateOrderStatus({
        id: order.id,
        status: OrderStatus.PENDING_ORDER
      });
    } catch (e) {
      await this.ordersApiService.updateOrderStatus({
        id: order.id,
        status: OrderStatus.ERROR
      });

      throw new Exception(`Failed to create pre-etat dates, order ${order.id}`, { cause: e });
    }
  }

  async onPreEtatDatesCompleted(args: { providerId: string }) {
    const order = await this.ordersApiService.getOrder({
      providerId: args.providerId,
      type: OrderType.PRE_ETAT_DATE
    });

    if (order.status === OrderStatus.COMPLETED) {
      throw new Exception(`Order ${order.id} is already completed`);
    }

    assertIsOrderPreEtatDate(order.data);

    try {
      const filesPreEtatDates = await this.preEtatDatesProvider.createPreEtatDatesFiles({
        providerId: args.providerId
      });

      const documentId = 'document_pre_etat_date';

      await this.legalsApiService.updateLegalRecordAnswer({
        answer: {
          [documentId]: {
            value: {
              [filesPreEtatDates.pedFileId]: 1
            }
          }
        },
        legalRecordId: order.data.destinationLegalRecordId
      });

      const driveFolder = await this.drivesApiService.createDriveFolder({
        label: `Pre-état daté (${formatDateTime(new Date())})`,
        operationId: order.legalOperationId,
        userId: order.creatorId
      });

      await this.drivesApiService.createDriveFile({
        documentLabel: 'Pre-état daté',
        fileId: filesPreEtatDates.pedFileId,
        folderId: driveFolder.id,
        operationId: order.legalOperationId
      });

      for (const additionalFile of filesPreEtatDates.additionalFiles) {
        await this.drivesApiService.createDriveFile({
          documentLabel: additionalFile.name,
          fileId: additionalFile.fileId,
          folderId: driveFolder.id,
          operationId: order.legalOperationId
        });
      }

      await this.ordersApiService.updateOrder({
        data: {
          additionalFiles: filesPreEtatDates.additionalFiles.map((additionalFile) => ({
            fileId: additionalFile.fileId,
            name: additionalFile.name
          })),
          documentFileId: filesPreEtatDates.pedFileId,
          type: OrderType.PRE_ETAT_DATE
        },
        id: order.id
      });

      await this.ordersApiService.updateOrderStatus({
        id: order.id,
        status: OrderStatus.COMPLETED
      });
    } catch (e) {
      await this.ordersApiService.updateOrderStatus({
        id: order.id,
        status: OrderStatus.ERROR
      });
      throw e;
    }
  }
}

interface CreatePreEtatDatesArgs {
  invoiceFileId?: string;
  orderId: string;
}

export function assertIsOrderPreEtatDate(data: OrderERP | OrderPreEtatDate): asserts data is OrderPreEtatDate {
  if (data.type !== OrderType.PRE_ETAT_DATE) {
    throw new Exception(`Expected order type to be ${OrderType.PRE_ETAT_DATE} but got ${data.type}`);
  }
}
