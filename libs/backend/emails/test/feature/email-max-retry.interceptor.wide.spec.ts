import { Controller, Post, UseInterceptors } from '@nestjs/common';
import { EmailAsyncTaskInterceptor } from '@mynotary/backend/emails/feature';
import {
  InternalNotificationsApiService,
  InternalNotificationType
} from '@mynotary/backend/internal-notifications/api';
import { provideEmailsTest } from '..';
import { createTestingWideApp } from '@mynotary/backend/shared/test';

describe('EmailAsyncTaskInterceptor', () => {
  it('should not call sendNotification when retry count not reach notification', async () => {
    const { client, internalNotificationSpy } = await setup();

    await client.post('/emails/test-email-id').set({ 'x-cloudtasks-taskretrycount': '1' }).send({});

    expect(internalNotificationSpy).not.toHaveBeenCalled();
  });

  it('should call sendNotification when retry count reach notification', async () => {
    const { client, internalNotificationSpy } = await setup();

    await client.post('/emails/test-email-id').set({ 'x-cloudtasks-taskretrycount': '8' }).send({});

    expect(internalNotificationSpy).toHaveBeenCalled();
    expect(internalNotificationSpy).toHaveBeenCalledWith({
      emailId: 'test-email-id',
      type: InternalNotificationType.EMAIL
    });
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: TestController,
      providers: provideEmailsTest()
    });

    const internalNotificationService = getService(InternalNotificationsApiService);

    const internalNotificationSpy = jest.spyOn(internalNotificationService, 'sendInternalNotification');
    internalNotificationSpy.mockImplementation(() => Promise.resolve());

    return {
      client,
      internalNotificationSpy
    };
  }
});

@Controller()
class TestController {
  @UseInterceptors(EmailAsyncTaskInterceptor())
  @Post('/emails/:emailId')
  async testEndpoint() {
    return Promise.reject(new Error('Test error'));
  }
}
