import { provideContractValidationsScope } from '@mynotary/backend/contract-validations/providers';
import { provideAuthorizationsTest } from '@mynotary/backend/authorizations/test';
import { provideLegalsTest } from '@mynotary/backend/legals/test';
import { provideUsersTest } from '@mynotary/backend/users/test';
import { provideEmailsTest } from '@mynotary/backend/emails/test';
import { provideNotificationsTest } from '@mynotary/backend/notifications/test';
import { provideOrganizationsTest } from '@mynotary/backend/organizations/test';
import { provideContractValidatorsTest } from '@mynotary/backend/contract-validators/test';

export const provideContractValidationsTest = () => {
  return [
    ...provideContractValidationsScope(),
    ...provideContractValidatorsTest(),
    ...provideAuthorizationsTest(),
    ...provideLegalsTest(),
    ...provideEmailsTest(),
    ...provideUsersTest(),
    ...provideNotificationsTest(),
    ...provideOrganizationsTest()
  ];
};
