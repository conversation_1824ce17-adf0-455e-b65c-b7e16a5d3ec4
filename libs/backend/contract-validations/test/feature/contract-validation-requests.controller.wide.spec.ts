import { ContractValidationsController } from '@mynotary/backend/contract-validations/feature';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';
import { ContractStatus, TaskType } from '@mynotary/crossplatform/legals/api';
import { ContractValidationRequestNewDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { provideContractValidationsTest } from '..';
import { ContractStatutError, LegalsApiService } from '@mynotary/backend/legals/api';
import { NotificationApiService } from '@mynotary/backend/notifications/api';
import { EmailsApiService } from '@mynotary/backend/emails/api';
import { EnvService } from '@mynotary/backend/secrets/api';

describe('Contract Validation Requests', () => {
  it('should throw when contract status is invalid', async () => {
    const { client, operationId, testingRepos, userId } = await setup();
    const contract = await testingRepos.contracts.createMandat({
      operationId,
      status: ContractStatus.VALIDATED,
      userId
    });

    const response = await client.post('/contract-validation-requests').send({
      contractId: contract.id,
      description: 'Test description',
      emailAssignees: ['<EMAIL>'],
      emailContent: 'Test content',
      emailSubject: 'Test subject',
      title: 'Test Title',
      userId: userId
    } satisfies ContractValidationRequestNewDto);

    expect(response.status).toBe(400);
    expect(response.body.type).toEqual(ContractStatutError.name);
  });

  it('should create task successfully', async () => {
    const {
      client,
      createNotificationSpy,
      envService,
      operationId,
      organizationId,
      sendEmailSpy,
      testingRepos,
      userAssignee,
      userEmail,
      userFirstname,
      userId,
      userLastname
    } = await setup();

    const contract = await testingRepos.contracts.createMandat({
      operationId,
      status: ContractStatus.REDACTION,
      userId
    });

    const response = await client.post('/contract-validation-requests').send({
      contractId: contract.id,
      description: 'Test description',
      emailAssignees: [userAssignee.email],
      emailContent: 'Test content',
      emailSubject: 'Test subject',
      title: 'Test Title',
      userId: userId
    } satisfies ContractValidationRequestNewDto);

    expect(response.status).toBe(201);
    const updatedContract = await testingRepos.contracts.getContract(contract.id);
    expect(updatedContract.status).toBe(ContractStatus.VALIDATION_PENDING);
    expect(updatedContract.currentContractValidationTaskId).toBeDefined();
    expect(updatedContract.currentContractValidationTaskId).toEqual(response.body.id);
    expect(response.body).toMatchObject({
      assignees: [{ email: userAssignee.email }],
      contractId: contract.id,
      description: 'Test description',
      title: 'Test Title',
      type: 'VALIDATE_CONTRACT'
    });

    expect(createNotificationSpy).toHaveBeenCalledTimes(1);
    expect(createNotificationSpy).toHaveBeenCalledWith({
      contractId: contract.id,
      creatorFirstname: 'Foo',
      creatorId: userId,
      creatorLastname: 'BAR',
      operationId: operationId,
      receivers: [userAssignee.email],
      taskId: response.body.id,
      title: 'Demande de validation',
      type: 'TASK_VALIDATE_CONTRACT_CREATED',
      userIds: [userAssignee.id]
    });
    expect(sendEmailSpy).toHaveBeenCalledTimes(1);
    expect(sendEmailSpy).toHaveBeenCalledWith({
      data: {
        appUrl: `${envService.url.mnAppUrl}/operation/${operationId}/contrats/${contract.id}`,
        contractId: contract.id,
        emailContent: 'Test content',
        operationId: operationId,
        organizationId: organizationId,
        sender: {
          email: userEmail,
          firstname: userFirstname,
          lastname: userLastname,
          organizationName: 'MyNotary',
          phone: ''
        },
        subject: 'Test subject',
        taskId: response.body.id,
        templateId: 'TASK_VALIDATE_CONTRACT'
      },
      receiver: userAssignee.email
    });
  });

  it('should rollback on error', async () => {
    const { client, notificationApiService, operationId, testingRepos, userId } = await setup();
    const contract = await testingRepos.contracts.createMandat({
      operationId,
      status: ContractStatus.REDACTION,
      userId
    });

    jest.spyOn(notificationApiService, 'createNotification').mockImplementation(() => {
      throw new Error('Notification error');
    });

    const response = await client.post('/contract-validation-requests').send({
      contractId: contract.id,
      description: 'Test description',
      emailAssignees: ['<EMAIL>'],
      emailContent: 'Test content',
      emailSubject: 'Test subject',
      title: 'Test Title',
      userId: userId
    } satisfies ContractValidationRequestNewDto);

    const updatedContract = await testingRepos.contracts.getContract(contract.id);

    expect(response.status).toBe(500);

    expect(updatedContract.status).toBe(ContractStatus.REDACTION);
    expect(updatedContract.currentContractValidationTaskId).toBeNull();
  });

  it('should throw when contract status is invalid', async () => {
    const { client, member, testingRepos } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId: member.organizationId });
    const contract = await testingRepos.contracts.createMandat({
      operationId: operation.id,
      status: ContractStatus.DRAFT,
      userId: member.userId
    });

    const response = await client.delete(`/contract-validation-requests/${contract.id}`);

    expect(response.status).toBe(400);
    expect(response.body).toMatchObject({
      displayedMessage: "Le statut du contrat ne permet pas d'effectuer cette action, veuillez rafraichir la page",
      type: 'ContractStatutError'
    });
  });

  it('should delete task for validation request', async () => {
    const { client, legalsApiService, member, testingRepos } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId: member.organizationId });
    const contract = await testingRepos.contracts.createMandat({
      operationId: operation.id,
      status: ContractStatus.VALIDATION_PENDING,
      userId: member.userId
    });

    const task = await testingRepos.tasks.createTask({
      operationId: operation.id,
      reference: { contractId: parseInt(contract.id) },
      type: TaskType.VALIDATE_CONTRACT,
      userId: member.userId
    });

    await testingRepos.contracts.updateContract({
      currentContractValidationTaskId: task.id,
      id: contract.id
    });

    const deleteTaskSpy = jest.spyOn(legalsApiService, 'deleteTask');

    const response = await client.delete(`/contract-validation-requests/${contract.id}`);

    expect(deleteTaskSpy).toHaveBeenCalledWith(task.id);
    expect(response.body.deletedTaskIds).toEqual([task.id]);

    const updatedContract = await testingRepos.contracts.getContract(contract.id);
    expect(updatedContract.status).toBe(ContractStatus.REDACTION);
    expect(updatedContract.currentContractValidationTaskId).toBeNull();
  });

  it('should delete task for pending review request', async () => {
    const { client, legalsApiService, member, testingRepos } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId: member.organizationId });
    const contract = await testingRepos.contracts.createMandat({
      operationId: operation.id,
      status: ContractStatus.REVIEW_PENDING,
      userId: member.userId
    });

    const taskReview = await testingRepos.tasks.createTask({
      operationId: operation.id,
      reference: { contractId: parseInt(contract.id) },
      type: TaskType.REVIEW_CONTRACT,
      userId: member.userId
    });

    const taskValidation = await testingRepos.tasks.createTask({
      operationId: operation.id,
      reference: { contractId: parseInt(contract.id) },
      type: TaskType.VALIDATE_CONTRACT,
      userId: member.userId
    });

    const deleteTaskSpy = jest.spyOn(legalsApiService, 'deleteTask');

    const response = await client.delete(`/contract-validation-requests/${contract.id}`);

    expect(response.body.deletedTaskIds).toEqual([taskReview.id, taskValidation.id]);
    expect(deleteTaskSpy).toHaveBeenCalledWith(taskReview.id);
    expect(deleteTaskSpy).toHaveBeenCalledWith(taskValidation.id);

    const updatedContract = await testingRepos.contracts.getContract(contract.id);
    expect(updatedContract.status).toBe(ContractStatus.REDACTION);
  });

  it('should not delete task for confirmed review request', async () => {
    const { client, legalsApiService, member, testingRepos } = await setup();

    const operation = await testingRepos.operations.createVenteAncien({ organizationId: member.organizationId });
    const contract = await testingRepos.contracts.createMandat({
      operationId: operation.id,
      status: ContractStatus.REVIEWED,
      userId: member.userId
    });

    const taskValidation = await testingRepos.tasks.createTask({
      operationId: operation.id,
      reference: { contractId: parseInt(contract.id) },
      type: TaskType.VALIDATE_CONTRACT,
      userId: member.userId
    });

    const taskReview = await testingRepos.tasks.createTask({
      completionTime: new Date().toISOString(),
      operationId: operation.id,
      reference: { contractId: parseInt(contract.id) },
      type: TaskType.REVIEW_CONTRACT,
      userId: member.userId
    });

    const deleteTaskSpy = jest.spyOn(legalsApiService, 'deleteTask');

    const response = await client.delete(`/contract-validation-requests/${contract.id}`);

    expect(deleteTaskSpy).toHaveBeenCalledTimes(1);
    expect(response.body.deletedTaskIds).toEqual([taskValidation.id]);

    const updatedContract = await testingRepos.contracts.getContract(contract.id);
    expect(updatedContract.status).toBe(ContractStatus.REDACTION);
  });

  async function setup() {
    const { client, getService } = await createTestingWideApp({
      bypassAuth: true,
      controller: ContractValidationsController,
      providers: provideContractValidationsTest()
    });

    const testingRepos = getService(TestingRepositories);
    const notificationApiService = getService(NotificationApiService);
    const emailsApiService = getService(EmailsApiService);
    const legalsApiService = getService(LegalsApiService);
    const envService = getService(EnvService);

    const { organizationId, userEmail, userFirstname, userId, userLastname } = await testingRepos.createMember({});

    const { id: operationId } = await testingRepos.operations.createVenteAncien({
      organizationId,
      userId
    });

    const userAssignee = await testingRepos.users.createUser({
      email: '<EMAIL>',
      firstname: 'example',
      lastname: 'example'
    });

    const member = await testingRepos.createMember({});

    const createNotificationSpy = jest.spyOn(notificationApiService, 'createNotification');
    const sendEmailSpy = jest.spyOn(emailsApiService, 'sendEmail');

    return {
      client,
      createNotificationSpy,
      envService,
      legalsApiService,
      member,
      notificationApiService,
      operationId,
      organizationId,
      sendEmailSpy,
      testingRepos,
      userAssignee,
      userEmail,
      userFirstname,
      userId,
      userLastname
    };
  }
});
