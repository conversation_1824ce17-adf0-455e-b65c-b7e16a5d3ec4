# Guards Goldens

  > ⚠ This file is auto-generated. Do not edit by hand.
## contract-validations.controller.ts
```ts
@UseGuards(
    OrGuard([
      [
        HasContractPermission(PermissionType.CREATE_CONTRACT_TASK_ASK_VALIDATION, {
          contractIdResolver: bodyResolver('contractId')
        })
      ],
      [
        HasContractPermission(PermissionType.FORCE_CREATE_CONTRACT_TASK_ASK_VALIDATION, {
          contractIdResolver: bodyResolver('contractId')
        })
      ]
    ])
  )
@Post('/contract-validation-requests')

@UseGuards(HasContractValidationRequestDeletePermission())
@Delete('/contract-validation-requests/:contractId')

@UseGuards(
    OrGuard([
      [
        HasContractPermission(PermissionType.VALIDATE_CONTRACT, {
          contractIdResolver: bodyResolver('contractId')
        })
      ],
      [
        HasContractPermission(PermissionType.FORCE_VALIDATE_CONTRACT, {
          contractIdResolver: bodyResolver('contractId')
        })
      ]
    ])
  )
@Post('/contract-validations')

@UseGuards(
    OrGuard([
      [HasContractPermission(PermissionType.VALIDATE_CONTRACT)],
      [HasContractPermission(PermissionType.FORCE_VALIDATE_CONTRACT)]
    ])
  )
@Delete('/contract-validation/:contractId')

```
