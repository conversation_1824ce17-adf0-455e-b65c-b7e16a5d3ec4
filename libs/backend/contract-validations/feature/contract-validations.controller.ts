import { Body, Controller, Delete, Param, Post, UseGuards } from '@nestjs/common';
import {
  ContractValidationRequestNewArgs,
  ContractValidationsService
} from '@mynotary/backend/contract-validations/core';
import {
  ContractValidationNewDto,
  ContractValidationRequestNewDto,
  TaskDeletedDto,
  TaskDto,
  TaskTypeDto
} from '@mynotary/crossplatform/api-mynotary/openapi';
import { HasContractPermission } from '@mynotary/backend/legals/api';
import { OrGuard } from '@mynotary/backend/authorizations/api';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { convertEnum } from '@mynotary/crossplatform/shared/util';
import { HasContractValidationRequestDeletePermission } from '@mynotary/backend/contract-validations/authorization';
import { bodyResolver } from '@mynotary/backend/shared/auth-util';

@Controller()
export class ContractValidationsController {
  constructor(private contractValidationsService: ContractValidationsService) {}

  @UseGuards(
    OrGuard([
      [
        HasContractPermission(PermissionType.CREATE_CONTRACT_TASK_ASK_VALIDATION, {
          contractIdResolver: bodyResolver('contractId')
        })
      ],
      [
        HasContractPermission(PermissionType.FORCE_CREATE_CONTRACT_TASK_ASK_VALIDATION, {
          contractIdResolver: bodyResolver('contractId')
        })
      ]
    ])
  )
  @Post('/contract-validation-requests')
  async createContractValidationRequest(@Body() body: ContractValidationRequestNewDto): Promise<TaskDto> {
    const validationRequest: ContractValidationRequestNewArgs = {
      contractId: body.contractId,
      description: body.description ?? '',
      email: {
        content: body.emailContent,
        subject: body.emailSubject
      },
      emailAssignees: body.emailAssignees,
      title: body.title,
      userId: body.userId
    };
    const validation = await this.contractValidationsService.createContractValidationRequest(validationRequest);

    return {
      assignees: validation.task.assignees,
      contractId: validation.contract.id,
      creationTime: validation.task.creationTime,
      creatorEmail: validation.creator.email,
      creatorFirstname: validation.creator.firstname,
      creatorLastname: validation.creator.lastname,
      creatorPhone: validation.creator.phone,
      creatorProfilePictureFileId: validation.creator.profilePictureFileId,
      creatorUserId: validation.creator.id,
      description: validation.task.description,
      id: validation.task.id,
      legalOperationId: validation.task.operationId,
      organizationAddress: validation.organization.address,
      organizationId: validation.organization.id,
      organizationName: validation.organization.name,
      seen: false,
      title: validation.task.title,
      type: convertEnum(TaskTypeDto, validation.task.type)
    };
  }

  @UseGuards(HasContractValidationRequestDeletePermission())
  @Delete('/contract-validation-requests/:contractId')
  async deleteContractValidationRequest(@Param('contractId') contractId: string): Promise<TaskDeletedDto> {
    return await this.contractValidationsService.deleteContractValidationRequest(contractId);
  }

  @UseGuards(
    OrGuard([
      [
        HasContractPermission(PermissionType.VALIDATE_CONTRACT, {
          contractIdResolver: bodyResolver('contractId')
        })
      ],
      [
        HasContractPermission(PermissionType.FORCE_VALIDATE_CONTRACT, {
          contractIdResolver: bodyResolver('contractId')
        })
      ]
    ])
  )
  @Post('/contract-validations')
  async validateContract(@Body() body: ContractValidationNewDto): Promise<void> {
    await this.contractValidationsService.validateContract(body);
  }

  @UseGuards(
    OrGuard([
      [HasContractPermission(PermissionType.VALIDATE_CONTRACT)],
      [HasContractPermission(PermissionType.FORCE_VALIDATE_CONTRACT)]
    ])
  )
  @Delete('/contract-validation/:contractId')
  async cancelContractValidation(@Param('contractId') contractId: string): Promise<void> {
    await this.contractValidationsService.cancelContractValidation(contractId);
  }
}
