import { InvalidInputError } from '@mynotary/crossplatform/shared/util';

export class InvalidContractValidatorError extends InvalidInputError {
  override name = InvalidContractValidatorError.name;

  constructor({ cause, email }: { cause?: unknown; email: string }) {
    super({
      cause,
      displayedMessage: `Le validateur ${email} du contrat n'est pas autorisé pour la validation de ce contrat.`,
      message: `Invalid contract validator ${email}`
    });
  }
}
