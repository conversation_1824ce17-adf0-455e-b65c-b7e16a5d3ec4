import { Injectable } from '@nestjs/common';
import { LegalsApiService, ContractStatutError, Task } from '@mynotary/backend/legals/api';
import { ContractStatus, isRedactionPending, TaskType } from '@mynotary/crossplatform/legals/api';
import { NotificationApiService, TaskNotificationType } from '@mynotary/backend/notifications/api';
import { EmailsApiService, EmailTemplateId } from '@mynotary/backend/emails/api';
import { Contract } from '@mynotary/crossplatform/legals/api';
import { User } from '@mynotary/crossplatform/shared/users-core';
import { UsersApiService } from '@mynotary/backend/users/api';
import { EmailNewData } from '@mynotary/backend/emails/api';
import { EnvService } from '@mynotary/backend/secrets/api';
import { Organization, OrganizationsApiService } from '@mynotary/backend/organizations/api';
import { ContractValidatorsApiService } from '@mynotary/backend/contract-validators/api';
import { InvalidContractValidatorError } from './contract-validations.error';

@Injectable()
export class ContractValidationsService {
  constructor(
    private legalsApiService: LegalsApiService,
    private organizationsApiService: OrganizationsApiService,
    private notificationApiService: NotificationApiService,
    private emailsApiService: EmailsApiService,
    private usersApiService: UsersApiService,
    private envService: EnvService,
    private contractValidatorsApiService: ContractValidatorsApiService
  ) {}

  async createContractValidationRequest(args: ContractValidationRequestNewArgs): Promise<ContractValidationCreated> {
    const { contractId, description, email, emailAssignees, title, userId } = args;

    const contract = await this.legalsApiService.getContract(contractId);
    const assignees = await this.usersApiService.getUsers({ emails: emailAssignees });
    const creator = await this.usersApiService.getUser({ id: userId, type: 'by-id' });
    const organization = await this.organizationsApiService.getOrganization({
      organizationId: contract.organizationId
    });

    const validators = await this.contractValidatorsApiService.findContractValidators(contract);

    if (validators && validators.locked) {
      const validatorEmails = validators.users.map((validator) => validator.email);
      for (const assignee of assignees) {
        if (!validatorEmails.includes(assignee.email)) {
          throw new InvalidContractValidatorError({ email: assignee.email });
        }
      }
    }

    if (contract.status !== ContractStatus.REDACTION) {
      throw new ContractStatutError(`Cannot create validation in status ${contract.status}`);
    }

    let contractValidation: ContractValidationCreated | null = null;
    try {
      const task = await this.legalsApiService.createTask({
        assignees: emailAssignees,
        contractId,
        creatorUserId: userId,
        description,
        email: {
          content: email.content,
          subject: email.subject
        },
        legalComponentId: contract.legalOperationId,
        organizationId: contract.organizationId,
        title,
        type: TaskType.VALIDATE_CONTRACT
      });

      contractValidation = {
        assignees,
        contract,
        creator,
        email: {
          content: email.content,
          subject: email.subject
        },
        organization,
        task
      };

      await this.legalsApiService.updateContract({
        currentContractValidationTaskId: task.id,
        id: contractId,
        status: ContractStatus.VALIDATION_PENDING
      });

      await this.notifyContractValidated(contractValidation);
      return contractValidation;
    } catch (error) {
      if (contractValidation?.task.id) {
        await this.legalsApiService.deleteTask(contractValidation?.task.id);
      }

      await this.legalsApiService.updateContract({
        currentContractValidationTaskId: null,
        id: contractId,
        status: ContractStatus.REDACTION
      });

      throw error;
    }
  }

  async deleteContractValidationRequest(contractId: string): Promise<{ deletedTaskIds: string[] }> {
    const contract = await this.legalsApiService.getContract(contractId);

    const allowedStatus = [ContractStatus.VALIDATION_PENDING, ContractStatus.REVIEW_PENDING, ContractStatus.REVIEWED];

    if (!allowedStatus.includes(contract.status as ContractStatus)) {
      throw new ContractStatutError();
    }

    const tasks = await this.legalsApiService.getTasks({
      contractId,
      types: [TaskType.VALIDATE_CONTRACT, TaskType.REVIEW_CONTRACT]
    });

    await this.legalsApiService.updateContract({
      currentContractValidationTaskId: null,
      id: contractId,
      status: ContractStatus.REDACTION
    });

    const deletedTaskIds: string[] = [];
    for (const task of tasks) {
      if (task.type === TaskType.REVIEW_CONTRACT && task.completionDate == null) {
        await this.legalsApiService.deleteTask(task.id);
        deletedTaskIds.push(task.id);
      } else if (task.type === TaskType.VALIDATE_CONTRACT) {
        await this.legalsApiService.deleteTask(task.id);
        deletedTaskIds.push(task.id);
      }
    }

    return { deletedTaskIds };
  }

  async validateContract(args: ContractValidationNewArgs): Promise<void> {
    const { contractId, fileId, userId } = args;

    const user = await this.usersApiService.getUser({ id: userId, type: 'by-id' });

    const contract = await this.legalsApiService.getContract(contractId);

    if (!isRedactionPending(contract.status)) {
      throw new ContractStatutError();
    }

    await this.legalsApiService.updateContract({
      contractFileId: fileId,
      id: contractId,
      status: ContractStatus.VALIDATED
    });

    const validationTasks = await this.legalsApiService.getTasks({
      contractId,
      types: [TaskType.VALIDATE_CONTRACT]
    });

    for (const task of validationTasks) {
      if (task.completionDate == null) {
        await this.legalsApiService.updateTask({
          completionTime: new Date().toISOString(),
          id: task.id
        });

        await this.notificationApiService.createNotification({
          contractId,
          creatorFirstname: user.firstname,
          creatorId: user.id,
          creatorLastname: user.lastname,
          operationId: task.operationId,
          taskId: task.id,
          title: task.title,
          type: TaskNotificationType.TASK_VALIDATE_CONTRACT_COMPLETED,
          userIds: [task.creatorId]
        });

        if (task.creatorEmail) {
          const operation = await this.legalsApiService.getOperation(task.operationId);

          let appUrl = `${this.envService.url.mnAppUrl}/operation/${task.operationId}/contrats/redaction/${contractId}`;
          if (operation.parentId) {
            appUrl = `${this.envService.url.mnAppUrl}/operation/${operation.parentId}/operations/${task.operationId}/contrats/redaction/${contractId}`;
          }

          await this.emailsApiService.sendEmail({
            data: {
              appUrl,
              contractId,
              contractStatus: ContractStatus.VALIDATED as string,
              documentLabel: contract.label,
              emailValidator: user.email,
              operationId: task.operationId,
              operationLabel: task.title,
              organizationId: contract.organizationId,
              templateId: EmailTemplateId.CONTRACT_VALIDATION
            },
            receiver: task.creatorEmail
          });
        }
      }
    }
  }

  async cancelContractValidation(contractId: string): Promise<void> {
    const contract = await this.legalsApiService.getContract(contractId);
    const contractTasks = await this.legalsApiService.getTasks({
      contractId,
      types: [TaskType.VALIDATE_CONTRACT, TaskType.REVIEW_CONTRACT]
    });

    const contractValidation = {
      contract,
      reviewTasks: contractTasks.filter((task) => task.type === TaskType.REVIEW_CONTRACT),
      validationTasks: contractTasks.filter((task) => task.type === TaskType.VALIDATE_CONTRACT)
    };

    if (contract.status !== ContractStatus.VALIDATED) {
      throw new ContractStatutError();
    }

    const targetStatus = getDeletedValidationTargetStatus(contractValidation);

    await this.legalsApiService.updateContract({
      id: contract.id,
      status: targetStatus
    });

    const currentValidationTask = contractValidation.validationTasks.find((task) => task.completionDate != null);

    if (currentValidationTask != null) {
      await this.legalsApiService.updateTask({
        completionTime: null,
        id: currentValidationTask.id
      });
    }
  }

  private async notifyContractValidated(contractValidation: ContractValidationCreated): Promise<void> {
    const operation = await this.legalsApiService.getOperation(contractValidation.contract.legalOperationId);

    let link = `${this.envService.url.mnAppUrl}/operation/${contractValidation.contract.legalOperationId}/contrats/${contractValidation.contract.id}`;
    if (operation.parentId) {
      link = `${this.envService.url.mnAppUrl}/operation/${operation.parentId}/operations/${contractValidation.contract.legalOperationId}/contrats/${contractValidation.contract.id}`;
    }

    await this.notificationApiService.createNotification({
      contractId: contractValidation.contract.id,
      creatorFirstname: contractValidation.creator.firstname,
      creatorId: contractValidation.creator.id,
      creatorLastname: contractValidation.creator.lastname,
      operationId: contractValidation.contract.legalOperationId,
      receivers: contractValidation.assignees.map((assignee) => assignee.email),
      taskId: contractValidation.task.id,
      title: 'Demande de validation',
      type: TaskNotificationType.TASK_VALIDATE_CONTRACT_CREATED,
      userIds: contractValidation.assignees.map((assignee) => assignee.id)
    });

    const emailData: EmailNewData = {
      appUrl: link,
      contractId: contractValidation.contract.id,
      emailContent: contractValidation.email.content,
      operationId: contractValidation.contract.legalOperationId,
      organizationId: contractValidation.contract.organizationId,
      sender: {
        email: contractValidation.creator.email,
        firstname: contractValidation.creator.firstname,
        lastname: contractValidation.creator.lastname,
        organizationName: contractValidation.organization.name,
        phone: contractValidation.creator.phone
      },
      subject: contractValidation.email.subject,
      taskId: contractValidation.task.id,
      templateId: EmailTemplateId.TASK_VALIDATE_CONTRACT
    };

    for (const assignee of contractValidation.assignees) {
      await this.emailsApiService.sendEmail({
        data: emailData,
        receiver: assignee.email
      });
    }
  }
}

export interface ContractValidationRequestNewArgs {
  contractId: string;
  description: string;
  email: {
    content: string;
    subject: string;
  };
  emailAssignees: string[];
  title: string;
  userId: string;
}

interface ContractValidationNewArgs {
  contractId: string;
  fileId: string;
  userId: string;
}

interface ContractValidation {
  contract: Contract;
  reviewTasks: Task[];
  validationTasks: Task[];
}

function getDeletedValidationTargetStatus({ reviewTasks, validationTasks }: ContractValidation) {
  if (reviewTasks.length > 0 && !hasCompletedTasks(reviewTasks)) {
    return ContractStatus.REVIEW_PENDING;
  }

  if (reviewTasks.length > 0 && hasCompletedTasks(reviewTasks)) {
    return ContractStatus.REVIEWED;
  }

  if (hasCompletedTasks(validationTasks)) {
    return ContractStatus.VALIDATION_PENDING;
  }

  return ContractStatus.REDACTION;
}

function hasCompletedTasks(tasks: Task[]): boolean {
  return tasks.some((task) => task.completionDate != null);
}

interface ContractValidationCreated {
  assignees: User[];
  contract: Contract;
  creator: User;
  email: {
    content: string;
    subject: string;
  };
  organization: Organization;
  task: Task;
}
