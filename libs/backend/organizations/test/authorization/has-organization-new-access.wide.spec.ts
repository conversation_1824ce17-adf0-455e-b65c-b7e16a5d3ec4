import { Controller, Post, UseGuards } from '@nestjs/common';
import { HasOrganizationNewAccess } from '@mynotary/backend/organizations/authorization';
import { AdminType } from '@mynotary/crossplatform/shared/users-core';
import { provideOrganizationsTest } from '../index';
import { createTestingWideApp, TestingRepositories } from '@mynotary/backend/shared/test';

describe(HasOrganizationNewAccess.name, () => {
  it('should allow access if user is admin with sufficient level', async () => {
    const { client, setCurrentUser, testingRepos } = await setup();

    const user = await testingRepos.users.createUser({ adminType: AdminType.COLLABORATORS, isAdmin: true });

    setCurrentUser({ userId: user.id });

    const response = await client.post('/organizations').send({});
    expect(response.statusCode).toBe(201);
  });

  it('should allow access if user is verified and organization is not a children organization', async () => {
    const { client, setCurrentUser, testingRepos } = await setup();

    const user = await testingRepos.users.createUser({});

    setCurrentUser({ userId: user.id });

    const response = await client.post('/organizations').send({});
    expect(response.statusCode).toBe(201);
  });

  it('should allow access if user is a legacy app user', async () => {
    const { client, setLegacyApp } = await setup();

    setLegacyApp();

    const response = await client.post('/organizations').send({});
    expect(response.statusCode).toBe(201);
  });

  it('should deny access if user is verified but is children organization', async () => {
    const { client, setCurrentUser, testingRepos } = await setup();

    const user = await testingRepos.users.createUser({});

    setCurrentUser({ userId: user.id });

    const response = await client.post('/organizations').send({ parentOrganizationId: '123' });
    expect(response.statusCode).toBe(403);
  });

  it('should deny access if user is does not match any condition', async () => {
    const { client, setCurrentUser, testingRepos } = await setup();

    const user = await testingRepos.users.createUser({ verified: false });

    setCurrentUser({ userId: user.id });

    const response = await client.post('/organizations').send();
    expect(response.statusCode).toBe(403);
  });

  async function setup() {
    const { client, getService, setCurrentUser, setLegacyApp } = await createTestingWideApp({
      controller: TestController,
      providers: provideOrganizationsTest()
    });

    const testingRepos = getService(TestingRepositories);

    return { client, setCurrentUser, setLegacyApp, testingRepos };
  }
});

@Controller()
class TestController {
  @UseGuards(HasOrganizationNewAccess())
  @Post('/organizations')
  createOrganization(__arg: { parentOrganizationId?: string }): string {
    return 'Organization created successfully';
  }
}
