import { Injectable } from '@nestjs/common';
import { v4 as uuid } from 'uuid';
import { find, forEach, isEmpty } from 'lodash';
import { UsersApiService } from '@mynotary/backend/users/api';
import { Role } from '@mynotary/crossplatform/roles/api';
import { Exception } from '@mynotary/crossplatform/shared/util';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { MembersApiService } from '@mynotary/backend/members/api';
import { RolesApiService } from '@mynotary/backend/roles/api';
import { AssociationsApiService, InternalEntityType } from '@mynotary/backend/external-apps/api';
import { OrganizationAssociationType, RecordAssociationType } from '@mynotary/crossplatform/external-apps/api';
import { LegalOperationTemplateId, LegalRecordTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { DefaultRecordsApiService } from '@mynotary/backend/default-records/api';
import { getDefaultValidators } from './orpi-utils';
import { PhoneUtil } from '@mynotary/crossplatform/shared/phones-util';
import { OrpiAgenciesProvider } from './orpi-agencies.provider';
import { OrpiAgency, OrpiMemberRole, OrpiUser } from './orpi';
import { OrpiRepository } from './orpi.repository';
import { defaultAnswerReferences } from './default-answer-references';
import { LegalsApiService } from '@mynotary/backend/legals/api';
import { ContractValidatorsApiService } from '@mynotary/backend/contract-validators/api';
import { Civility, User } from '@mynotary/crossplatform/shared/users-core';
import { OrganizationsApiService } from '@mynotary/backend/organizations/api';
import { OrganizationType } from '@mynotary/crossplatform/organizations/api';

@Injectable()
export class OrpiService {
  constructor(
    private orpiTelemacProvider: OrpiAgenciesProvider,
    private usersApiService: UsersApiService,
    private membersApiService: MembersApiService,
    private rolesApiService: RolesApiService,
    private legalsApiService: LegalsApiService,
    private associationsApiService: AssociationsApiService,
    private defaultRecordsApiService: DefaultRecordsApiService,
    private contractValidatorsApiService: ContractValidatorsApiService,
    private orpiRepository: OrpiRepository,
    private organizationsApiService: OrganizationsApiService
  ) {}

  async createAgency(args: CreateAgencyArgs) {
    const agency = await this.orpiTelemacProvider.getOrpiAgency({ code: args.code });

    if (agency.status === 'inactive') {
      return;
    }

    const organization = await this.createOrganization({ agency, holdingId: args.holdingId });

    const members = await this.createMembers({ agency, organization });

    const adminId = this.getAdminId({ agency, members });
    await this.createDefaultRecords({ adminId, agency, members, organization });

    await this.createDefaultAnswers({ adminId, organization });

    await this.createDefaultValidators({ members, organization });

    return {
      id: agency.id
    };
  }

  async createDefaultValidators(args: { members: Member[]; organization: Organization }) {
    const validators = getDefaultValidators(args);

    await this.contractValidatorsApiService.createContractValidatorsFeature({
      locked: false,
      organizationId: args.organization.id,
      validators
    });
  }

  private async createOrganization({ agency, holdingId }: CreateOrganizationArgs): Promise<Organization> {
    const association = await this.associationsApiService.findAssociation({
      externalId: agency.id.toString(),
      type: OrganizationAssociationType.ORPI_ORGANIZATION
    });

    if (association != null) {
      const roles = await this.rolesApiService.getRoles(association.mynotaryId);

      return {
        id: association.mynotaryId,
        roles
      };
    }

    const mnOrga = await this.organizationsApiService.createOrganization({
      address: agency.address,
      name: agency.name,
      parentOrganizationId: holdingId,
      siren: agency.siren,
      type: OrganizationType.AGENCY
    });

    await this.orpiRepository.updateSubscriptionIdFromHolding({ organizationId: mnOrga.id.toString() });

    const roles = await this.rolesApiService.getRoles(mnOrga.id.toString());

    await this.associationsApiService.createAssociation({
      externalId: agency.id.toString(),
      internalType: InternalEntityType.ORGANIZATION,
      mynotaryId: mnOrga.id.toString(),
      organizationId: mnOrga.id.toString(),
      type: OrganizationAssociationType.ORPI_ORGANIZATION
    });

    await this.associationsApiService.createAssociation({
      externalId: agency.code,
      internalType: InternalEntityType.ORGANIZATION,
      mynotaryId: mnOrga.id.toString(),
      organizationId: mnOrga.id.toString(),
      type: OrganizationAssociationType.ORPI_ORGANIZATION_CODE
    });

    await this.associationsApiService.createAssociation({
      externalId: agency.idSweepBright,
      internalType: InternalEntityType.ORGANIZATION,
      mynotaryId: mnOrga.id.toString(),
      organizationId: mnOrga.id.toString(),
      type: OrganizationAssociationType.ORPI_ORGANIZATION_SWEEPBRIGHT
    });

    return {
      id: mnOrga.id.toString(),
      roles
    };
  }

  private async createMembers({ agency, organization }: CreateMembersArgs): Promise<Member[]> {
    const members: Member[] = [];

    for (const orpiMember of agency.members) {
      let user = await this.usersApiService.findUser({ email: orpiMember.emailOrpi, type: 'by-email' });
      const orpiUser = await this.orpiTelemacProvider.getOrpiUser(orpiMember.emailOrpi);
      const role = this.findRole({ role: orpiMember.role, roles: organization.roles });

      if (user == null) {
        let phone = orpiUser.phone ? PhoneUtil.parsePhone(orpiUser.phone) : undefined;
        phone = phone?.replace(/\s/g, '');

        try {
          user = await this.usersApiService.createUser({
            civility: orpiUser.civility,
            email: orpiMember.emailOrpi,
            firstname: orpiUser.firstname,
            lastname: orpiUser.lastname,
            password: uuid(),
            phone,
            verified: true
          });
        } catch (error) {
          console.error(error);
          user = await this.usersApiService.createUser({
            civility: orpiUser.civility,
            email: orpiMember.emailOrpi,
            firstname: orpiUser.firstname,
            lastname: orpiUser.lastname,
            password: uuid(),
            verified: true
          });
        }
      }

      const member = await this.membersApiService.findMember({ organizationId: organization.id, userId: user.id });
      if (member == null) {
        await this.membersApiService.createMember({
          email: user.email,
          organizationId: organization.id.toString(),
          roleId: role.id
        });
      }

      members.push({ contractType: orpiMember.contractType, orpiUser, role: orpiMember.role, user });
    }

    return members;
  }

  private async createDefaultRecords(args: CreateDefaultRecordsArgs) {
    for (const member of args.members) {
      const intermediaireRecord = await this.createRecordIfNotExists({
        answer: this.mapIntermediaireAnswer({
          contractType: member.contractType,
          role: member.role,
          rsac: member.rsac,
          user: member.orpiUser
        }),
        associationType: RecordAssociationType.ORPI_RECORD_INTERMEDIAIRE,
        creatorId: member.user.id,
        externalId: member.orpiUser.id.toString(),
        organizationId: args.organization.id,
        templateId: 'RECORD__PERSONNE__PHYSIQUE__INTERMEDIAIRE_IMMOBILIER'
      });

      if (intermediaireRecord.hasBeenCreated) {
        await this.addDefaultIntermediaireRecord({
          organizationId: args.organization.id,
          recordId: intermediaireRecord.id,
          userId: member.user.id
        });
      }
    }

    const agencyRecord = await this.createRecordIfNotExists({
      answer: this.agencyRecordAnswer(args),
      associationType: RecordAssociationType.ORPI_RECORD_AGENCE,
      creatorId: args.adminId,
      externalId: args.agency.id.toString(),
      organizationId: args.organization.id,
      templateId: 'RECORD__PERSONNE__MORALE__AGENT_IMMOBILIER'
    });

    if (agencyRecord.hasBeenCreated) {
      const representantRecords: string[] = [];

      for (const legalRepresentative of args.agency.legalRepresentatives) {
        const orpiUser = await this.orpiTelemacProvider.getOrpiUser(legalRepresentative.email);
        const memberFunction = this.getMemberFunction({ agency: args.agency, email: orpiUser.email });

        const representantRecord = await this.createRecordIfNotExists({
          answer: this.mapPersonnePhysiqueAnswer({ function: memberFunction, user: orpiUser }),
          associationType: RecordAssociationType.ORPI_RECORD_PERSONNE_PHYSIQUE,
          creatorId: args.adminId,
          externalId: orpiUser.id.toString(),
          organizationId: args.organization.id,
          templateId: 'RECORD__PERSONNE__PHYSIQUE'
        });
        representantRecords.push(representantRecord.id);
      }
      await this.linkAgencyRepresentant({
        adminId: args.adminId,
        agencyRecordId: agencyRecord.id,
        organizationId: args.organization.id,
        representantRecords
      });

      await this.addDefaultAgencyRecord({
        organizationId: args.organization.id,
        recordId: agencyRecord.id,
        userId: args.adminId
      });
    }
  }

  private getMemberFunction(args: { agency: OrpiAgency; email: string }) {
    const targetOrpiMember = args.agency.members.find((member) => member.emailOrpi === args.email);
    const targetMember = args.agency.members.find((member) => member.emailOrpi === args.email);

    return targetOrpiMember?.function ?? targetMember?.function ?? '-';
  }

  private async createRecordIfNotExists(args: {
    answer: AnswerDict;
    associationType: RecordAssociationType;
    creatorId: string;
    externalId: string;
    organizationId: string;
    templateId: LegalRecordTemplateId;
  }) {
    let hasBeenCreated = false;
    let association = await this.associationsApiService.findAssociation({
      externalId: args.externalId,
      organizationId: args.organizationId,
      type: args.associationType
    });

    if (association == null) {
      const record = await this.legalsApiService.createRecord({
        answer: args.answer,
        creatorId: args.creatorId,
        organizationId: args.organizationId,
        templateId: args.templateId
      });
      hasBeenCreated = true;

      association = await this.associationsApiService.createAssociation({
        externalId: args.externalId,
        internalType: InternalEntityType.LEGAL_COMPONENT,
        mynotaryId: record.id,
        organizationId: args.organizationId,
        type: args.associationType
      });
    }

    return {
      hasBeenCreated,
      id: association.mynotaryId
    };
  }

  private async linkAgencyRepresentant(args: LinkAgencyRepresentantArgs) {
    const link = await this.legalsApiService.createLegalLink({
      creatorId: args.adminId,
      fromLegalId: args.agencyRecordId,
      legalLinkTemplateId: 'LINK__REPRESENTATION__PERSONNE_MORALE'
    });

    forEach(args.representantRecords, async (recordId) => {
      await this.legalsApiService.createLegalBranch({
        fromLegalId: args.agencyRecordId,
        legalLinkId: link.legalLinks[0].id,
        reverseType: 'REPRESENTE',
        toLegalId: recordId,
        type: 'REPRESENTANT'
      });
    });
  }

  private async addDefaultIntermediaireRecord(args: { organizationId: string; recordId: string; userId: string }) {
    const operationTemplateIds: LegalOperationTemplateId[] = [
      'OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL',
      'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL',
      'OPERATION__IMMOBILIER__LOCATION',
      'OPERATION__ORPI__IMMOBILIER__VENTE',
      'OPERATION__IMMOBILIER__VENTE_VIAGER'
    ];

    for (const operationTemplateId of operationTemplateIds) {
      await this.defaultRecordsApiService.addDefaultRecord({
        isOrganizationRecord: false,
        linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES',
        operationTemplateId,
        organizationId: args.organizationId,
        recordId: args.recordId,
        userId: args.userId
      });
    }
  }

  private async addDefaultAgencyRecord(args: { organizationId: string; recordId: string; userId: string }) {
    const venteTemplateIds: LegalOperationTemplateId[] = [
      'OPERATION__ORGANISATION_INTERNE',
      'OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL',
      'OPERATION__ORPI__IMMOBILIER__VENTE',
      'OPERATION__IMMOBILIER__VENTE_VIAGER'
    ];

    for (const venteTemplateId of venteTemplateIds) {
      await this.defaultRecordsApiService.addDefaultRecord({
        isOrganizationRecord: true,
        linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__AGENTS',
        operationTemplateId: venteTemplateId,
        organizationId: args.organizationId,
        recordId: args.recordId,
        userId: args.userId
      });
    }

    const locationTemplateIds: LegalOperationTemplateId[] = [
      'OPERATION__IMMOBILIER__LOCATION',
      'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL'
    ];

    for (const venteTemplateId of locationTemplateIds) {
      await this.defaultRecordsApiService.addDefaultRecord({
        isOrganizationRecord: true,
        linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOCATION__AGENTS',
        operationTemplateId: venteTemplateId,
        organizationId: args.organizationId,
        recordId: args.recordId,
        userId: args.userId
      });
    }

    await this.defaultRecordsApiService.addDefaultRecord({
      isOrganizationRecord: true,
      linkTemplateId: 'LINK__OPERATION__SYNDIC__GENERAL__SYNDIC',
      operationTemplateId: 'OPERATION__SYNDIC__GENERAL',
      organizationId: args.organizationId,
      recordId: args.recordId,
      userId: args.userId
    });

    await this.defaultRecordsApiService.addDefaultRecord({
      isOrganizationRecord: true,
      linkTemplateId: 'LINK__OPERATION__RECRUTEMENT__AGENT__AGENCE',
      operationTemplateId: 'OPERATION__SYNDIC__GENERAL',
      organizationId: args.organizationId,
      recordId: args.recordId,
      userId: args.userId
    });

    await this.defaultRecordsApiService.addDefaultRecord({
      isOrganizationRecord: true,
      linkTemplateId: 'LINK__OPERATION__RECRUTEMENT__AGENT__AGENCE',
      operationTemplateId: 'OPERATION__RECRUTEMENT__MYNOTARY__AGENT',
      organizationId: args.organizationId,
      recordId: args.recordId,
      userId: args.userId
    });
  }

  private async createDefaultAnswers(args: { adminId: string; organization: Organization }) {
    const operations: LegalOperationTemplateId[] = [
      'OPERATION__IMMOBILIER__LOCATION',
      'OPERATION__IMMOBILIER__LOCATION_COMMERCIAL',
      'OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL',
      'OPERATION__IMMOBILIER__VENTE_VIAGER',
      'OPERATION__ORGANISATION_INTERNE',
      'OPERATION__ORPI__IMMOBILIER__VENTE',
      'OPERATION__RECRUTEMENT__MYNOTARY__AGENT',
      'OPERATION__SYNDIC__GENERAL'
    ];

    /* 1) For each type of operation, create an operation reference */
    for (const operationTemplate of operations) {
      const operationReference = defaultAnswerReferences.find(
        (reference) => reference.operationTemplate === operationTemplate
      );

      if (isEmpty(operationReference?.answers)) {
        continue;
      }

      const operation = await this.legalsApiService.createLegalOperationWithDefaultValues({
        isOperationReference: true,
        label: 'Operation par défaut',
        legalOperationTemplateId: operationTemplate,
        organizationId: args.organization.id,
        userId: args.adminId
      });

      if (operationReference == null) {
        throw new Exception(`Operation reference not found for operation ${operationTemplate}`);
      }

      const answerReferences = await this.orpiRepository.getDefaultAnswerReferences({
        operationId: operation.id.toString()
      });

      for (const answerReference of answerReferences) {
        const defaultAnswer = operationReference.answers.find((answer) => answer.type === answerReference.type);

        if (defaultAnswer != null) {
          await this.legalsApiService.updateLegalRecordAnswer({
            answer: defaultAnswer.answer,
            legalRecordId: answerReference.recordId
          });
        }
      }
    }
  }

  private findRole(args: { role: string; roles: Role[] }): Role {
    const targetRole = find(args.roles, (role) => role.name.toLowerCase() === args.role);

    if (targetRole == null) {
      throw new Exception(`Role ${args.role} not found in roles`);
    }

    return targetRole;
  }

  private getAdminId(args: { agency: OrpiAgency; members: Member[] }) {
    const adminId = args.members.find((member) => member.role === 'admin')?.user.id;

    if (adminId == null) {
      throw new Exception(`Admin not found in members for agency ${args.agency.id}`);
    }

    return adminId;
  }

  private agencyRecordAnswer(args: CreateDefaultRecordsArgs): AnswerDict {
    const res: AnswerDict = {};

    forEach(args.agency.answers, (value, key) => {
      // EDGE CASE: 'NULL' is a string value
      if (value && value != 'NULL' && value != 'null') {
        res[key] = { value };
      }
    });

    return res;
  }

  private mapIntermediaireAnswer(args: {
    contractType: ContractType;
    role: OrpiMemberRole;
    rsac?: string;
    user: OrpiUser;
  }): AnswerDict {
    return {
      email: { value: args.user.email },
      intermediaire_statut: { value: args.contractType },
      nom: { value: args.user.lastname },
      numero_rsac: { value: args.rsac },
      prenoms: { value: args.user.firstname },
      sexe: { value: args.user.civility === Civility.MAN ? 'homme' : 'femme' },
      telephone: { value: args.user.phone }
    };
  }

  private mapPersonnePhysiqueAnswer(args: { function?: string; user: OrpiUser }): AnswerDict {
    return {
      email: { value: args.user.email },
      informations_personnelles_nom_usage: { value: args.user.lastname },
      nom: { value: args.user.lastname },
      prenoms: { value: args.user.firstname },
      pro_fonction_societe: { value: args.function },
      sexe: { value: args.user.civility === Civility.MAN ? 'homme' : 'femme' },
      telephone: { value: args.user.phone }
    };
  }
}

export interface Organization {
  id: string;
  roles: Role[];
}

interface CreateMembersArgs {
  agency: OrpiAgency;
  organization: Organization;
}

export interface Member {
  contractType: ContractType;
  orpiUser: OrpiUser;
  role: OrpiMemberRole;
  // Registre Spécial des Agents Commerciaux
  rsac?: string;
  user: User;
}

interface CreateDefaultRecordsArgs {
  adminId: string;
  agency: OrpiAgency;
  members: Member[];
  organization: Organization;
}

interface LinkAgencyRepresentantArgs {
  adminId: string;
  agencyRecordId: string;
  organizationId: string;
  representantRecords: string[];
}

type ContractType = 'representant' | 'agent_co_salarie' | 'agent_co_independannt';

interface CreateAgencyArgs {
  code: string;
  holdingId: string;
}

interface CreateOrganizationArgs {
  agency: OrpiAgency;
  holdingId: string;
}
