openapi: 3.0.0
info:
  title: main-api
  version: '1.0'
  description: ...
servers:
  - url: 'http://localhost:3000'
    description: Test
  - url: 'http://localhost:3000/api-mynotary/v1'
    description: Local development
  - url: 'https://api.preproduction.mynotary.fr/api-mynotary/v1'
    description: Preproduction
  - url: 'https://api.production.mynotary.fr/api-mynotary/v1'
    description: Production
paths:
  '/ping':
    get:
      summary: Ping and return server ip
      tags:
        - Misc
      responses:
        '200':
          description: OK
  '/orpi-agencies':
    $ref: './specs/orpi.openapi.yaml#/paths/~1orpi-agencies'
  '/subscriptions':
    $ref: './specs/billings.openapi.yaml#/paths/~1subscriptions'
  '/subscriptions/{subscriptionId}':
    $ref: './specs/billings.openapi.yaml#/paths/~1subscriptions~1{subscriptionId}'
  '/payment-information':
    $ref: './specs/billings.openapi.yaml#/paths/~1payment-information'
  '/coupon-validation/{couponCode}':
    $ref: './specs/billings.openapi.yaml#/paths/~1coupon-validation~1{couponCode}'
  '/subscription-price-details':
    $ref: './specs/billings.openapi.yaml#/paths/~1subscription-price-details'
  '/subscription-activation':
    $ref: './specs/billings.openapi.yaml#/paths/~1subscription-activation'
  '/subscription-sync':
    $ref: './specs/billings.openapi.yaml#/paths/~1subscription-sync'
  '/credits':
    $ref: './specs/billings.openapi.yaml#/paths/~1credits'
  '/credits/{id}':
    $ref: './specs/billings.openapi.yaml#/paths/~1credits~1{id}'
  '/provider-invoices':
    $ref: './specs/billings.openapi.yaml#/paths/~1provider-invoices'
  '/invoice-file/{invoiceId}':
    $ref: './specs/billings.openapi.yaml#/paths/~1invoice-file~1{invoiceId}'
  '/contracts/{contractId}/archive':
    $ref: './specs/contracts.openapi.yaml#/paths/~1contracts~1{contractId}~1archive'
  '/contract-duplicates':
    $ref: './specs/contracts.openapi.yaml#/paths/~1contract-duplicates'
  '/contract-views':
    $ref: './specs/contract-views.openapi.yaml#/paths/~1contract-views'
  '/contract-validators':
    $ref: './specs/contract-validators.openapi.yaml#/paths/~1contract-validators'
  '/contract-validators/{id}':
    $ref: './specs/contract-validators.openapi.yaml#/paths/~1contract-validators~1{id}'
  '/admin/contracts-status/{contractId}':
    $ref: './specs/contracts.openapi.yaml#/paths/~1admin~1contracts-status~1{contractId}'
  '/contract-validation-requests':
    $ref: './specs/contract-validations.openapi.yaml#/paths/~1contract-validation-requests'
  '/contract-validation-requests/{contractId}':
    $ref: './specs/contract-validations.openapi.yaml#/paths/~1contract-validation-requests~1{contractId}'
  '/contract-validations':
    $ref: './specs/contract-validations.openapi.yaml#/paths/~1contract-validations'
  '/contract-validation/{contractId}':
    $ref: './specs/contract-validations.openapi.yaml#/paths/~1contract-validation~1{contractId}'
  '/contract-review-requests':
    $ref: './specs/contract-reviews.openapi.yaml#/paths/~1contract-review-requests'
  '/contract-review-requests/{contractId}':
    $ref: './specs/contract-reviews.openapi.yaml#/paths/~1contract-review-requests~1{contractId}'
  '/contract-reviews':
    $ref: './specs/contract-reviews.openapi.yaml#/paths/~1contract-reviews'
  '/contract-reviews/{contractId}':
    $ref: './specs/contract-reviews.openapi.yaml#/paths/~1contract-reviews~1{contractId}'
  '/external-organization-associations':
    $ref: './specs/external-apps.openapi.yaml#/paths/~1external-organization-associations'
  '/legal-record-data/{legalRecordId}':
    $ref: './specs/records.openapi.yaml#/paths/~1legal-record-data~1{legalRecordId}'
  '/records':
    $ref: './specs/records.openapi.yaml#/paths/~1records'
  '/records/{recordId}':
    $ref: './specs/records.openapi.yaml#/paths/~1records~1{recordId}'
  '/record-transfers':
    $ref: './specs/organization-data-transfers.openapi.yaml#/paths/~1record-transfers'
  '/legal-record-infos':
    $ref: './specs/records.openapi.yaml#/paths/~1legal-record-infos'
  '/operation-duplicates':
    $ref: './specs/operations.openapi.yaml#/paths/~1operation-duplicates'
  '/operations/{operationId}':
    $ref: './specs/operations.openapi.yaml#/paths/~1operations~1{operationId}'
  '/operations':
    $ref: './specs/operations.openapi.yaml#/paths/~1operations'
  '/operation-transfers':
    $ref: './specs/organization-data-transfers.openapi.yaml#/paths/~1operation-transfers'
  '/operation-views':
    $ref: './specs/operation-views.openapi.yaml#/paths/~1operation-views'
  '/operation-access':
    $ref: './specs/operation-access.openapi.yaml#/paths/~1operation-access'
  '/operation-access/{id}':
    $ref: './specs/operation-access.openapi.yaml#/paths/~1operation-access~1{id}'
  '/organizations/{organizationId}/operation-references':
    $ref: './specs/default-answers.openapi.yaml#/paths/~1organizations~1{organizationId}~1operation-references'
  '/operation-default-answers':
    $ref: './specs/default-answers.openapi.yaml#/paths/~1operation-default-answers'
  '/organizations/{organizationId}/operation-default-answers/{defaultAnswerId}':
    $ref: './specs/default-answers.openapi.yaml#/paths/~1organizations~1{organizationId}~1operation-default-answers~1{defaultAnswerId}'
  '/users':
    $ref: './specs/users.openapi.yaml#/paths/~1users'
  '/users/{userId}':
    $ref: './specs/users.openapi.yaml#/paths/~1users~1{userId}'
  '/users/{userId}/latest':
    $ref: './specs/users.openapi.yaml#/paths/~1users~1{userId}~1latest'
  '/roles':
    $ref: './specs/roles.openapi.yaml#/paths/~1roles'
  '/roles/{roleId}':
    $ref: './specs/roles.openapi.yaml#/paths/~1roles~1{roleId}'
  '/role-default-permissions':
    $ref: './specs/roles.openapi.yaml#/paths/~1role-default-permissions'
  '/contract-default-permissions':
    $ref: './specs/roles.openapi.yaml#/paths/~1contract-default-permissions'
  '/deprecated-permissions':
    $ref: './specs/roles.openapi.yaml#/paths/~1deprecated-permissions'
  '/permissions/{permissionId}':
    $ref: './specs/roles.openapi.yaml#/paths/~1permissions~1{permissionId}'
  '/permissions':
    $ref: './specs/roles.openapi.yaml#/paths/~1permissions'
  '/custom-views':
    $ref: './specs/custom-views.openapi.yaml#/paths/~1custom-views'
  '/themes':
    $ref: './specs/themes.openapi.yaml#/paths/~1themes'
  '/theme-applications/{id}':
    $ref: './specs/themes.openapi.yaml#/paths/~1theme-applications~1{id}'
  '/theme-contracts/{id}':
    $ref: './specs/themes.openapi.yaml#/paths/~1theme-contracts~1{id}'
  '/theme-contracts':
    $ref: './specs/themes.openapi.yaml#/paths/~1theme-contracts'
  '/theme-emails/{id}':
    $ref: './specs/themes.openapi.yaml#/paths/~1theme-emails~1{id}'
  '/features':
    $ref: './specs/features.openapi.yaml#/paths/~1features'
  '/features/{id}':
    $ref: './specs/features.openapi.yaml#/paths/~1features~1{id}'
  '/unis-users':
    $ref: './specs/unis.openapi.yaml#/paths/~1unis-users'
  '/unis-organizations':
    $ref: './specs/unis.openapi.yaml#/paths/~1unis-organizations'
  '/organizations':
    $ref: './specs/organizations.openapi.yaml#/paths/~1organizations'
  '/organizations/{organizationId}':
    $ref: './specs/organizations.openapi.yaml#/paths/~1organizations~1{organizationId}'
  '/tableau-config':
    $ref: './specs/data-tableau.openapi.yaml#/paths/~1tableau-config'
  '/tableau-config/{id}':
    $ref: './specs/data-tableau.openapi.yaml#/paths/~1tableau-config~1{id}'
  '/tableau-tokens':
    $ref: './specs/data-tableau.openapi.yaml#/paths/~1tableau-tokens'
  '/register-entries':
    $ref: './specs/registers.openapi.yaml#/paths/~1register-entries'
  '/transaction-registers':
    $ref: './specs/registers.openapi.yaml#/paths/~1transaction-registers'
  '/transaction-registers/{id}':
    $ref: './specs/registers.openapi.yaml#/paths/~1transaction-registers~1{id}'
  '/management-registers':
    $ref: './specs/registers.openapi.yaml#/paths/~1management-registers'
  '/management-registers/{id}':
    $ref: './specs/registers.openapi.yaml#/paths/~1management-registers~1{id}'
  '/receivership-registers':
    $ref: './specs/registers.openapi.yaml#/paths/~1receivership-registers'
  '/receivership-registers/{id}':
    $ref: './specs/registers.openapi.yaml#/paths/~1receivership-registers~1{id}'
  '/registered-letter-batches/{batchId}':
    $ref: './specs/registered-letters.openapi.yaml#/paths/~1registered-letter-batches~1{batchId}'
  '/registered-letters-sync/{letterId}':
    $ref: './specs/registered-letters.openapi.yaml#/paths/~1registered-letters-sync~1{letterId}'
  '/registered-letter-archives/{batchId}':
    $ref: './specs/registered-letters.openapi.yaml#/paths/~1registered-letter-archives~1{batchId}'
  '/registered-letter-receiver':
    $ref: './specs/registered-letters.openapi.yaml#/paths/~1registered-letter-receiver'
  '/external-subscriptions/{subscriptionId}':
    $ref: './specs/billings.openapi.yaml#/paths/~1external-subscriptions~1{subscriptionId}'
  '/members':
    $ref: './specs/members.openapi.yaml#/paths/~1members'
  '/members/{memberId}':
    $ref: './specs/members.openapi.yaml#/paths/~1members~1{memberId}'
  '/member-setups/{memberId}':
    $ref: './specs/members.openapi.yaml#/paths/~1member-setups~1{memberId}'
  '/missing-branches':
    $ref: './specs/legal-branches.openapi.yaml#/paths/~1missing-branches'
  '/legal-links':
    $ref: './specs/legal-links.openapi.yaml#/paths/~1legal-links'
  '/legal-operations':
    $ref: './specs/legal-operations.openapi.yaml#/paths/~1legal-operations'
  '/legal-operation-infos':
    $ref: './specs/legal-operations.openapi.yaml#/paths/~1legal-operation-infos'
  '/legal-links/{linkId}':
    $ref: './specs/legal-links.openapi.yaml#/paths/~1legal-links~1{linkId}'
  '/legal-branches':
    $ref: './specs/legal-branches.openapi.yaml#/paths/~1legal-branches'
  '/legal-branches/{branchId}':
    $ref: './specs/legal-branches.openapi.yaml#/paths/~1legal-branches~1{branchId}'
  '/drives':
    $ref: './specs/drives.openapi.yaml#/paths/~1drives'
  '/drive-files':
    $ref: './specs/drives.openapi.yaml#/paths/~1drive-files'
  '/drive-files/{driveFileId}':
    $ref: './specs/drives.openapi.yaml#/paths/~1drive-files~1{driveFileId}'
  '/drive-folders':
    $ref: './specs/drives.openapi.yaml#/paths/~1drive-folders'
  '/drive-folders/{folderId}':
    $ref: './specs/drives.openapi.yaml#/paths/~1drive-folders~1{folderId}'
  '/annexed-documents':
    $ref: './specs/annexed-documents.openapi.yaml#/paths/~1annexed-documents'
  '/document-requests':
    $ref: './specs/document-requests.openapi.yaml#/paths/~1document-requests'
  '/document-requests/{id}':
    $ref: './specs/document-requests.openapi.yaml#/paths/~1document-requests~1{id}'
  '/notifications':
    $ref: './specs/notifications.openapi.yaml#/paths/~1notifications'
  '/notifications/{notificationUserId}':
    $ref: './specs/notifications.openapi.yaml#/paths/~1notifications~1{notificationUserId}'
  '/notification-unseen':
    $ref: './specs/notifications.openapi.yaml#/paths/~1notification-unseen'
  '/task-expired':
    $ref: './specs/tasks.openapi.yaml#/paths/~1task-expired'
  '/notification-seen':
    $ref: './specs/notifications.openapi.yaml#/paths/~1notification-seen'
  '/invoice-configs':
    $ref: './specs/invoices.openapi.yaml#/paths/~1invoice-configs'
  '/invoice-configs/{id}':
    $ref: './specs/invoices.openapi.yaml#/paths/~1invoice-configs~1{id}'
  '/invoices':
    $ref: './specs/invoices.openapi.yaml#/paths/~1invoices'
  '/invoices/{id}':
    $ref: './specs/invoices.openapi.yaml#/paths/~1invoices~1{id}'
  '/invoice-files/{invoiceId}':
    $ref: './specs/invoices.openapi.yaml#/paths/~1invoice-files~1{invoiceId}'
  '/invoice-emails':
    $ref: './specs/invoice-emails.openapi.yaml#/paths/~1invoice-emails'
  '/invoice-reports':
    $ref: './specs/invoices.openapi.yaml#/paths/~1invoice-reports'
  '/legal-record-form':
    $ref: './specs/legal-templates.openapi.yaml#/paths/~1legal-record-form'
  '/learn-worlds-sso':
    $ref: './specs/learn-worlds.openapi.yaml#/paths/~1learn-worlds-sso'
  '/operation-invitations':
    $ref: './specs/operation-invitations.openapi.yaml#/paths/~1operation-invitations'
  '/operation-invitations/{id}':
    $ref: './specs/operation-invitations.openapi.yaml#/paths/~1operation-invitations~1{id}'
  '/operation-roles':
    $ref: './specs/operation-invitations.openapi.yaml#/paths/~1operation-roles'
  '/data-analytics':
    $ref: './specs/data-analytics.openapi.yaml#/paths/~1data-analytics'
  '/program-synthesis':
    $ref: './specs/programs.openapi.yaml#/paths/~1program-synthesis'
  '/coproprietes':
    $ref: './specs/coproprietes.openapi.yaml#/paths/~1coproprietes'
  '/legal-record-export-tasks':
    $ref: './specs/legal-record-exports.openapi.yaml#/paths/~1legal-record-export-tasks'
  '/legal-record-exports':
    $ref: './specs/legal-record-exports.openapi.yaml#/paths/~1legal-record-exports'
  '/organization-setups/{organizationId}':
    $ref: './specs/organization-setups.openapi.yaml#/paths/~1organization-setups~1{organizationId}'
  '/associations/{associationId}':
    $ref: './specs/external-apps.openapi.yaml#/paths/~1associations~1{associationId}'
  '/default-records':
    $ref: './specs/default-records.openapi.yaml#/paths/~1default-records'
  '/gel-avoirs':
    $ref: './specs/gel-avoirs.openapi.yaml#/paths/~1gel-avoirs'
  '/orders':
    $ref: './specs/orders.openapi.yaml#/paths/~1orders'
  '/orders/{id}':
    $ref: './specs/orders.openapi.yaml#/paths/~1orders~1{id}'
  '/payment-webhooks':
    $ref: './specs/billings.openapi.yaml#/paths/~1payment-webhooks'
  '/credit-histories':
    $ref: './specs/billings.openapi.yaml#/paths/~1credit-histories'
  '/pre-etat-dates':
    $ref: './specs/pre-etat-dates.openapi.yaml#/paths/~1pre-etat-dates'
  '/pre-etat-dates-webhook':
    $ref: './specs/pre-etat-dates.openapi.yaml#/paths/~1pre-etat-dates-webhook'
  '/user-emails':
    $ref: './specs/users.openapi.yaml#/paths/~1user-emails'
  '/contracts':
    $ref: './specs/contracts.openapi.yaml#/paths/~1contracts'
  '/registered-letters':
    $ref: './specs/registered-letters.openapi.yaml#/paths/~1registered-letters'
