Task:
  type: object
  properties:
    assignees:
      type: array
      items:
        $ref: './task-assignee.openapi.yaml#/TaskAssignee'
    creationTime:
      type: string
      format: date-time
    creatorUserId:
      type: string
    creatorEmail:
      type: string
    creatorFirstname:
      type: string
    creatorLastname:
      type: string
    creatorPhone:
      type: string
    creatorProfilePictureFileId:
      type: string
    description:
      type: string
    id:
      type: string
    legalOperationId:
      type: string
    contractId:
      type: string
    organizationName:
      type: string
    organizationId:
      type: string
    organizationAddress:
      $ref: './address.openapi.yaml#/Address'
    seen:
      type: boolean
    title:
      type: string
    type:
      $ref: './task-type.openapi.yaml#/TaskType'
  required:
    - assignees
    - creationTime
    - id
    - legalOperationId
    - seen
    - title
    - type
    - creatorUserId
    - creatorEmail
    - creatorFirstname
    - creatorLastname
    - organizationName
    - organizationId
    - organizationAddress
