/**
 * main-api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TaskNotificationTypeDto } from './task-notification-type-dto';


export interface TaskNotificationNewDto { 
    userIds: Array<string>;
    type: TaskNotificationTypeDto;
    contractId?: string;
    operationId: string;
    taskId: string;
    creatorId: string;
    creatorFirstname: string;
    creatorLastname: string;
    receivers?: Array<string>;
    expirationDate?: string;
    title: string;
}



