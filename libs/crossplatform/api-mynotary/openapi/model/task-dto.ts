/**
 * main-api
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { TaskTypeDto } from './task-type-dto';
import { TaskAssigneeDto } from './task-assignee-dto';
import { AddressDto } from './address-dto';


export interface TaskDto { 
    assignees: Array<TaskAssigneeDto>;
    creationTime: string;
    creatorUserId: string;
    creatorEmail: string;
    creatorFirstname: string;
    creatorLastname: string;
    creatorPhone?: string;
    creatorProfilePictureFileId?: string;
    description?: string;
    id: string;
    legalOperationId: string;
    contractId?: string;
    organizationName: string;
    organizationId: string;
    organizationAddress: AddressDto;
    seen: boolean;
    title: string;
    type: TaskTypeDto;
}



