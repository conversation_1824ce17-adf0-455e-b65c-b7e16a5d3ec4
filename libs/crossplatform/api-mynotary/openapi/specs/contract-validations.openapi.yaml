openapi: 3.0.0
info:
  title: contract-validations
  version: '1.0'
paths:
  '/contract-validation-requests':
    post:
      tags: ['Contract Validations']
      summary: Create a contract validation request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContractValidationRequestNew'
      responses:
        '200':
          description: Contract validation request created successfully
          content:
            application/json:
              schema:
                $ref: '../shared-components/task.openapi.yaml#/Task'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  '/contract-validation-requests/{contractId}':
    delete:
      tags: ['Contract Validations']
      summary: Delete a contract validation request
      parameters:
        - schema:
            type: string
          name: contractId
          in: path
          required: true
      responses:
        '200':
          description: Contract validation request deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskDeleted'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
  '/contract-validations':
    post:
      tags: ['Contract Validations']
      summary: Validate a contract
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContractValidationNew'
      responses:
        '204':
          description: Contract validated successfully
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  '/contract-validation/{contractId}':
    delete:
      tags: ['Contract Validations']
      summary: Cancel a contract validation
      parameters:
        - schema:
            type: string
          name: contractId
          in: path
          required: true
      responses:
        '204':
          description: Contract validation cancelled successfully
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
components:
  schemas:
    ContractValidationRequestNew:
      type: object
      properties:
        contractId:
          type: string
        userId:
          type: string
        title:
          type: string
        description:
          type: string
        emailAssignees:
          type: array
          items:
            type: string
        emailSubject:
          type: string
        emailContent:
          type: string
      required:
        - contractId
        - userId
        - title
        - emailAssignees
        - emailSubject
        - emailContent

    ContractValidationNew:
      type: object
      properties:
        contractId:
          type: string
        fileId:
          type: string
        userId:
          type: string
      required:
        - contractId
        - fileId
        - userId

    TaskDeleted:
      $ref: '../shared-components/task-deleted.openapi.yaml#/TaskDeleted'
