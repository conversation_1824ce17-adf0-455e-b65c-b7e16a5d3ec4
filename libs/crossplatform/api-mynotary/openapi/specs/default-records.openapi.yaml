openapi: 3.0.0
info:
  title: default-answers
  version: '1.0'
paths:
  /default-records:
    get:
      summary: Get Default Records
      description: Fetches a list of default records for a specific organization.
      operationId: getDefaultRecords
      tags:
        - Default Records
      parameters:
        - name: organizationId
          in: query
          required: false
          description: The ID of the organization to fetch default records for.
          schema:
            type: string
        - name: legalOperationId
          in: query
          required: false
          description: The ID of the operation to fetch default records for.
          schema:
            type: string
      responses:
        '200':
          description: A list of default records.
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/DefaultRecordsList'
        '400':
          description: Invalid organization ID provided.
        '401':
          description: Unauthorized access.
components:
  schemas:
    DefaultRecordsList:
      type: object
      properties:
        items:
          type: array
          description: A list of default records.
          items:
            $ref: '#/components/schemas/DefaultRecord'
      required:
        - items
    DefaultRecord:
      type: object
      properties:
        id:
          type: string
          description: Unique identifier for the default record.
        isOrganizationRecord:
          type: boolean
          description: Indicates if the record is tied to an organization.
        legalLinkTemplateId:
          type: string
          description: Identifier for the associated link template.
        legalOperationTemplateId:
          type: string
          description: Identifier for the associated operation template.
        organizationId:
          type: string
          description: The ID of the organization the record belongs to.
        legalRecordId:
          type: string
          description: Identifier for the record.
        userId:
          type: string
          description: Identifier of the user associated with the record.
      required:
        - id
        - isOrganizationRecord
        - legalLinkTemplateId
        - legalOperationTemplateId
        - organizationId
        - legalRecordId
        - userId
