openapi: 3.0.0
info:
  title: themes
  version: '1.0'
paths:
  '/themes':
    get:
      tags: ['Themes']
      summary: Get themes
      parameters:
        - schema:
            type: array
            items:
              type: string
          name: organizationIds
          in: query
          required: true
      responses:
        '200':
          description: The color and logo used to style the application and contract
          content:
            application/json:
              schema:
                type: array
                items:
                  $ref: '#/components/schemas/Theme'
  '/theme-contracts':
    put:
      summary: Batch update themes for organizations
      description: Allows an administrator to update themes for specified organizations.
      operationId: updateThemes
      tags:
        - Themes
      security:
        - AdminAuth: []
      requestBody:
        required: true
        content:
          application/json:
            schema:
              type: object
              properties:
                organizationIds:
                  type: array
                  items:
                    type: string
                  description: List of organization IDs to which the theme update will be applied.
                theme:
                  $ref: '#/components/schemas/BatchContractsThemeUpdate'
              required:
                - organizationIds
                - theme
      responses:
        '200':
          description: Themes updated successfully.
        '400':
          description: Bad request. Validation error.
        '403':
          description: Forbidden. Only administrators can perform this operation.
  '/theme-applications/{id}':
    put:
      summary: Update application theme configuration
      tags: ['Themes']
      responses:
        '204':
          description: No Content
      parameters:
        - schema:
            type: string
          name: id
          in: path
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ApplicationThemeUpdate'
  '/theme-contracts/{id}':
    put:
      summary: Update contracts theme configuration
      tags: ['Themes']
      responses:
        '204':
          description: No Content
      parameters:
        - schema:
            type: string
          name: id
          in: path
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContractsThemeUpdate'
  '/theme-emails/{id}':
    put:
      summary: Update emails theme configuration
      tags: ['Themes']
      responses:
        '204':
          description: No Content
      parameters:
        - schema:
            type: string
          name: id
          in: path
          required: true
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/EmailsThemeUpdate'

components:
  schemas:
    Theme:
      title: Theme
      type: object
      properties:
        application:
          $ref: '#/components/schemas/ApplicationTheme'
        contracts:
          $ref: '#/components/schemas/ContractsTheme'
        emails:
          $ref: '#/components/schemas/EmailsTheme'
        organization:
          $ref: '#/components/schemas/OrganizationTheme'
      required:
        - application
        - contracts
        - emails
        - organization
    ContractsTheme:
      type: object
      properties:
        organizationId:
          type: string
        id:
          type: string
        backCover:
          type: string
        color:
          type: string
        cover:
          type: string
        displayTable:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              $ref: '#/components/schemas/DisplayContractEntity'
        flyLeafImage:
          type: string
        logo:
          type: string
        logoFooter:
          type: string
      required:
        - advanced
        - color
        - displayTable
        - flyLeafImage
        - logo
        - logoFooter
    ApplicationTheme:
      type: object
      properties:
        organizationId:
          type: string
        id:
          type: string
        favicon:
          type: string
          nullable: true
        logo:
          type: string
        mainColor:
          type: string
        name:
          type: string
      required:
        - logo
        - mainColor
        - name
    EmailsTheme:
      type: object
      properties:
        organizationId:
          type: string
        id:
          type: string
        logo:
          type: string
        mainColor:
          type: string
      required:
        - logo
        - mainColor
    ApplicationThemeUpdate:
      properties:
        organizationId:
          type: string
        favicon:
          type: string
          nullable: true
        logo:
          type: string
          nullable: true
        mainColor:
          type: string
        name:
          type: string
      required:
        - organizationId
    BatchContractsThemeUpdate:
      type: object
      properties:
        displayTable:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              $ref: '#/components/schemas/DisplayContractEntity'
          description: Structure defining the display settings of contracts.
      required:
        - displayTable
    ContractsThemeUpdate:
      properties:
        organizationId:
          type: string
        backCover:
          type: string
          nullable: true
        color:
          type: string
        cover:
          type: string
          nullable: true
        displayTable:
          type: object
          additionalProperties:
            type: object
            additionalProperties:
              $ref: '#/components/schemas/DisplayContractEntity'
        flyLeafImage:
          type: string
          nullable: true
        logo:
          type: string
          nullable: true
        logoFooter:
          type: string
          nullable: true
      required:
        - organizationId
    EmailsThemeUpdate:
      properties:
        organizationId:
          type: string
        id:
          type: string
        logo:
          type: string
          nullable: true
        mainColor:
          type: string
      required:
        - organizationId
    OrganizationTheme:
      title: OrganizationTheme
      type: object
      properties:
        name:
          type: string
        address:
          type: string
      required:
        - name
    DisplayContractEntity:
        type: object
        properties:
          COVER:
            type: boolean
          FLY_LEAF:
            type: boolean
          BACK_COVER:
            type: boolean
          FLY_LEAF_IMAGE:
            type: boolean
          FLY_LEAF_ORGA_INFO:
            type: boolean
          logo:
            type: string
          logoFooter:
            type: string
          cover:
            type: string
          backCover:
            type: string
          flyLeafImage:
            type: string
          color:
            type: string
        required:
          - COVER
          - FLY_LEAF
          - BACK_COVER
          - FLY_LEAF_IMAGE
          - FLY_LEAF_ORGA_INFO
