openapi: 3.0.0
info:
  title: contract-reviews
  version: '1.0'
paths:
  '/contract-review-requests':
    post:
      tags: ['Contract Reviews']
      summary: Create a contract review request
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContractReviewRequestNew'
      responses:
        '200':
          description: Contract review request created successfully
          content:
            application/json:
              schema:
                $ref: '../shared-components/task.openapi.yaml#/Task'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  '/contract-review-requests/{contractId}':
    delete:
      tags: ['Contract Reviews']
      summary: Delete a contract review request
      parameters:
        - schema:
            type: string
          name: contractId
          in: path
          required: true
      responses:
        '200':
          description: Contract review request deleted successfully
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/TaskDeleted'
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
  '/contract-reviews':
    post:
      tags: ['Contract Reviews']
      summary: Create a contract review
      requestBody:
        required: true
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ContractReview'
      responses:
        '204':
          description: Contract review created successfully
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
  '/contract-reviews/{contractId}':
    delete:
      tags: ['Contract Reviews']
      summary: Delete a contract review
      parameters:
        - schema:
            type: string
          name: contractId
          in: path
          required: true
      responses:
        '204':
          description: Contract review deleted successfully
        '400':
          description: Bad request
        '401':
          description: Unauthorized
        '403':
          description: Forbidden
        '404':
          description: Not found
components:
  schemas:
    ContractReviewRequestNew:
      type: object
      properties:
        contractId:
          type: string
        userId:
          type: string
        title:
          type: string
        description:
          type: string
        emailAssignees:
          type: array
          items:
            type: string
        emailSubject:
          type: string
        emailContent:
          type: string
      required:
        - contractId
        - userId
        - title
        - emailAssignees
        - emailSubject
        - emailContent

    ContractReview:
      type: object
      properties:
        contractId:
          type: string
        userId:
          type: string
      required:
        - contractId
        - userId

    TaskType:
      $ref: '../shared-components/task-type.openapi.yaml#/TaskType'

    TaskDeleted:
      $ref: '../shared-components/task-deleted.openapi.yaml#/TaskDeleted'
