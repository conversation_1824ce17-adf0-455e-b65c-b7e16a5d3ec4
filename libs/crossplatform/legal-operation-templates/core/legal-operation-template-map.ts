// This code is autogenerated don't modify it manually
import { LegalOperationTemplate, LegalOperationTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import * as templates from './operations';

export const legalOperationTemplateMap: Record<LegalOperationTemplateId, LegalOperationTemplate> = {
  OPERATION__AGENCE_DIRECTE__IMMOBILIER: templates.OperationAgenceDirecteImmobilier,
  OPERATION__AJP__DOSSIER_DE_LOCATION_AJP: templates.OperationAjpDossierDeLocationAjp,
  OPERATION__AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP:
    templates.OperationAjpDossierDeLocationBienProfessionnelAjp,
  OPERATION__AJP__DOSSIER_DE_VENTE_AJP: templates.OperationAjpDossierDeVenteAjp,
  OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP: templates.OperationAjpDossierDeVenteBienProfessionnelAjp,
  OPERATION__ALLOWA__IMMOBILIER: templates.OperationAllowaImmobilier,
  OPERATION__AQUITANIS__PROGRAMME_ANCIEN: templates.OperationAquitanisProgrammeAncien,
  OPERATION__AQUITANIS__PROGRAMME_BRS: templates.OperationAquitanisProgrammeBrs,
  OPERATION__AQUITANIS__RESERVATION_BRS: templates.OperationAquitanisReservationBrs,
  OPERATION__AQUITANIS__VENTE_ANCIEN: templates.OperationAquitanisVenteAncien,
  OPERATION__AUTRE__LIBRE: templates.OperationAutreLibre,
  OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION: templates.OperationAxoActifImmobilierHabitation,
  OPERATION__BEAUX_VILLAGES__IMMOBILIER: templates.OperationBeauxVillagesImmobilier,
  OPERATION__BENEDIC__DOSSIER_DE_LOCATION_BENEDIC: templates.OperationBenedicDossierDeLocationBenedic,
  OPERATION__BENEDIC__IMMOBILIER: templates.OperationBenedicImmobilier,
  OPERATION__BLOT__IMMOBILIER: templates.OperationBlotImmobilier,
  OPERATION__C2I__CONTRAT_RECRUTEMENT: templates.OperationC2IContratRecrutement,
  OPERATION__C2I__FORMATION: templates.OperationC2IFormation,
  OPERATION__C2I__FORMATION_MAESTRO: templates.OperationC2IFormationMaestro,
  OPERATION__CANNISIMMO__IMMOBILIER: templates.OperationCannisimmoImmobilier,
  OPERATION__CDC__IMMOBILIER__PROGRAMME: templates.OperationCdcImmobilierProgramme,
  OPERATION__CDC__IMMOBILIER__VENTE: templates.OperationCdcImmobilierVente,
  OPERATION__CDC__PROGRAMME_IMMOBILIER_PSLA: templates.OperationCdcProgrammeImmobilierPsla,
  OPERATION__CDC__VENTE_IMMOBILIER_PSLA: templates.OperationCdcVenteImmobilierPsla,
  OPERATION__CLESENCE__IMMOBILIER__PROGRAMME: templates.OperationClesenceImmobilierProgramme,
  OPERATION__CLESENCE__IMMOBILIER__PROGRAMME_NEUF: templates.OperationClesenceImmobilierProgrammeNeuf,
  OPERATION__CLESENCE__IMMOBILIER__VENTE: templates.OperationClesenceImmobilierVente,
  OPERATION__CLESENCE__IMMOBILIER__VENTE_NEUF: templates.OperationClesenceImmobilierVenteNeuf,
  OPERATION__COLDWELL_BANKER__CONTRAT_NEGOCIATEUR: templates.OperationColdwellBankerContratNegociateur,
  OPERATION__COLDWELL_BANKER__IMMOBILIER: templates.OperationColdwellBankerImmobilier,
  OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE: templates.OperationCoteParticuliersImmobilierVente,
  OPERATION__DESIMO__IMMOBILIER__PROGRAMME: templates.OperationDesimoImmobilierProgramme,
  OPERATION__DESIMO__IMMOBILIER__VENTE: templates.OperationDesimoImmobilierVente,
  OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME: templates.OperationDomofranceImmobilierAcheveProgramme,
  OPERATION__DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE: templates.OperationDomofranceImmobilierAcheveVente,
  OPERATION__DOMOFRANCE__IMMOBILIER__PROGRAMME: templates.OperationDomofranceImmobilierProgramme,
  OPERATION__DOMOFRANCE__IMMOBILIER__VENTE: templates.OperationDomofranceImmobilierVente,
  OPERATION__DUVAL__IMMOBILIER__PROGRAMME_VEFA: templates.OperationDuvalImmobilierProgrammeVefa,
  OPERATION__DUVAL__IMMOBILIER__VEFA_RESERVATION: templates.OperationDuvalImmobilierVefaReservation,
  OPERATION__EFFICITY__IMMOBILIER: templates.OperationEfficityImmobilier,
  OPERATION__EFFICITY__IMMOBILIER_COMMERCIAL: templates.OperationEfficityImmobilierCommercial,
  OPERATION__EFFICITY__IMMOBILIER__LOCATION__COMMERCIAL: templates.OperationEfficityImmobilierLocationCommercial,
  OPERATION__EFFICITY__IMMOBILIER__LOCATION__HABITATION: templates.OperationEfficityImmobilierLocationHabitation,
  OPERATION__ERA__IMMOBILIER__LOCATION: templates.OperationEraImmobilierLocation,
  OPERATION__ERA__IMMOBILIER__VENTE: templates.OperationEraImmobilierVente,
  OPERATION__ERIGERE__ERIGERE_PROGRAMME_VEFA: templates.OperationErigereErigereProgrammeVefa,
  OPERATION__ERIGERE__ERIGERE_VEFA_RESERVATION: templates.OperationErigereErigereVefaReservation,
  OPERATION__FOLLIOT__IMMOBILIER: templates.OperationFolliotImmobilier,
  OPERATION__FOLLIOT__IMMOBILIER_PRO: templates.OperationFolliotImmobilierPro,
  OPERATION__FRANCE_FORESTRY__IMMOBILIER: templates.OperationFranceForestryImmobilier,
  OPERATION__GIBOIRE__IMMOBILIER__VENTE: templates.OperationGiboireImmobilierVente,
  OPERATION__HERMES__IMMOBILIER__LOCATION_PRO: templates.OperationHermesImmobilierLocationPro,
  OPERATION__HERMES__IMMOBILIER__TRANSACTION_PRO: templates.OperationHermesImmobilierTransactionPro,
  OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE: templates.OperationI3FImmobilierBrsPreliminaire,
  OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME: templates.OperationI3FImmobilierBrsProgramme,
  OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE: templates.OperationI3FImmobilierPslaPreliminaire,
  OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME: templates.OperationI3FImmobilierPslaProgramme,
  OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME: templates.OperationI3FImmobilierVefaProgramme,
  OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION: templates.OperationI3FImmobilierVefaReservation,
  OPERATION__ICM__CONTRAT_APPORTEUR_DAFFAIRE_ICM: templates.OperationIcmContratApporteurDaffaireIcm,
  OPERATION__ICM__DOSSIER_DE_VENTE_ICM: templates.OperationIcmDossierDeVenteIcm,
  OPERATION__IDEAL__IMMOBILIER__PROGRAMME: templates.OperationIdealImmobilierProgramme,
  OPERATION__IDEAL__IMMOBILIER__VENTE: templates.OperationIdealImmobilierVente,
  OPERATION__IMMOBILIER__BRS_PROGRAMME_HYBRIDE: templates.OperationImmobilierBrsProgrammeHybride,
  OPERATION__IMMOBILIER__BRS_VENTE_HYBRIDE: templates.OperationImmobilierBrsVenteHybride,
  OPERATION__IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE: templates.OperationImmobilierConstructionMaisonIndividuelle,
  OPERATION__IMMOBILIER__LOCATION: templates.OperationImmobilierLocation,
  OPERATION__IMMOBILIER__LOCATION_COMMERCIAL: templates.OperationImmobilierLocationCommercial,
  OPERATION__IMMOBILIER__LOCATION_VISITE: templates.OperationImmobilierLocationVisite,
  OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME: templates.OperationImmobilierLotissementProgramme,
  OPERATION__IMMOBILIER__LOTISSEMENT_VENTE: templates.OperationImmobilierLotissementVente,
  OPERATION__IMMOBILIER__POLYNESIE__VENTE: templates.OperationImmobilierPolynesieVente,
  OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE: templates.OperationImmobilierPslaPreliminaire,
  OPERATION__IMMOBILIER__PSLA_PROGRAMME: templates.OperationImmobilierPslaProgramme,
  OPERATION__IMMOBILIER__SOCIAL_PROGRAMME: templates.OperationImmobilierSocialProgramme,
  OPERATION__IMMOBILIER__SOCIAL_VENTE: templates.OperationImmobilierSocialVente,
  OPERATION__IMMOBILIER__VENTES_PROGRAMME: templates.OperationImmobilierVentesProgramme,
  OPERATION__IMMOBILIER__VENTE_ANCIEN: templates.OperationImmobilierVenteAncien,
  OPERATION__IMMOBILIER__VENTE_ANCIEN_ESP: templates.OperationImmobilierVenteAncienEsp,
  OPERATION__IMMOBILIER__VENTE_BIEN_PROFESSIONNEL: templates.OperationImmobilierVenteBienProfessionnel,
  OPERATION__IMMOBILIER__VENTE_NEUF: templates.OperationImmobilierVenteNeuf,
  OPERATION__IMMOBILIER__VENTE_VIAGER: templates.OperationImmobilierVenteViager,
  OPERATION__IMOCONSEIL__IMMOBILIER__VENTE: templates.OperationImoconseilImmobilierVente,
  OPERATION__ISM__IMMOBILIER: templates.OperationIsmImmobilier,
  OPERATION__I_PARTICULIERS__IMMOBILIER__ESPAGNE: templates.OperationIParticuliersImmobilierEspagne,
  OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION: templates.OperationIParticuliersImmobilierHabitation,
  OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE: templates.OperationIParticuliersImmobilierLuxe,
  OPERATION__I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL: templates.OperationIParticuliersImmobilierProfessionnel,
  OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE: templates.OperationIParticuliersImmobilierSuisse,
  OPERATION__JOHN_TAYLOR_ALPILLES__VENTE_JOHN_TAYLOR_ALPILLES:
    templates.OperationJohnTaylorAlpillesVenteJohnTaylorAlpilles,
  OPERATION__JOHN_TAYLOR_CORSE__VENTE_JOHN_TAYLOR_CORSE: templates.OperationJohnTaylorCorseVenteJohnTaylorCorse,
  OPERATION__JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION: templates.OperationJohnTaylorImmobilierLocationHabitation,
  OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION: templates.OperationJohnTaylorImmobilierVenteHabitation,
  OPERATION__KELLER_WILLIAMS__IMMOBILIER: templates.OperationKellerWilliamsImmobilier,
  OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL: templates.OperationKellerWilliamsImmobilierCommercial,
  OPERATION__KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION:
    templates.OperationKellerWilliamsImmobilierLocationHabitation,
  OPERATION__KELLER_WILLIAMS__IMMOBILIER_VIAGER: templates.OperationKellerWilliamsImmobilierViager,
  OPERATION__KELLER_WILLIAMS__LOCATION_COMMERCIAL: templates.OperationKellerWilliamsLocationCommercial,
  OPERATION__KEREDES__IMMOBILIER_BRS: templates.OperationKeredesImmobilierBrs,
  OPERATION__KW_ABONDANCE__IMMOBILIER: templates.OperationKwAbondanceImmobilier,
  OPERATION__LFEUR__IMMOBILIER__PROGRAMME: templates.OperationLfeurImmobilierProgramme,
  OPERATION__LFEUR__IMMOBILIER__VENTE: templates.OperationLfeurImmobilierVente,
  OPERATION__LGM__IMMOBILIER: templates.OperationLgmImmobilier,
  OPERATION__MA_GESTION_LOCATIVE__LOCATION: templates.OperationMaGestionLocativeLocation,
  OPERATION__MA_REGIE__LOCATION: templates.OperationMaRegieLocation,
  OPERATION__MLS__IMMOBILIER: templates.OperationMlsImmobilier,
  OPERATION__MYNOTARY__CONTRAT_SAAS: templates.OperationMynotaryContratSaas,
  OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE: templates.OperationMyChezMoiImmobilierVente,
  OPERATION__ORGANISATION_INTERNE: templates.OperationOrganisationInterne,
  OPERATION__ORPI__IMMOBILIER__LOCATION: templates.OperationOrpiImmobilierLocation,
  OPERATION__ORPI__IMMOBILIER__VENTE: templates.OperationOrpiImmobilierVente,
  OPERATION__OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE: templates.OperationOzanamImmobilierPslaPreliminaire,
  OPERATION__OZANAM__IMMOBILIER__PSLA_PROGRAMME: templates.OperationOzanamImmobilierPslaProgramme,
  OPERATION__PIERRE_REVENTE__IMMOBILIER: templates.OperationPierreReventeImmobilier,
  OPERATION__PODELIHA__IMMOBILIER__PROGRAMME: templates.OperationPodelihaImmobilierProgramme,
  OPERATION__PODELIHA__IMMOBILIER__VENTE: templates.OperationPodelihaImmobilierVente,
  OPERATION__PRELLO__IMMOBILIER__LOCATION: templates.OperationPrelloImmobilierLocation,
  OPERATION__PROPRIETES_PRIVEES__BUSINESS: templates.OperationProprietesPriveesBusiness,
  OPERATION__PROPRIETES_PRIVEES__IMMOBILIER: templates.OperationProprietesPriveesImmobilier,
  OPERATION__PROPRIETES_PRIVEES__IMMOBILIER_LOCATION: templates.OperationProprietesPriveesImmobilierLocation,
  OPERATION__PROPRIETES_PRIVEES__IMMORESEAU: templates.OperationProprietesPriveesImmoreseau,
  OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_BUSINESS: templates.OperationProprietesPriveesImmoreseauBusiness,
  OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_LOCATION: templates.OperationProprietesPriveesImmoreseauLocation,
  OPERATION__PROPRIETES_PRIVEES__MIZAPRI: templates.OperationProprietesPriveesMizapri,
  OPERATION__PROPRIETES_PRIVEES__PAUL_PARKER: templates.OperationProprietesPriveesPaulParker,
  OPERATION__PROPRIETES_PRIVEES__RECRUTEMENT_PROPRIETES_PRIVEES:
    templates.OperationProprietesPriveesRecrutementProprietesPrivees,
  OPERATION__PROPRIETES_PRIVEES__REZOXIMO: templates.OperationProprietesPriveesRezoximo,
  OPERATION__PROPRIETES_PRIVEES__VIAGER: templates.OperationProprietesPriveesViager,
  OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME: templates.OperationPvciSensImmobilierProgramme,
  OPERATION__PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE: templates.OperationPvciSensImmobilierProgrammeAcheve,
  OPERATION__PVCI_SENS__IMMOBILIER__VENTE: templates.OperationPvciSensImmobilierVente,
  OPERATION__PVCI_SENS__IMMOBILIER__VENTE_ACHEVE: templates.OperationPvciSensImmobilierVenteAcheve,
  OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE: templates.OperationPvciSensImmobilierVenteSeule,
  OPERATION__RASQUAIN__IMMOBILIER__LOCATION: templates.OperationRasquainImmobilierLocation,
  OPERATION__RASQUAIN__IMMOBILIER__VENTE: templates.OperationRasquainImmobilierVente,
  OPERATION__RECRUTEMENT__AGENT: templates.OperationRecrutementAgent,
  OPERATION__RECRUTEMENT__KW_EXPAND__AGENT: templates.OperationRecrutementKwExpandAgent,
  OPERATION__RECRUTEMENT__KW_POLYNESIE__AGENT: templates.OperationRecrutementKwPolynesieAgent,
  OPERATION__RECRUTEMENT__MYNOTARY__AGENT: templates.OperationRecrutementMynotaryAgent,
  OPERATION__SANTONI__IMMOBILIER: templates.OperationSantoniImmobilier,
  OPERATION__SD_ACCESS__IMMOBILIER_HYBRIDE__PROGRAMME: templates.OperationSdAccessImmobilierHybrideProgramme,
  OPERATION__SD_ACCESS__IMMOBILIER_HYBRIDE__VENTE: templates.OperationSdAccessImmobilierHybrideVente,
  OPERATION__SD_ACCESS__IMMOBILIER__PROGRAMME: templates.OperationSdAccessImmobilierProgramme,
  OPERATION__SD_ACCESS__IMMOBILIER__VENTE: templates.OperationSdAccessImmobilierVente,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON:
    templates.OperationSelectionHabitatDossierDeVenteAgenceHamilton,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO:
    templates.OperationSelectionHabitatDossierDeVenteAgenceHamiltonPro,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO: templates.OperationSelectionHabitatDossierDeVenteAriegImmo,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO:
    templates.OperationSelectionHabitatDossierDeVenteAriegImmoPro,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT:
    templates.OperationSelectionHabitatDossierDeVenteSelectionHabitat,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO:
    templates.OperationSelectionHabitatDossierDeVenteSelectionHabitatPro,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER:
    templates.OperationSelectionHabitatDossierDeVenteSelectionImmobilier,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO:
    templates.OperationSelectionHabitatDossierDeVenteSelectionImmobilierPro,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN:
    templates.OperationSelectionHabitatDossierDeVenteSelectionOcean,
  OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO:
    templates.OperationSelectionHabitatDossierDeVenteSelectionOceanPro,
  OPERATION__SEXTANT__DOSSIER_DE_VENTE_SEXTANT_LMNP: templates.OperationSextantDossierDeVenteSextantLmnp,
  OPERATION__SEXTANT__IMMOBILIER: templates.OperationSextantImmobilier,
  OPERATION__SEXTANT__IMMOBILIER_COMMERCIAL: templates.OperationSextantImmobilierCommercial,
  OPERATION__SYNDIC__GENERAL: templates.OperationSyndicGeneral,
  OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER:
    templates.OperationThierryImmobilierDossierDeVenteThierryImmobilier,
  OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS:
    templates.OperationTroisGImmoTroisGImmoLocationBiensProfessionnels,
  OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION: templates.OperationTroisGImmoTroisGImmoLocationHabitation,
  OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF: templates.OperationTroisGImmoTroisGImmoNeuf,
  OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE: templates.OperationTroisGImmoTroisGImmoPrestige,
  OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN: templates.OperationTroisGImmoTroisGImmoTerrain,
  OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN: templates.OperationTroisGImmoTroisGImmoVenteAncien,
  OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_BIENS_PROFESSIONNELS:
    templates.OperationTroisGImmoTroisGImmoVenteBiensProfessionnels,
  OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VIAGER: templates.OperationTroisGImmoTroisGImmoViager,
  OPERATION__UNIS__COPROPRIETE: templates.OperationUnisCopropriete,
  OPERATION__UNIS__IMMOBILIER__COMMERCIAL: templates.OperationUnisImmobilierCommercial,
  OPERATION__UNIS__IMMOBILIER__HABITATION: templates.OperationUnisImmobilierHabitation,
  OPERATION__UNIS__LOCATION__COMMERCIAL: templates.OperationUnisLocationCommercial,
  OPERATION__UNIS__LOCATION__HABITATION: templates.OperationUnisLocationHabitation,
  OPERATION__UNIS__ORGANISATION_INTERNE: templates.OperationUnisOrganisationInterne,
  OPERATION__UNIS__SOCIAL: templates.OperationUnisSocial,
  OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME: templates.OperationValloireImmobilierAcheveProgramme,
  OPERATION__VALLOIRE__IMMOBILIER_ACHEVE__VENTE: templates.OperationValloireImmobilierAcheveVente,
  OPERATION__VALLOIRE__IMMOBILIER_NEUF__PROGRAMME: templates.OperationValloireImmobilierNeufProgramme,
  OPERATION__VALLOIRE__IMMOBILIER_NEUF__VENTE: templates.OperationValloireImmobilierNeufVente,
  OPERATION__VIAGER_CONSULTING__IMMOBILIER: templates.OperationViagerConsultingImmobilier,
  OPERATION__WHITEBIRD__GENERAL_LOCATION: templates.OperationWhitebirdGeneralLocation,
  OPERATION__WHITEBIRD__GENERAL_PROGRAMME: templates.OperationWhitebirdGeneralProgramme,
  OPERATION__WHITEBIRD__LOCATION_HABITATION: templates.OperationWhitebirdLocationHabitation
};

export function getLegalOperationTemplate(id: string): LegalOperationTemplate;
export function getLegalOperationTemplate(id?: string): LegalOperationTemplate | undefined {
  if (!id) {
    return undefined;
  }

  return legalOperationTemplateMap[id as LegalOperationTemplateId];
}
