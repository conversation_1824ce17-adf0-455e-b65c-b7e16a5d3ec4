// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationI3FImmobilierPslaProgramme: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    tags: {
      OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS: {
        order: 0,
        max: 5,
        group: 0
      },
      OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__BAILLEUR: {
        order: 1,
        max: 5,
        group: 0
      },
      OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__NOTAIRE__PROGRAMME: {
        order: 2,
        max: 5,
        group: 0
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COOPERATIVE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'COPROPRIETE'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 10
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [
      {
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 999
        },
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'VENTES']
      }
    ],
    subOperations: 'Ventes',
    synthesis: {
      tables: [
        {
          id: 'LOTS',
          title: 'Grille de lots',
          titleWithArticle: 'un lot',
          linkSpecificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'LOTS'],
          type: 'PRIMARY',
          columns: [
            {
              label: 'N° de lot',
              property: 'numero_lot.value',
              bold: true,
              responsive: 'TITLE'
            },
            {
              label: 'N° de commercialisation',
              property: 'numero_commercialisation_lot.value',
              responsive: 'CONTENT'
            },
            {
              label: 'Superficie',
              property: 'programme_superficie.value'
            },
            {
              label: 'Batiment',
              property: 'programme_batiment.value'
            },
            {
              label: 'Etage',
              property: 'programme_niveau.value'
            },
            {
              label: 'Type',
              property: 'nature_bien.value',
              format: 'SELECT',
              responsive: 'CONTENT'
            },
            {
              label: 'Type autre',
              property: 'nature_bien_autre.value',
              responsive: 'TITLE'
            },
            {
              label: 'Prix',
              property: 'programme_prix_vente_ttc.value',
              format: 'PRICE',
              responsive: 'CONTENT'
            },
            {
              label: 'Reservation',
              source: 'RESERVATION',
              property: 'status',
              responsive: 'CONTENT'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HABITATION']
            },
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HORS_HABITATION']
            },
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HABITATION']
            },
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HORS_HABITATION']
            }
          ]
        },
        {
          id: 'COPROS',
          title: 'Copropriétés',
          titleWithArticle: 'une copropriété',
          linkSpecificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'COPROPRIETE'],
          type: 'SECONDARY',
          columns: [
            {
              label: 'Dénomination',
              bold: true,
              property: 'denomination.value',
              responsive: 'TITLE'
            },
            {
              label: 'Adresse',
              property: 'adresse.value.formattedAddress',
              responsive: 'TITLE'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*.links.COMPOSITION__COPROPRIETE.\\d*.link.branches.CONTENU.\\d*.to.\\d*',
              specificType: ['STRUCTURE', 'COPROPRIETE']
            }
          ]
        }
      ]
    },
    contractModels: [
      {
        id: 'I3F_IMMOBILIER_PSLA_PRELIMINAIRE_CONTRAT_PRELIMINAIRE',
        label: 'Contrat Préliminaire'
      },
      {
        id: 'I3F_IMMOBILIER_PSLA_PRELIMINAIRE_CONTRAT_PRELIMINAIRE_NOUVEAU',
        label: 'Contrat Préliminaire PSLA - Nouveau'
      },
      {
        id: 'I3F_IMMOBILIER_PSLA_PRELIMINAIRE_PSLA_CHILLY',
        label: 'Contrat Préliminaire PSLA - Modèle Chilly'
      },
      {
        id: 'I_3_F__PSLA__PSLA_NOINTEL',
        label: 'Contrat Préliminaire PSLA - Modèle Nointel'
      },
      {
        id: 'I_3_F__PSLA__PSLA_AVENANT',
        label: 'Avenant - Contrat Préliminaire PSLA'
      },
      {
        id: 'I3F_IMMOBILIER_PSLA_PRELIMINAIRE_BON_SOUSCRIPTION',
        label: 'Bulletin de souscription - CLARM'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isParentOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      '3f_acquereur_social_information',
      '3f_acquereur_social_proposition_achat',
      '3f_bulletin_paie',
      '3f_justificatif',
      'fiscal_n_2',
      'notice_descriptive',
      'plan_etages',
      'plan_lot',
      'plan_masse',
      'plan_sous_sol',
      'plans_lots_copropriete',
      'programme_plan'
    ]
  },
  id: 'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME',
  label: '3F - PSLA Programme',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__PSLA_PROGRAMME',
  specificTypes: ['I3F', 'IMMOBILIER', 'PSLA_PROGRAMME'],
  type: 'OPERATION'
};
