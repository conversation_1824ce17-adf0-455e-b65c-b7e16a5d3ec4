// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationI3FImmobilierPslaPreliminaire: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    labelPattern: '{{ numero_lot }} / {{ programme_nom }} /  {{ nom_acquereur }}',
    emailLabelPattern: '{{ biens_vendus }}',
    tags: {
      numero_lot: {
        link: 'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS',
        questionId: ['numero_lot', 'numero_commercialisation_lot'],
        max: 1,
        label: 'Numéro du lot'
      },
      programme_nom: {
        link: 'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__FICHE_PROGRAMME',
        questionId: 'programme_nom',
        max: 1,
        label: 'Nom du programme'
      },
      nom_acquereur: {
        link: 'OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__ACQUEREUR',
        questionId: ['nom', 'personne_morale_denomination'],
        max: 1,
        label: 'Nom acquéreur'
      },
      biens_vendus: {
        link: 'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS',
        order: 0,
        max: 1
      },
      bailleurs: {
        link: 'OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__BAILLEUR',
        order: 1,
        max: 1
      },
      commercialisateurs: {
        link: 'OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__REPRESENTANT_COMMERCIALISATEUR',
        order: 3,
        max: 5,
        group: 0
      },
      acquereurs: {
        link: 'OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE__ACQUEREUR',
        order: 2,
        max: 1
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PRELIMINAIRE', 'ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PRELIMINAIRE', 'CONTRAT_PRELIMINAIRE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PRELIMINAIRE', 'NOTAIRE_ACQUEREUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COOPERATIVE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PRELIMINAIRE', 'COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PRELIMINAIRE', 'REPRESENTANT_COMMERCIALISATEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    linkOverrides: [
      'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__FICHE_PROGRAMME',
      'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__BAILLEUR',
      'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__REPRESENTANT_BAILLEUR',
      'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__NOTAIRE_PROGRAMME',
      'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__COOPERATIVE'
    ],
    operationLinks: [
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'PSLA_PROGRAMME', 'VENTES']
      }
    ],
    contracts: [
      {
        id: 'I3F_IMMOBILIER_PSLA_PRELIMINAIRE_CONTRAT_PRELIMINAIRE',
        label: 'Contrat Préliminaire'
      },
      {
        id: 'I3F_IMMOBILIER_PSLA_PRELIMINAIRE_CONTRAT_PRELIMINAIRE_NOUVEAU',
        label: 'Contrat Préliminaire PSLA - Nouveau'
      },
      {
        id: 'I3F_IMMOBILIER_PSLA_PRELIMINAIRE_PSLA_CHILLY',
        label: 'Contrat Préliminaire PSLA - Modèle Chilly'
      },
      {
        id: 'I_3_F__PSLA__PSLA_NOINTEL',
        label: 'Contrat Préliminaire PSLA - Modèle Nointel'
      },
      {
        id: 'I_3_F__PSLA__PSLA_AVENANT',
        label: 'Avenant - Contrat Préliminaire PSLA'
      },
      {
        id: 'I3F_IMMOBILIER_PSLA_PRELIMINAIRE_BON_SOUSCRIPTION',
        label: 'Bulletin de souscription - CLARM'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__I3F__IMMOBILIER__PSLA_PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isSubOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      '3f_acquereur_social_information',
      '3f_acquereur_social_proposition_achat',
      '3f_bulletin_paie',
      '3f_justificatif',
      'fiscal_n_2',
      'notice_descriptive',
      'plan_etages',
      'plan_lot',
      'plan_masse',
      'plan_sous_sol',
      'plans_lots_copropriete',
      'programme_plan'
    ]
  },
  id: 'OPERATION__I3F__IMMOBILIER__PSLA_PRELIMINAIRE',
  label: '3F - PSLA Contrat Préliminaire',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__PSLA_PRELIMINAIRE',
  specificTypes: ['I3F', 'IMMOBILIER', 'PSLA_PRELIMINAIRE'],
  type: 'OPERATION'
};
