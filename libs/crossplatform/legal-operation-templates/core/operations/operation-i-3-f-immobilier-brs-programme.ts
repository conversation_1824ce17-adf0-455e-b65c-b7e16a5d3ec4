// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationI3FImmobilierBrsProgramme: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    hiddenPages: {
      documents: true
    },
    tags: {
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS: {
        order: 0,
        max: 1,
        group: 0
      },
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__ACQUEREUR: {
        order: 1,
        max: 1,
        group: 0
      },
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__BAILLEUR: {
        order: 2,
        max: 1,
        group: 0
      },
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__NOTAIRE_PROGRAMME: {
        order: 3,
        max: 1,
        group: 0
      },
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__REPRESENTANT_COMMERCIALISATEUR: {
        order: 4,
        max: 1,
        group: 0
      }
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'REPRESENTANT_BAILLEUR'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COOPERATIVE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 1,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COPROPRIETE'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 10
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [
      {
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 999
        },
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'VENTES']
      }
    ],
    subOperations: 'Ventes',
    synthesis: {
      tables: [
        {
          id: 'LOTS',
          title: 'Grille de lots',
          titleWithArticle: 'un lot',
          linkSpecificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'LOTS'],
          type: 'PRIMARY',
          columns: [
            {
              label: 'N° de lot',
              property: 'numero_lot.value',
              bold: true,
              responsive: 'TITLE'
            },
            {
              label: 'N° de commercialisation',
              property: 'numero_commercialisation_lot.value',
              responsive: 'CONTENT'
            },
            {
              label: 'Superficie',
              property: 'programme_superficie.value'
            },
            {
              label: 'Batiment',
              property: 'programme_batiment.value'
            },
            {
              label: 'Etage',
              property: 'programme_niveau.value'
            },
            {
              label: 'Type',
              property: 'nature_bien.value',
              format: 'SELECT',
              responsive: 'CONTENT'
            },
            {
              label: 'Type Autre',
              property: 'nature_bien_autre.value',
              responsive: 'TITLE'
            },
            {
              label: 'Prix',
              property: 'programme_prix_vente_ttc.value',
              format: 'PRICE',
              responsive: 'CONTENT'
            },
            {
              label: 'Reservation',
              source: 'RESERVATION',
              property: 'status',
              responsive: 'CONTENT'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HABITATION']
            },
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HORS_HABITATION']
            },
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HABITATION']
            },
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HORS_HABITATION']
            }
          ]
        },
        {
          id: 'COPROS',
          title: 'Copropriétés',
          titleWithArticle: 'une copropriété',
          linkSpecificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COPROPRIETE'],
          type: 'SECONDARY',
          columns: [
            {
              label: 'Dénomination',
              bold: true,
              property: 'denomination.value',
              responsive: 'TITLE'
            },
            {
              label: 'Adresse',
              property: 'adresse.value.formattedAddress',
              responsive: 'TITLE'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*.links.COMPOSITION__COPROPRIETE.\\d*.link.branches.CONTENU.\\d*.to.\\d*',
              specificType: ['STRUCTURE', 'COPROPRIETE']
            }
          ]
        }
      ]
    },
    contractModels: [
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_CONTRAT_RESERVATION_BRS',
        label: 'Contrat Préliminaire - Bail Réel Solidaire'
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_CONTRAT_PRELIMINAIRE_BRS_V2',
        label: 'Contrat Préliminaire - BRS sur VEFA'
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_BRS_PLESSIS',
        label: 'Contrat Préliminaire - BRS Modèle Plessis Trévise'
      },
      {
        id: 'I_3_F__BRS__BRS_AVENANT',
        label: 'Avenant - Contrat Préliminaire BRS'
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_BON_SOUSCRIPTION',
        label: 'Bulletin de souscription (un par foyer)'
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_CONVENTION_HONORAIRES',
        label: "Convention d'honoraires / Lettre de mission"
      },
      {
        id: 'I3F_IMMOBILIER_BRS_PRELIMINAIRE_LETTRE_DE_MISSION_PLESSIS',
        label: 'Lettre de mission - Modèle Plessis Trévise'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isParentOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      '3f_acquereur_social_information',
      '3f_acquereur_social_proposition_achat',
      '3f_bulletin_paie',
      '3f_justificatif',
      'fiscal_n_2',
      'notice_descriptive',
      'programme_plan',
      'plan_exposition_bruit_libre'
    ]
  },
  id: 'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME',
  label: '3F - Programme Bail Réel Solidaire',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME',
  specificTypes: ['I3F', 'IMMOBILIER', 'BRS_PROGRAMME'],
  type: 'OPERATION'
};
