// @ts-nocheck
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';
export const OperationSextantDossierDeVenteSextantLmnp: LegalOperationTemplate = {
  config: {
    contracts: [
      {
        id: 'SEXTANT__TRANSACTION__MANDAT_DE_VENTE_LMNP',
        label: 'Mandat de vente Exclusif - LMNP'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT',
        label: 'Mandat de vente - Avenant'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_OFFRE_ACHAT_AVEC_VENDEUR',
        label: "Offre d'achat avec Vendeur"
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_ACCEPTATION_OFFRE',
        label: "Acceptation de l'offre par le Vendeur"
      },
      {
        id: 'IMMOBILIER__VENTE_ANCIEN__CONTRE_OFFRE',
        label: 'Contre offre par le Vendeur'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_PROCURATION_VENTE',
        label: 'Procuration pour vendre'
      },
      {
        id: 'IMMOBILIER_VENTE_ANCIEN_PROCURATION_ACQUERIR',
        label: 'Procuration pour acquérir'
      },
      {
        id: 'SEXTANT_IMMOBILIER_FICHES_TRACFIN_VENDEUR',
        label: 'Fiche LCB/FT (Tracfin) - Vendeur'
      },
      {
        id: 'SEXTANT_IMMOBILIER_FICHES_TRACFIN_ACQUEREUR',
        label: 'Fiche LCB/FT (Tracfin) - Acquéreur '
      }
    ],
    documentsToExclude: [],
    documentsToInclude: [
      'appel_charge',
      'assainissement_non_collectif_controle_libre',
      'assurance_decennale_construction',
      'assurance_do_initial',
      'assurance_dommage_construction',
      'attestation_diagnostiqueur',
      'carnet_information_construction_document',
      'certificat_conformite_construction',
      'cheminee_facture_ramonage',
      'conformite_piscine',
      'conge_locataire',
      'conge_vente',
      'construction_particulier_assurance_dommage',
      'contrat_attaches_list_contrat_attaches_document',
      'contrat_bail',
      'controle_assainissement_collectif',
      'controle_assainissement_collectif_libre',
      'controle_assainissement_fosse',
      'controle_assainissement_non_collectif_libre',
      'copro_alur_attestation',
      'copro_infos_financieres_alur',
      'courrier_renonciation_preemption_locataire',
      'declaration_achevement_construction',
      'declaration_prealable_construction',
      'diagnostic_amiante',
      'diagnostic_electrique',
      'diagnostic_gaz',
      'diagnostic_merule',
      'diagnostic_parties_communes_amiante',
      'diagnostic_parties_communes_diagnostic_dtg',
      'diagnostic_parties_communes_plomb',
      'diagnostic_plomb',
      'diagnostic_termites',
      'dip_exclusif',
      'dip_libre_anglais',
      'dip_recherche',
      'dip_semi',
      'dip_simple',
      'document_pre_etat_date',
      'dpe',
      'dpe_audit_document',
      'engagement_conjoint',
      'etude_geotechnique',
      'facture_ramonage',
      'factures_entreprises_construction',
      'gestion_lotissement_gestion_lotissement_autre_statuts',
      'gestion_lotissement_gestion_lotissement_statuts_aful',
      'gestion_lotissement_gestion_lotissement_statuts_asl',
      'gestion_volume_gestion_volume_autre_statuts',
      'gestion_volume_gestion_volume_statuts_aful',
      'gestion_volume_gestion_volume_statuts_asl',
      'location_bail_liste_contrat_bail',
      'location_bail_liste_etat_des_lieux_document',
      'location_bail_liste_quittance_loyer',
      'location_bail_liste_quittances_loyer',
      'mandat_bail',
      'mandat_retractation_document',
      'ordonnance_autorisation_document',
      'origine_propriete_op_acquisition',
      'origine_propriete_op_adjudication',
      'origine_propriete_op_donation',
      'origine_propriete_op_echange',
      'origine_propriete_op_partage',
      'origine_propriete_op_remembrement',
      'origine_propriete_op_succession',
      'origine_propriete_op_succession_pas_reglee',
      'permis_construire_construction',
      'piscine_note_securite_document',
      'piscine_securite_attestation_document',
      'pv_ag',
      'pv_ag_liste_pv_ag_doc',
      'servitude_acte',
      'taxe_fonciere',
      'travaux_list_assurance_decennale_travaux',
      'travaux_list_autorisation_copropriete_travaux_global',
      'travaux_list_carnet_information_travaux_document',
      'travaux_list_certificat_conformite',
      'travaux_list_ctravaux_particulier_assurance_dommage',
      'travaux_list_declaration_achevement',
      'travaux_list_declaration_prealable',
      'travaux_list_dossier_urbanisme',
      'travaux_list_facture_entreprise_travaux',
      'travaux_list_permis_construire',
      'travaux_list_travaux_autorisation_pv_ag',
      'travaux_list_travaux_facture_doc',
      'retractation_sextant',
      'dip_sextant'
    ],
    emailLabelPattern: '{{ adresse }}',
    hiddenPages: {
      documents: true
    },
    labelPattern: '{{ numero_mandat }} / {{ adresse }} / {{ nom_vendeur }} / {{ nom_acquereur }}',
    operationLinks: [],
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    recordExtensions: [
      {
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
          indirectLinks: ['SITUATION_MARITALE__MARIAGE', 'SITUATION_MARITALE__DIVORCE']
        },
        specificTypes: ['EXTENSION', 'ANCIEN', 'ACQUEREUR']
      },
      {
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        },
        specificTypes: ['EXTENSION', 'COPROPRIETE']
      },
      {
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__NOTAIRES',
          directRecords: ['PERSONNE__MORALE__NOTAIRE']
        },
        specificTypes: ['EXTENSION', 'ANCIEN', 'NOTAIRE']
      },
      {
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
          directRecords: [
            'BIEN__INDIVIDUEL_HABITATION',
            'BIEN__INDIVIDUEL_HORS_HABITATION',
            'BIEN__TERRAIN_CONSTRUCTIBLE',
            'BIEN__TERRAIN_NON_CONSTRUCTIBLE',
            'BIEN__MONOPROPRIETE_HABITATION',
            'BIEN__MONOPROPRIETE_HORS_HABITATION',
            'BIEN__LOT_HABITATION',
            'BIEN__LOT_HORS_HABITATION'
          ]
        },
        specificTypes: ['EXTENSION', 'ANCIEN', 'BIEN']
      },
      {
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
          indirectLinks: ['COMPOSITION__LOTISSEMENT__LOTISSEMENT'],
          indirectRecords: ['STRUCTURE__LOTISSEMENT']
        },
        specificTypes: ['EXTENSION', 'ANCIEN', 'LOTISSEMENT']
      },
      {
        match: {
          directLink: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
          directRecords: ['PERSONNE__PHYSIQUE'],
          indirectLinks: [
            'CAPACITE__TUTELLE',
            'CAPACITE__MINORITE',
            'CAPACITE__MANDAT_PROTECTION_FUTURE',
            'CAPACITE__HABILITATION_FAMILIALE'
          ]
        },
        specificTypes: ['EXTENSION', 'ANCIEN', 'VENDEUR']
      }
    ],
    recordLinks: [
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VENDEURS']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITEURS']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'VISITE']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREURS']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'REPRESENTANTS']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRANTS']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'OFFRE']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'ACQUEREUR_CESSIONNAIRE']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'APPORTEUR']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        hasDefaultRecord: true,
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENTS']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        hasDefaultRecord: true,
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'MANDATAIRES']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'BIENS_VENDUS']
      },
      {
        constraints: {
          max: 1,
          min: 1
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE_ANCIEN', 'FICHES']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'AGENCE_DELEGATAIRE']
      },
      {
        constraints: {
          max: 1,
          min: 0
        },
        creation: {
          autoCreate: true
        },
        specificTypes: ['OPERATION', 'IMMOBILIER', 'VENTE', 'NOTAIRES']
      }
    ],
    tags: {
      acquereurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        max: 1,
        order: 2
      },
      adresse: {
        format: 'ADDRESS',
        label: 'Adresse',
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
        max: 1,
        questionId: 'adresse'
      },
      biens_vendus: {
        link: 'OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS',
        max: 1,
        order: 0
      },
      nom_acquereur: {
        label: 'Nom acquéreur',
        link: 'OPERATION__IMMOBILIER__VENTE__ACQUEREURS',
        max: 1,
        questionId: ['nom', 'personne_morale_denomination']
      },
      nom_vendeur: {
        label: 'Nom vendeur',
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        max: 1,
        questionId: ['nom', 'personne_morale_denomination']
      },
      numero_mandat: {
        format: 'MANDAT',
        label: 'N° mandat',
        link: 'OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES',
        max: 1,
        questionId: 'mandat_numero'
      },
      vendeurs: {
        link: 'OPERATION__IMMOBILIER__VENTE__VENDEURS',
        max: 1,
        order: 1
      }
    }
  },
  id: 'OPERATION__SEXTANT__DOSSIER_DE_VENTE_SEXTANT_LMNP',
  label: 'Dossier de vente Sextant - LMNP',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTE_ANCIEN',
  specificTypes: ['SEXTANT', 'DOSSIER_DE_VENTE_SEXTANT_LMNP'],
  type: 'OPERATION'
};
