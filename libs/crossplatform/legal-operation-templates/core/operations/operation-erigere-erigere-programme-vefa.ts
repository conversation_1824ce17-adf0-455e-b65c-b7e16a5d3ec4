// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const OperationErigereErigereProgrammeVefa: LegalOperationTemplate = {
  config: {
    preEtatDate: {
      destinationLegalRecordTemplateId: 'RECORD__EXTENSION__COPROPRIETE'
    },
    tags: {
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS: {
        order: 0,
        max: 5,
        group: 0
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__REPRESENTANT_PROMOTEURS: {
        order: 1,
        max: 5,
        group: 0
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__NOTAIRE_PROGRAMME: {
        order: 2,
        max: 5,
        group: 0
      }
    },
    hiddenPages: {
      documents: true
    },
    recordLinks: [
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'PROMOTEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        },
        hasDefaultRecord: true
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'REPRESENTANT_PROMOTEURS'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'BRS_PROGRAMME', 'COOPERATIVE'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'FICHE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'NOTAIRE_PROGRAMME'],
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'COPROPRIETE'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 10
        }
      },
      {
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'LOTS'],
        restrictNewLinkToParentOperation: true,
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    operationLinks: [
      {
        creation: {
          autoCreate: true
        },
        constraints: {
          min: 0,
          max: 999
        },
        specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'VENTES_ERIGERE']
      }
    ],
    subOperations: 'Ventes',
    synthesis: {
      tables: [
        {
          id: 'LOTS',
          title: 'Grille de lots',
          titleWithArticle: 'un lot',
          linkSpecificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'LOTS'],
          type: 'PRIMARY',
          columns: [
            {
              label: 'N° de lot',
              property: 'numero_lot.value',
              bold: true,
              responsive: 'TITLE'
            },
            {
              label: 'N° de commercialisation',
              property: 'numero_commercialisation_lot.value',
              responsive: 'CONTENT'
            },
            {
              label: 'Superficie',
              property: 'programme_superficie.value'
            },
            {
              label: 'Type',
              property: 'programme_typologie.value',
              format: 'SELECT',
              responsive: 'TITLE'
            },
            {
              label: 'Batiment',
              property: 'programme_batiment.value'
            },
            {
              label: 'Etage',
              property: 'programme_niveau.value'
            },
            {
              label: 'Prix',
              property: 'programme_prix_vente_ttc.value',
              format: 'PRICE',
              responsive: 'CONTENT'
            },
            {
              label: 'Reservation',
              source: 'RESERVATION',
              property: 'status',
              responsive: 'CONTENT'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HABITATION']
            },
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'INDIVIDUEL_HORS_HABITATION']
            },
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HABITATION']
            },
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*',
              specificType: ['BIEN', 'LOT_HORS_HABITATION']
            }
          ]
        },
        {
          id: 'COPROS',
          title: 'Copropriétés',
          titleWithArticle: 'une copropriété',
          linkSpecificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_PROGRAMME', 'COPROPRIETE'],
          type: 'SECONDARY',
          columns: [
            {
              label: 'Dénomination',
              bold: true,
              property: 'denomination.value',
              responsive: 'TITLE'
            },
            {
              label: 'Adresse',
              property: 'adresse.value.formattedAddress',
              responsive: 'TITLE'
            }
          ],
          records: [
            {
              path: 'links.OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS.\\d*.link.branches.BIEN_VENDU.\\d*.to.\\d*.links.COMPOSITION__COPROPRIETE.\\d*.link.branches.CONTENU.\\d*.to.\\d*',
              specificType: ['STRUCTURE', 'COPROPRIETE']
            }
          ]
        }
      ]
    },
    contractModels: [
      {
        id: 'ERIGERE__VEFA__CONTRAT_DE_RESERVATION',
        label: 'Contrat de Réservation'
      },
      {
        id: 'I_3_F__VEFA__VEFA_AVENANT',
        label: 'Avenant - Contrat de Réservation VEFA'
      }
    ],
    recordExtensions: [
      {
        specificTypes: ['EXTENSION', 'COPROPRIETE'],
        match: {
          directLink: 'OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS',
          indirectLinks: ['COMPOSITION__COPROPRIETE'],
          indirectRecords: ['STRUCTURE__COPROPRIETE']
        }
      }
    ],
    isParentOperation: true,
    documentsToExclude: [],
    documentsToInclude: [
      '3f_acquereur_social_information',
      '3f_acquereur_social_proposition_achat',
      '3f_bulletin_paie',
      '3f_justificatif',
      'fiscal_n_2',
      'notice_descriptive',
      'plan_etages',
      'plan_lot',
      'plan_masse',
      'plan_sous_sol',
      'plans_lots_copropriete',
      'programme_plan'
    ]
  },
  id: 'OPERATION__ERIGERE__ERIGERE_PROGRAMME_VEFA',
  label: 'Erigère - Programme VEFA',
  mynotaryTemplate: false,
  originTemplate: 'OPERATION__IMMOBILIER__VENTES_PROGRAMME',
  specificTypes: ['ERIGERE', 'ERIGERE_PROGRAMME_VEFA'],
  type: 'OPERATION'
};
