import { getRequiredLegalLinksInLegalOperation } from './get-required-legal-links-in-legal-operation';
import { legalOperationTemplateMap } from './legal-operation-template-map';
import { LegalOperationTemplateId } from '@mynotary/crossplatform/legal-templates/api';

describe(getRequiredLegalLinksInLegalOperation.name, () => {
  it('should retrieve the required legal links list', async () => {
    const requiredLegalLinks = getRequiredLegalLinksInLegalOperation('OPERATION__IMMOBILIER__VENTE_ANCIEN');

    expect(requiredLegalLinks).toEqual(
      expect.arrayContaining([
        { legalBranches: [{ type: 'VENDEUR' }], legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__VENDEURS' },
        { legalBranches: [{ type: 'VISITEUR' }], legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__VISITEURS' },
        {
          legalBranches: [
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__VISITE', type: 'FICHE_VISITE' }
          ],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__VISITE'
        },
        {
          legalBranches: [{ type: 'ACQUEREUR' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__ACQUEREURS'
        },
        {
          legalBranches: [{ type: 'REPRESENTANT' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__REPRESENTANTS'
        },
        { legalBranches: [{ type: 'OFFRANT' }], legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS' },
        {
          legalBranches: [
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT', type: 'FICHE_OFFRE' }
          ],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRE'
        },
        {
          legalBranches: [{ type: 'ACQUEREUR_CESSIONNAIRE' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__ACQUEREUR_CESSIONNAIRE'
        },
        {
          legalBranches: [{ type: 'APPORTEUR' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__APPORTEUR'
        },
        {
          legalBranches: [{ type: 'BIEN_VENDU' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS'
        },
        {
          legalBranches: [
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__OFFRE_ACHAT', type: 'OFFRE_ACHAT' },
            {
              legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__GENERAL',
              type: 'CONDITIONS_GENERALES'
            },
            {
              legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__SUBSTITUTION',
              type: 'SUBSTITUTION'
            },
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__DELEGATION', type: 'DELEGATION' },
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__MANDAT', type: 'MANDAT' },
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__FINANCEMENT', type: 'FINANCEMENT_ACQUEREUR' },
            {
              legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__PROCURATION_ACHAT',
              type: 'PROCURATION_ACHAT'
            },
            {
              legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__PROCURATION_VENTE',
              type: 'PROCURATION_VENTE'
            },
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__REMISE_ALUR', type: 'REMISE_ALUR' },
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__RESILIATION', type: 'RESILIATION' },
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__TRACFIN', type: 'TRACFIN' },
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_ANCIEN__VISITE', type: 'VISITE' },
            {
              legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__MANDAT_COMMERCIAL',
              type: 'MANDAT_COMMERCIAL'
            },
            {
              legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__LOCATION_COMMERCIAL__BAIL_COMMERCIAL',
              type: 'BAIL_COMMERCIAL'
            }
          ],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES'
        },
        {
          legalBranches: [{ type: 'AGENT_IMMOBILIER' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__AGENTS'
        },
        {
          legalBranches: [{ type: 'MANDATAIRE' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__MANDATAIRES'
        },
        {
          legalBranches: [{ type: 'AGENCE_DELEGATAIRE' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__AGENCE_DELEGATAIRE'
        },
        {
          legalBranches: [{ type: 'NOTAIRE_IMMOBILIER' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__NOTAIRES'
        }
      ])
    );
  });

  it('should retrieve the required legal links for a sub operation', async () => {
    const requiredLegalLinks = getRequiredLegalLinksInLegalOperation('OPERATION__DUVAL__IMMOBILIER__VEFA_RESERVATION');

    expect(requiredLegalLinks.length).toEqual(6);
    expect(requiredLegalLinks).toEqual(
      expect.arrayContaining([
        {
          legalBranches: [{ type: 'BIEN_VENDU' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTES_PROGRAMME__LOTS_DUVAL'
        },
        {
          legalBranches: [{ type: 'RESERVATAIRE' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES'
        },
        {
          legalBranches: [
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__VENTE_NEUF__GENERAL', type: 'CONDITIONS_GENERALES' },
            { legalRecordTemplateId: 'RECORD__OPERATION__IMMOBILIER__FINANCEMENT', type: 'FINANCEMENT_ACQUEREUR' }
          ],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE_NEUF__FICHES'
        },
        {
          legalBranches: [{ type: 'NOTAIRE_RESERVATAIRE' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE_NEUF__NOTAIRE_RESERVATAIRE'
        },
        {
          legalBranches: [{ type: 'COMMERCIALISATEUR' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE_NEUF__COMMERCIALISATEUR'
        },
        {
          legalBranches: [{ type: 'REPRESENTANT_COMMERCIALISATEUR' }],
          legalLinkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE_NEUF__REPRESENTANT_COMMERCIALISATEUR'
        }
      ])
    );
  });

  it.each(Object.keys(legalOperationTemplateMap))(
    'should check if legal branch has only 1 legal record template available for %s',
    async (legalOperationTemplateId) => {
      const requiredLegalLinks = getRequiredLegalLinksInLegalOperation(
        legalOperationTemplateId as LegalOperationTemplateId
      );
      expect(requiredLegalLinks.length).toBeDefined();
    }
  );
});
