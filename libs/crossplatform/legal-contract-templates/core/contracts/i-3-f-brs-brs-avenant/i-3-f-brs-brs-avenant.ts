// @ts-nocheck
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';
export const I3FBrsBrsAvenant: LegalContractTemplate = {
  config: {
    defaultSubscribers: ['OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__NOTAIRE_PROGRAMME'],
    folder: {
      mandatoryDocuments: [
        {
          documentIds: ['emprunt_simulation'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__CONTRAT_PRELIMINAIRE',
            'BRANCHES',
            'FINANCEMENT',
            'RECORDS'
          ]
        },
        {
          documentIds: [
            'carte_identite',
            'titre_sejour',
            'personne_morale_statuts',
            'personne_morale_KBIS',
            '3f_bulletin_paie',
            '3f_justificatif',
            'fiscal_n_2'
          ],
          recordPath: ['OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR', 'BRANCHES', 'ACQUEREUR', 'RECORDS']
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['jugement_divorce', 'convention_de_divorce'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ]
        },
        {
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ]
        },
        {
          documentIds: ['contrat_pacs'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR',
            'BRANCHES',
            'ACQUEREUR',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ]
        }
      ]
    },
    operationRecords: {
      OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR: {
        branches: {
          ACQUEREUR: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },

      OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__CONTRAT_PRELIMINAIRE: {
        branches: {
          CONTRAT_PRELIMINAIRE: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__BAILLEUR: {
        branches: {
          BAILLEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__COPROPRIETE: {
        branches: {
          COPROPRIETE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__FICHE_PROGRAMME: {
        branches: {
          PROGRAMME: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__LOTS: {
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE']
          }
        },
        constraints: {
          subOperationOnly: true
        }
      },
      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__NOTAIRE_PROGRAMME: {
        branches: {
          NOTAIRE_PROGRAMME: {
            constraints: {
              min: 1
            }
          }
        }
      },

      OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__REPRESENTANT_BAILLEUR: {
        branches: {
          REPRESENTANT_BAILLEUR: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    registeredLetter: {
      defaultReceivers: {
        OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR: {
          branches: {
            ACQUEREUR: true
          }
        }
      },
      letterSubstitutions: {
        senderAddress: {
          items: [
            {
              path: ['sender', 'address', 'formattedAddress'],
              type: 'VARIABLE'
            }
          ]
        },
        senderName: {
          aggregate: {
            type: 'CONCAT'
          },
          items: [
            {
              path: ['sender', 'firstname'],
              type: 'VARIABLE'
            },
            {
              constant: ' ',
              type: 'CONSTANT'
            },
            {
              path: ['sender', 'lastname'],
              type: 'VARIABLE'
            }
          ]
        },
        signatureDate: {
          items: [
            {
              path: ['configuration', 'signatureTime'],
              transformers: [
                {
                  type: 'DATE'
                }
              ],
              type: 'VARIABLE'
            }
          ]
        }
      },
      letterTemplateId: 'REGISTERED_LETTER_PSLA'
    },
    signature: {
      blockingConditions: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__I3F__IMMOBILIER__BRS_PRELIMINAIRE__ACQUEREUR: {
          branches: {
            ACQUEREUR: true
          }
        },
        OPERATION__I3F__IMMOBILIER__BRS_PROGRAMME__REPRESENTANT_BAILLEUR: {
          branches: {
            REPRESENTANT_BAILLEUR: true
          }
        }
      },
      mandatoryDocuments: []
    }
  },
  id: 'I_3_F__BRS__BRS_AVENANT',
  jeffersonPath: 'mynotary/i3F/brs/brsAvenant.json',
  label: 'BRS - Avenant',
  mainContract: false,
  originTemplate: null
};
