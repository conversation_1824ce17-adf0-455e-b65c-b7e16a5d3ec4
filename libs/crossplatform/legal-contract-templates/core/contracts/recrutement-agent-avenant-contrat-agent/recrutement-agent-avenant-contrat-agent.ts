// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecrutementAgentAvenantContratAgent: LegalContractTemplate = {
  config: {
    operationRecords: {
      OPERATION__RECRUTEMENT__AGENT__GENERAL: {
        branches: {
          INFO_GENERALE: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__RECRUTEMENT__AGENT__AGENCE: {
        branches: {
          AGENCE: {
            constraints: {
              min: 1
            },
            recordLinks: ['REPRESENTATION__PERSONNE_MORALE']
          }
        }
      },
      OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL: {
        branches: {
          AGENT_COMMERCIAL: {
            constraints: {
              min: 1
            }
          }
        }
      }
    },
    folder: {
      mandatoryDocuments: []
    },
    signature: {
      blockingConditions: [],
      mandatoryDocuments: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__RECRUTEMENT__AGENT__AGENCE: {
          branches: {
            AGENCE: true
          }
        },
        OPERATION__RECRUTEMENT__AGENT__AGENT_COMMERCIAL: {
          branches: {
            AGENT_COMMERCIAL: true
          }
        }
      }
    },
    registeredLetter: {}
  },
  id: 'RECRUTEMENT_AGENT_AVENANT_CONTRAT_AGENT',
  jeffersonPath: 'mynotary/recrutement/agent/avenantContratAgent.json',
  label: 'Avenant Contrat Agent - Délégation de paiement',
  mainContract: false,
  originTemplate: null
};
