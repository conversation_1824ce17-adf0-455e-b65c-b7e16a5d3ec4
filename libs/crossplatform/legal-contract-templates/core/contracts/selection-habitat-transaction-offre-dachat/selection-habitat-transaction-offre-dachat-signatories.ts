import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const offrantsSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE__OFFRANTS'
  });

  return [offrantsSignatories].flat();
}

export const SelectionHabitatTransactionOffreDachatSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
