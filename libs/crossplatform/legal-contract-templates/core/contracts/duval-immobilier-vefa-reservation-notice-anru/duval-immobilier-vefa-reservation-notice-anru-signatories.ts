import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const reservatairesSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__VENTE_NEUF__RESERVATAIRES'
  });

  return [reservatairesSignatories].flat();
}

export const DuvalImmobilierVefaReservationNoticeAnruSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
