// @ts-nocheck
import { LegalContractTemplate } from '@mynotary/crossplatform/legal-templates/api';
export const I3FVefaVefaAvenant: LegalContractTemplate = {
  config: {
    defaultSubscribers: ['OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__NOTAIRE_PROGRAMME'],
    folder: {
      mandatoryDocuments: [
        {
          documentIds: ['emprunt_simulation'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__CONTRAT_RESERVATION',
            'BRANCHES',
            'FINANCEMENT',
            'RECORDS'
          ]
        },
        {
          documentIds: [
            'carte_identite',
            'titre_sejour',
            'personne_morale_statuts',
            'personne_morale_KBIS',
            '3f_bulletin_paie',
            '3f_justificatif',
            'fiscal_n_2'
          ],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'RECORDS'
          ]
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_TUTELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_CURATELLE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_HABILITATION_FAMILIALE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_MANDAT_PROTECTION_FUTURE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['carte_identite'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'CAPACITE_MINORITE',
            'BRANCHES',
            'TUTEUR',
            'RECORDS'
          ]
        },
        {
          documentIds: ['jugement_divorce', 'convention_de_divorce'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'SITUATION_MARITALE_DIVORCE',
            'RECORD'
          ]
        },
        {
          documentIds: ['livret_famille', 'contrat_mariage_upload', 'acte_changement_regime'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'SITUATION_MARITALE_MARIAGE',
            'RECORD'
          ]
        },
        {
          documentIds: ['contrat_pacs'],
          recordPath: [
            'OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES',
            'BRANCHES',
            'RESERVATAIRE',
            'LINKS',
            'SITUATION_MARITALE_PACS',
            'RECORD'
          ]
        }
      ]
    },
    operationRecords: {
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__COPROPRIETE: {
        branches: {
          COPROPRIETE: {
            constraints: {
              min: 0
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__FICHE_PROGRAMME: {
        branches: {
          CONDITIONS_GENERALES: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__LOTS: {
        branches: {
          BIEN_VENDU: {
            constraints: {
              min: 1
            },
            recordLinks: ['COMPOSITION__COPROPRIETE']
          }
        },
        constraints: {
          subOperationOnly: true
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__NOTAIRE_PROGRAMME: {
        branches: {
          NOTAIRE_PROGRAMME: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__PROMOTEURS: {
        branches: {
          PROMOTEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__REPRESENTANT_PROMOTEURS: {
        branches: {
          REPRESENTANT_PROMOTEUR: {
            constraints: {
              min: 1
            }
          }
        }
      },
      OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__CONTRAT_RESERVATION: {
        branches: {
          CONTRAT_RESA: {
            constraints: {
              min: 1
            }
          }
        }
      },

      OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES: {
        branches: {
          RESERVATAIRE: {
            constraints: {
              min: 1
            },
            recordLinks: ['SITUATION_MARITALE', 'REPRESENTATION__PERSONNE_MORALE']
          }
        }
      }
    },
    registeredLetter: {
      defaultReceivers: {
        OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES: {
          branches: {
            RESERVATAIRE: true
          }
        }
      },
      letterSubstitutions: {
        senderAddress: {
          items: [
            {
              path: ['sender', 'address', 'formattedAddress'],
              type: 'VARIABLE'
            }
          ]
        },
        senderName: {
          aggregate: {
            type: 'CONCAT'
          },
          items: [
            {
              path: ['sender', 'firstname'],
              type: 'VARIABLE'
            },
            {
              constant: ' ',
              type: 'CONSTANT'
            },
            {
              path: ['sender', 'lastname'],
              type: 'VARIABLE'
            }
          ]
        },
        signatureDate: {
          items: [
            {
              path: ['configuration', 'signatureTime'],
              transformers: [
                {
                  type: 'DATE'
                }
              ],
              type: 'VARIABLE'
            }
          ]
        }
      },
      letterTemplateId: 'REGISTERED_LETTER_VEFA'
    },
    signature: {
      blockingConditions: [],
      creationLockedAnswers: {},
      defaultSignatories: {
        OPERATION__I3F__IMMOBILIER__VEFA_PROGRAMME__REPRESENTANT_PROMOTEURS: {
          branches: {
            REPRESENTANT_PROMOTEUR: true
          }
        },
        OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__RESERVATAIRES: {
          branches: {
            RESERVATAIRE: true
          }
        }
      },
      mandatoryDocuments: []
    }
  },
  id: 'I_3_F__VEFA__VEFA_AVENANT',
  jeffersonPath: 'mynotary/i3F/vefa/vefaAvenant.json',
  label: 'Contrat préliminaire - VEFA Avenant',
  mainContract: false,
  originTemplate: null
};
