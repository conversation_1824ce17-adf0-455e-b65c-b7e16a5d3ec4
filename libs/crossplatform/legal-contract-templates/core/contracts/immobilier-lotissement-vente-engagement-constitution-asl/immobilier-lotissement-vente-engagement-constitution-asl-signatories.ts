import {
  GetDefaultSignatoriesArgs,
  createDefaultSignatoriesFromRecords,
  DefaultSignatory
} from '../../signatures/contract-signature-config';

function getDefaultSignatories(ctx: GetDefaultSignatoriesArgs): DefaultSignatory[] {
  const representant_lotisseursSignatories = createDefaultSignatoriesFromRecords({
    ctx,
    linkTemplateId: 'LINK__OPERATION__IMMOBILIER__LOTISSEMENT_PROGRAMME__REPRESENTANT_LOTISSEUR'
  });

  return [representant_lotisseursSignatories].flat();
}

export const ImmobilierLotissementVenteEngagementConstitutionAslSignatureConfig = {
  getDefaultSignatories: getDefaultSignatories
};
