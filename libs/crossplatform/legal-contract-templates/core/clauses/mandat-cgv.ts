// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
export const MandatCgv = {
  children: [
    {
      id: 'si-template-pp',
      condition: 'TEMPLATE_PP',
      children: [
        {
          id: 'si-template-pp-si-bareme-pp-nouveau',
          condition: 'BAREME_PP_NOUVEAU',
          prerequisites: {
            conditions: {
              BAREME_PP_NOUVEAU: true
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          TEMPLATE_PP: true
        }
      }
    },
    {
      id: 'si-non-template-pp',
      condition: '!TEMPLATE_PP',
      children: [
        {
          id: 'si-non-template-pp-si-bareme-pp-ancien',
          condition: 'BAREME_PP_ANCIEN',
          prerequisites: {
            conditions: {
              BAREME_PP_ANCIEN: true
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          TEMPLATE_PP: true
        }
      }
    },
    {
      id: 'texte',
      content:
        '**APRÈS AVOIR PRIS CONNAISSANCE DES CONDITIONS GÉNÉRALES, CI-APRÈS, le mandant confère au mandataire, qui accepte, MANDAT {{ MANDAT_TYPE }} de rechercher et de lui présenter un acquéreur pour les biens ci-après désignés aux prix, charges et conditions suivants :**\n',
      prerequisites: {
        variables: {
          MANDAT_TYPE: true
        }
      }
    },
    {
      id: '1-designation-et-situation-des-biens-a-vendre-',
      children: [
        {
          id: '1-designation-et-situation-des-biens-a-vendre--texte',
          content: '## 1 - DÉSIGNATION ET SITUATION DES BIENS A VENDRE :\n\n'
        },
        {
          id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien',
          condition: 'PRESENCE_BIEN',
          children: [
            {
              id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes',
              repetition: {
                source: 'COPROPRIETES',
                item: 'COPROPRIETE'
              },
              children: [
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens',
                  repetition: {
                    source: 'COPROPRIETE.BIENS',
                    item: 'BIEN'
                  },
                  children: [
                    {
                      id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-si-non-bien-type-autre',
                      condition: '!BIEN.TYPE_AUTRE',
                      content:
                        '_Type de bien :_ **{{ BIEN.TYPE }}** _n° de lot :_ **{{ BIEN.NUMERO_LOT_COPROPRIETE }}**\n',
                      prerequisites: {
                        variables: {
                          'BIEN.TYPE': true,
                          'BIEN.NUMERO_LOT_COPROPRIETE': true
                        },
                        conditions: {
                          'BIEN.TYPE_AUTRE': true
                        }
                      }
                    },
                    {
                      id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-si-bien-type-autre',
                      condition: 'BIEN.TYPE_AUTRE',
                      content:
                        '_Type de bien :_ **{{ BIEN.TYPE_AUTRE }}** _n° de lot :_ **{{ BIEN.NUMERO_LOT_COPROPRIETE }}**\n',
                      prerequisites: {
                        variables: {
                          'BIEN.TYPE_AUTRE': true,
                          'BIEN.NUMERO_LOT_COPROPRIETE': true
                        },
                        conditions: {
                          'BIEN.TYPE_AUTRE': true
                        }
                      }
                    },
                    {
                      id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-si-bien-designation-value',
                      condition: 'BIEN.DESIGNATION_VALUE',
                      content: '_Désignation :_ {{ BIEN.DESIGNATION }}\n',
                      prerequisites: {
                        variables: {
                          'BIEN.DESIGNATION': true
                        },
                        conditions: {
                          'BIEN.DESIGNATION_VALUE': true
                        }
                      }
                    },
                    {
                      id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-si-non-bien-designation-value',
                      condition: '!BIEN.DESIGNATION_VALUE',
                      content:
                        '_Désignation :_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n',
                      prerequisites: {
                        conditions: {
                          'BIEN.DESIGNATION_VALUE': true
                        }
                      }
                    },
                    {
                      id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-texte',
                      content: '_Superficie du bien :_  {{ BIEN.MESURAGE_CARREZ }} m2\n\n',
                      prerequisites: {
                        variables: {
                          'BIEN.MESURAGE_CARREZ': true
                        }
                      }
                    },
                    {
                      id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-liste-bien-annexes',
                      repetition: {
                        source: 'BIEN.ANNEXES',
                        item: 'ANNEXE'
                      },
                      children: [
                        {
                          id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-liste-bien-annexes-texte',
                          content: '**Désignation du lot annexe :**\n'
                        },
                        {
                          id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-liste-bien-annexes-si-non-annexe-type-autre',
                          condition: '!ANNEXE.TYPE_AUTRE',
                          content: '_Type de bien :_ **{{ ANNEXE.TYPE }}** _n° de lot :_ **{{ ANNEXE.NUMERO }}** \n',
                          prerequisites: {
                            variables: {
                              'ANNEXE.TYPE': true,
                              'ANNEXE.NUMERO': true
                            },
                            conditions: {
                              'ANNEXE.TYPE_AUTRE': true
                            }
                          }
                        },
                        {
                          id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-liste-bien-annexes-si-annexe-type-autre',
                          condition: 'ANNEXE.TYPE_AUTRE',
                          content:
                            '_Type de bien :_ **{{ ANNEXE.TYPE_AUTRE }}** _n° de lot :_ **{{ ANNEXE.NUMERO }}** \n',
                          prerequisites: {
                            variables: {
                              'ANNEXE.TYPE_AUTRE': true,
                              'ANNEXE.NUMERO': true
                            },
                            conditions: {
                              'ANNEXE.TYPE_AUTRE': true
                            }
                          }
                        },
                        {
                          id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-liste-bien-annexes-texte-1',
                          content: '_Désignation :_ {{ ANNEXE.DESIGNATION }}\n',
                          prerequisites: {
                            variables: {
                              'ANNEXE.DESIGNATION': true
                            }
                          }
                        }
                      ],
                      prerequisites: {
                        repeats: {
                          'BIEN.ANNEXES': {
                            variables: {},
                            conditions: {},
                            repeats: {},
                            raws: {}
                          }
                        }
                      }
                    }
                  ],
                  prerequisites: {
                    repeats: {
                      'COPROPRIETE.BIENS': {
                        variables: {},
                        conditions: {},
                        repeats: {},
                        raws: {}
                      }
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-coproprietes-liste-copropriete-biens-1',
                  repetition: {
                    source: 'COPROPRIETE.BIENS',
                    item: 'BIEN'
                  },
                  content: '_Adresse du bien :_ **{{ BIEN.ADRESSE }}**.\n',
                  prerequisites: {
                    repeats: {
                      'COPROPRIETE.BIENS': {
                        variables: {
                          'BIEN.ADRESSE': true
                        },
                        conditions: {},
                        repeats: {},
                        raws: {}
                      }
                    }
                  }
                }
              ],
              prerequisites: {
                repeats: {
                  COPROPRIETES: {
                    variables: {},
                    conditions: {},
                    repeats: {},
                    raws: {}
                  }
                }
              }
            },
            {
              id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-habitation',
              repetition: {
                source: 'BIEN_DETACHES_HABITATION',
                item: 'BIEN'
              },
              children: [
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-habitation-si-non-bien-type-autre',
                  condition: '!BIEN.TYPE_AUTRE',
                  content: '_Type de bien :_ {{ BIEN.TYPE }} \n',
                  prerequisites: {
                    variables: {
                      'BIEN.TYPE': true
                    },
                    conditions: {
                      'BIEN.TYPE_AUTRE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-habitation-si-bien-type-autre',
                  condition: 'BIEN.TYPE_AUTRE',
                  content: '_Type de bien :_ {{ BIEN.TYPE_AUTRE }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.TYPE_AUTRE': true
                    },
                    conditions: {
                      'BIEN.TYPE_AUTRE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-habitation-texte',
                  content: '_Réf Cadastrales :_\n'
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-habitation-liste-bien-cadastre-parcelles',
                  repetition: {
                    source: 'BIEN.CADASTRE_PARCELLES',
                    item: 'PARCELLE'
                  },
                  content:
                    ' _Section_ {{ PARCELLE.SECTION }} _Numéro_ {{ PARCELLE.NUMERO }} _Contenance_ {{ PARCELLE.H }} ha {{ PARCELLE.A }} a {{ PARCELLE.CA }} ca  \n',
                  prerequisites: {
                    repeats: {
                      'BIEN.CADASTRE_PARCELLES': {
                        variables: {
                          'PARCELLE.SECTION': true,
                          'PARCELLE.NUMERO': true,
                          'PARCELLE.H': true,
                          'PARCELLE.A': true,
                          'PARCELLE.CA': true
                        },
                        conditions: {},
                        repeats: {},
                        raws: {}
                      }
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-habitation-texte-1',
                  content: '_Adresse du bien :_ {{ BIEN.ADRESSE }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.ADRESSE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-habitation-si-bien-designation-value',
                  condition: 'BIEN.DESIGNATION_VALUE',
                  content: '_Désignation :_ {{ BIEN.DESIGNATION }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.DESIGNATION': true
                    },
                    conditions: {
                      'BIEN.DESIGNATION_VALUE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-habitation-si-non-bien-designation-value',
                  condition: '!BIEN.DESIGNATION_VALUE',
                  content:
                    '_Désignation :_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n',
                  prerequisites: {
                    conditions: {
                      'BIEN.DESIGNATION_VALUE': true
                    }
                  }
                }
              ],
              prerequisites: {
                repeats: {
                  BIEN_DETACHES_HABITATION: {
                    variables: {},
                    conditions: {},
                    repeats: {},
                    raws: {}
                  }
                }
              }
            },
            {
              id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-non-habitation',
              repetition: {
                source: 'BIEN_DETACHES_NON_HABITATION',
                item: 'BIEN'
              },
              children: [
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-non-habitation-si-non-bien-type-autre',
                  condition: '!BIEN.TYPE_AUTRE',
                  content: '_Type de bien :_ {{ BIEN.TYPE }} \n',
                  prerequisites: {
                    variables: {
                      'BIEN.TYPE': true
                    },
                    conditions: {
                      'BIEN.TYPE_AUTRE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-non-habitation-si-bien-type-autre',
                  condition: 'BIEN.TYPE_AUTRE',
                  content: '_Type de bien :_ {{ BIEN.TYPE_AUTRE }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.TYPE_AUTRE': true
                    },
                    conditions: {
                      'BIEN.TYPE_AUTRE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-non-habitation-texte',
                  content: '_Réf Cadastrales :_\n'
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-non-habitation-liste-bien-cadastre-parcelles',
                  repetition: {
                    source: 'BIEN.CADASTRE_PARCELLES',
                    item: 'PARCELLE'
                  },
                  content:
                    ' _Section_ {{ PARCELLE.SECTION }} _Numéro_ {{ PARCELLE.NUMERO }} _Contenance_ {{ PARCELLE.H }} ha {{ PARCELLE.A }} a {{ PARCELLE.CA }} ca  \n',
                  prerequisites: {
                    repeats: {
                      'BIEN.CADASTRE_PARCELLES': {
                        variables: {
                          'PARCELLE.SECTION': true,
                          'PARCELLE.NUMERO': true,
                          'PARCELLE.H': true,
                          'PARCELLE.A': true,
                          'PARCELLE.CA': true
                        },
                        conditions: {},
                        repeats: {},
                        raws: {}
                      }
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-non-habitation-texte-1',
                  content: '_Adresse du bien :_ {{ BIEN.ADRESSE }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.ADRESSE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-non-habitation-si-bien-designation-value',
                  condition: 'BIEN.DESIGNATION_VALUE',
                  content: '_Désignation :_ {{ BIEN.DESIGNATION }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.DESIGNATION': true
                    },
                    conditions: {
                      'BIEN.DESIGNATION_VALUE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-detaches-non-habitation-si-non-bien-designation-value',
                  condition: '!BIEN.DESIGNATION_VALUE',
                  content:
                    '_Désignation :_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n',
                  prerequisites: {
                    conditions: {
                      'BIEN.DESIGNATION_VALUE': true
                    }
                  }
                }
              ],
              prerequisites: {
                repeats: {
                  BIEN_DETACHES_NON_HABITATION: {
                    variables: {},
                    conditions: {},
                    repeats: {},
                    raws: {}
                  }
                }
              }
            },
            {
              id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-tab',
              repetition: {
                source: 'BIEN_TAB',
                item: 'BIEN'
              },
              children: [
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-tab-texte',
                  content: '_Type de bien :_ Un terrain à bâtir\n_Réf Cadastrales :_\n'
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-tab-liste-bien-cadastre-parcelles',
                  repetition: {
                    source: 'BIEN.CADASTRE_PARCELLES',
                    item: 'PARCELLE'
                  },
                  content:
                    ' _Section_ {{ PARCELLE.SECTION }} _Numéro_ {{ PARCELLE.NUMERO }} _Contenance_ {{ PARCELLE.H }} ha {{ PARCELLE.A }} a {{ PARCELLE.CA }} ca  \n',
                  prerequisites: {
                    repeats: {
                      'BIEN.CADASTRE_PARCELLES': {
                        variables: {
                          'PARCELLE.SECTION': true,
                          'PARCELLE.NUMERO': true,
                          'PARCELLE.H': true,
                          'PARCELLE.A': true,
                          'PARCELLE.CA': true
                        },
                        conditions: {},
                        repeats: {},
                        raws: {}
                      }
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-tab-texte-1',
                  content: '_Superficie du bien_ : {{ BIEN.SUPERFICIE }} m2.\n_Adresse du bien :_ {{ BIEN.ADRESSE }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.SUPERFICIE': true,
                      'BIEN.ADRESSE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-tab-si-bien-designation-value',
                  condition: 'BIEN.DESIGNATION_VALUE',
                  content: '_Désignation :_ {{ BIEN.DESIGNATION }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.DESIGNATION': true
                    },
                    conditions: {
                      'BIEN.DESIGNATION_VALUE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-tab-si-non-bien-designation-value',
                  condition: '!BIEN.DESIGNATION_VALUE',
                  content:
                    '_Désignation :_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n',
                  prerequisites: {
                    conditions: {
                      'BIEN.DESIGNATION_VALUE': true
                    }
                  }
                }
              ],
              prerequisites: {
                repeats: {
                  BIEN_TAB: {
                    variables: {},
                    conditions: {},
                    repeats: {},
                    raws: {}
                  }
                }
              }
            },
            {
              id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-non-tab',
              repetition: {
                source: 'BIEN_NON_TAB',
                item: 'BIEN'
              },
              children: [
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-non-tab-texte',
                  content: '_Type de bien :_ Un terrain non constructible\n_Réf Cadastrales :_\n'
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-non-tab-liste-bien-cadastre-parcelles',
                  repetition: {
                    source: 'BIEN.CADASTRE_PARCELLES',
                    item: 'PARCELLE'
                  },
                  content:
                    ' _Section_ {{ PARCELLE.SECTION }} _Numéro_ {{ PARCELLE.NUMERO }} _Contenance_ {{ PARCELLE.H }} ha {{ PARCELLE.A }} a {{ PARCELLE.CA }} ca  \n',
                  prerequisites: {
                    repeats: {
                      'BIEN.CADASTRE_PARCELLES': {
                        variables: {
                          'PARCELLE.SECTION': true,
                          'PARCELLE.NUMERO': true,
                          'PARCELLE.H': true,
                          'PARCELLE.A': true,
                          'PARCELLE.CA': true
                        },
                        conditions: {},
                        repeats: {},
                        raws: {}
                      }
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-non-tab-texte-1',
                  content: '_Adresse du bien :_ {{ BIEN.ADRESSE }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.ADRESSE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-non-tab-si-bien-designation-value',
                  condition: 'BIEN.DESIGNATION_VALUE',
                  content: '_Désignation :_ {{ BIEN.DESIGNATION }}\n',
                  prerequisites: {
                    variables: {
                      'BIEN.DESIGNATION': true
                    },
                    conditions: {
                      'BIEN.DESIGNATION_VALUE': true
                    }
                  }
                },
                {
                  id: '1-designation-et-situation-des-biens-a-vendre--si-presence-bien-liste-bien-non-tab-si-non-bien-designation-value',
                  condition: '!BIEN.DESIGNATION_VALUE',
                  content:
                    '_Désignation :_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n',
                  prerequisites: {
                    conditions: {
                      'BIEN.DESIGNATION_VALUE': true
                    }
                  }
                }
              ],
              prerequisites: {
                repeats: {
                  BIEN_NON_TAB: {
                    variables: {},
                    conditions: {},
                    repeats: {},
                    raws: {}
                  }
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              PRESENCE_BIEN: true
            }
          }
        },
        {
          id: '1-designation-et-situation-des-biens-a-vendre--si-non-presence-bien',
          condition: '!PRESENCE_BIEN',
          content:
            '_Type de bien :_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ _Si copropriété : N° du (des) lot(s) :_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n_Réf Cadastrales :_\n_Section_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ _Numéro_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ _Contenance_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_  \n_Adresse du bien :_ \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n_Désignation :_\n\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n',
          prerequisites: {
            conditions: {
              PRESENCE_BIEN: true
            }
          }
        },
        {
          id: '1-designation-et-situation-des-biens-a-vendre--si-non-mandat-libre',
          condition: '!MANDAT_LIBRE',
          children: [
            {
              id: '1-designation-et-situation-des-biens-a-vendre--si-non-mandat-libre-si-non-mandat-loue',
              condition: '!MANDAT_LOUE',
              content:
                'Le jour de la signature de l’acte de vente, les biens seront : {{ BOX.UNCHECKED }} libres de toute occupation {{ BOX.UNCHECKED }} loués suivant l’état locatif ci-annexé.\n',
              prerequisites: {
                variables: {
                  'BOX.UNCHECKED': true
                },
                conditions: {
                  MANDAT_LOUE: true
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              MANDAT_LIBRE: true
            }
          }
        },
        {
          id: '1-designation-et-situation-des-biens-a-vendre--si-mandat-libre',
          condition: 'MANDAT_LIBRE',
          content: 'Le jour de la signature de l’acte de vente, les biens seront libres de toute occupation.\n',
          prerequisites: {
            conditions: {
              MANDAT_LIBRE: true
            }
          }
        },
        {
          id: '1-designation-et-situation-des-biens-a-vendre--si-mandat-loue',
          condition: 'MANDAT_LOUE',
          content:
            'Le jour de la signature de l’acte de vente, les biens seront loués suivant l’état locatif ci-annexé.\n',
          prerequisites: {
            conditions: {
              MANDAT_LOUE: true
            }
          }
        }
      ]
    },
    {
      id: '2-le-type-de-mandat-',
      children: [
        {
          id: '2-le-type-de-mandat--texte',
          content: '## 2 - LE TYPE DE MANDAT : \n'
        },
        {
          id: '2-le-type-de-mandat--si-non-mandat-semi',
          condition: '!MANDAT_SEMI',
          children: [
            {
              id: '2-le-type-de-mandat--si-non-mandat-semi-si-non-mandat-simple',
              condition: '!MANDAT_SIMPLE',
              children: [
                {
                  id: '2-le-type-de-mandat--si-non-mandat-semi-si-non-mandat-simple-si-non-mandat-exclusif',
                  condition: '!MANDAT_EXCLUSIF',
                  content:
                    "{{ BOX.UNCHECKED }} **Mandat SIMPLE** : le vendeur garde la liberté de vendre les biens désignés ci-dessus par lui-même ou avec le concours de tout intermédiaire de son choix.\n{{ BOX.UNCHECKED }} **Mandat SEMI-EXCLUSIF** : le vendeur s’interdit de vendre les biens désignés ci-dessus par l'intermédiaire d'un autre professionnel de l'immobilier.\n{{ BOX.UNCHECKED }} **Mandat EXCLUSIF** : Le vendeur s’interdit de vendre directement ou indirectement les biens désignés ci-dessus. Il s’engage à diriger toute demande ou offre à l'Agence.\n",
                  prerequisites: {
                    variables: {
                      'BOX.UNCHECKED': true
                    },
                    conditions: {
                      MANDAT_EXCLUSIF: true
                    }
                  }
                }
              ],
              prerequisites: {
                conditions: {
                  MANDAT_SIMPLE: true
                }
              }
            },
            {
              id: '2-le-type-de-mandat--si-non-mandat-semi-si-mandat-simple',
              condition: 'MANDAT_SIMPLE',
              content:
                '**Mandat SIMPLE** : le vendeur garde la liberté de vendre les biens désignés ci-dessus par lui-même ou avec le concours de tout intermédiaire de son choix.\n',
              prerequisites: {
                conditions: {
                  MANDAT_SIMPLE: true
                }
              }
            },
            {
              id: '2-le-type-de-mandat--si-non-mandat-semi-si-mandat-exclusif',
              condition: 'MANDAT_EXCLUSIF',
              content:
                "**Mandat EXCLUSIF** : Le vendeur s’interdit de vendre directement ou indirectement les biens désignés ci-dessus. Il s’engage à diriger toute demande ou offre à l'Agence.\n",
              prerequisites: {
                conditions: {
                  MANDAT_EXCLUSIF: true
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              MANDAT_SEMI: true
            }
          }
        },
        {
          id: '2-le-type-de-mandat--si-mandat-semi',
          condition: 'MANDAT_SEMI',
          content:
            "**Mandat SEMI-EXCLUSIF** : le vendeur s’interdit de vendre les biens désignés ci-dessus par l'intermédiaire d'un autre professionnel de l'immobilier.\n",
          prerequisites: {
            conditions: {
              MANDAT_SEMI: true
            }
          }
        }
      ]
    },
    {
      id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence-',
      children: [
        {
          id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--texte',
          content:
            '## 3 – LE PRIX DE PRÉSENTATION DES BIENS ET LES HONORAIRES DE L’AGENCE : \n\nLes honoraires TTC ci-dessous, exigibles le jour où l’opération sera effectivement conclue et constatée par acte notarié, seront :\n\n'
        },
        {
          id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-non-mandat-success',
          condition: '!MANDAT_SUCCESS',
          children: [
            {
              id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-non-mandat-success-si-non-mandat-charge-vendeur',
              condition: '!MANDAT.CHARGE_VENDEUR',
              children: [
                {
                  id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-non-mandat-success-si-non-mandat-charge-vendeur-si-non-mandat-charge-acquereur',
                  condition: '!MANDAT.CHARGE_ACQUEREUR',
                  children: [
                    {
                      id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-non-mandat-success-si-non-mandat-charge-vendeur-si-non-mandat-charge-acquereur-texte',
                      content:
                        "**{{ BOX.UNCHECKED }} A la charge du VENDEUR**\n\n- **Le prix demandé** (TVA et toutes autres taxes INCLUSES) **est de** :  \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n_(Montant que le vendeur percevra AVANT de payer le montant des honoraires de l’agence)_\n- **Les honoraires TTC de l'agence s'élèvent à :**  \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n",
                      prerequisites: {
                        variables: {
                          'BOX.UNCHECKED': true
                        }
                      }
                    },
                    {
                      id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-non-mandat-success-si-non-mandat-charge-vendeur-si-non-mandat-charge-acquereur-saut-de-ligne',
                      enhancedVariable: {
                        type: 'lineBreak',
                        parameters: {
                          number: 1
                        }
                      }
                    },
                    {
                      id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-non-mandat-success-si-non-mandat-charge-vendeur-si-non-mandat-charge-acquereur-texte-1',
                      content:
                        "**{{ BOX.UNCHECKED }} A la charge de l'ACQUEREUR**\n\n- **Le prix demandé (hors honoraires de l’agence)** (TVA et toutes autres taxes INCLUSES) **est de** : \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n- **Les honoraires TTC de l’agence s’élèvent à :** \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n- **Le prix affiché (incluant les honoraires de l’agence TTC) est de :** \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n_En cas d’exercice d’un droit de substitution ou de préemption, les honoraires stipulés ci-dessus seront dus par le préempteur ou le mandant ou l'acquéreur substitué._\n",
                      prerequisites: {
                        variables: {
                          'BOX.UNCHECKED': true
                        }
                      }
                    }
                  ],
                  prerequisites: {
                    conditions: {
                      'MANDAT.CHARGE_ACQUEREUR': true
                    }
                  }
                }
              ],
              prerequisites: {
                conditions: {
                  'MANDAT.CHARGE_VENDEUR': true
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              MANDAT_SUCCESS: true
            }
          }
        },
        {
          id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-non-mandat-success-1',
          condition: '!MANDAT_SUCCESS',
          children: [
            {
              id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-non-mandat-success-1-si-mandat-charge-vendeur',
              condition: 'MANDAT.CHARGE_VENDEUR',
              content:
                "**A la charge du VENDEUR**\n\n- **Le prix demandé** (TVA et toutes autres taxes INCLUSES) **est de** :  **{{ PRIX.VENTE }}**\n_(Montant que le vendeur percevra AVANT de payer le montant des honoraires de l’agence)_\n- **Les honoraires TTC de l'agence s'élèvent à :**  **{{ HONORAIRES_TOTAL }}**\n",
              prerequisites: {
                variables: {
                  'PRIX.VENTE': true,
                  HONORAIRES_TOTAL: true
                },
                conditions: {
                  'MANDAT.CHARGE_VENDEUR': true
                }
              }
            },
            {
              id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-non-mandat-success-1-si-mandat-charge-acquereur',
              condition: 'MANDAT.CHARGE_ACQUEREUR',
              content:
                "**A la charge de l'ACQUEREUR**\n\n- **Le prix demandé (hors honoraires de l’agence)** (TVA et toutes autres taxes INCLUSES) **est de** : **{{ PRIX.VENTE }}**\n- **Les honoraires TTC de l’agence s’élèvent à :**  **{{ HONORAIRES_TOTAL }}**\n- **Le prix affiché (incluant les honoraires de l’agence TTC) est de :** **{{ HONORAIRES_ET_PRIX }}**\n_En cas d’exercice d’un droit de substitution ou de préemption, les honoraires stipulés ci-dessus seront dus par le préempteur ou le mandant ou l'acquéreur substitué._\n",
              prerequisites: {
                variables: {
                  'PRIX.VENTE': true,
                  HONORAIRES_TOTAL: true,
                  HONORAIRES_ET_PRIX: true
                },
                conditions: {
                  'MANDAT.CHARGE_ACQUEREUR': true
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              MANDAT_SUCCESS: true
            }
          }
        },
        {
          id: '3-le-prix-de-presentation-des-biens-et-les-honoraires-de-l-agence--si-mandat-success',
          condition: 'MANDAT_SUCCESS',
          content:
            "**A la charge du VENDEUR**\n- **Le prix demandé** (TVA et toutes autres taxes INCLUSES) **est de** :  **{{ PRIX.VENTE }}**\n_(Montant que le vendeur percevra AVANT de payer le montant des honoraires de l’agence)_\n- **Les honoraires TTC de l'agence s'élèvent à :**  **{{ HONORAIRES_TOTAL }}**\n",
          prerequisites: {
            variables: {
              'PRIX.VENTE': true,
              HONORAIRES_TOTAL: true
            },
            conditions: {
              MANDAT_SUCCESS: true
            }
          }
        }
      ]
    },
    {
      id: '4-la-duree-du-mandat-',
      children: [
        {
          id: '4-la-duree-du-mandat--texte',
          content: '## 4 - LA DURÉE DU MANDAT :\n\n'
        },
        {
          id: '4-la-duree-du-mandat--si-mandat-trois',
          condition: 'MANDAT_TROIS',
          content:
            'Le présent mandat est donné à compter du jour de la dernière signature pour une **durée non renouvelable** de **TROIS MOIS. Passé ce délai, il prendra fin automatiquement.**\n',
          prerequisites: {
            conditions: {
              MANDAT_TROIS: true
            }
          }
        },
        {
          id: '4-la-duree-du-mandat--si-mandat-six',
          condition: 'MANDAT_SIX',
          content:
            'Le présent mandat est donné à compter du jour de la dernière signature pour une **durée non renouvelable** de **SIX MOIS. Passé ce délai, il prendra fin automatiquement.**\n',
          prerequisites: {
            conditions: {
              MANDAT_SIX: true
            }
          }
        },
        {
          id: '4-la-duree-du-mandat--si-mandat-neuf',
          condition: 'MANDAT_NEUF',
          content:
            'Le présent mandat est donné à compter du jour de la dernière signature pour une **durée non renouvelable** de **NEUF MOIS. Passé ce délai, il prendra fin automatiquement.**\n',
          prerequisites: {
            conditions: {
              MANDAT_NEUF: true
            }
          }
        },
        {
          id: '4-la-duree-du-mandat--si-non-mandat-trois',
          condition: '!MANDAT_TROIS',
          children: [
            {
              id: '4-la-duree-du-mandat--si-non-mandat-trois-si-non-mandat-six',
              condition: '!MANDAT_SIX',
              children: [
                {
                  id: '4-la-duree-du-mandat--si-non-mandat-trois-si-non-mandat-six-si-non-mandat-neuf',
                  condition: '!MANDAT_NEUF',
                  content:
                    'Le présent mandat est donné à compter du jour de la dernière signature pour une **durée non renouvelable** de : \n**{{ BOX.UNCHECKED }} TROIS MOIS {{ BOX.UNCHECKED }} SIX MOIS {{ BOX.UNCHECKED }} NEUF MOIS. Passé ce délai, il prendra fin automatiquement.**\n',
                  prerequisites: {
                    variables: {
                      'BOX.UNCHECKED': true
                    },
                    conditions: {
                      MANDAT_NEUF: true
                    }
                  }
                }
              ],
              prerequisites: {
                conditions: {
                  MANDAT_SIX: true
                }
              }
            }
          ],
          prerequisites: {
            conditions: {
              MANDAT_TROIS: true
            }
          }
        },
        {
          id: '4-la-duree-du-mandat--texte-1',
          content:
            '**Il est révocable à tout moment par tout moyen écrit : fax, mail, lettre simple ou recommandée.**\n\n'
        }
      ]
    },
    {
      id: 'si-clause-particuliere',
      condition: 'CLAUSE_PARTICULIERE',
      children: [
        {
          id: 'si-clause-particuliere-texte',
          content: '## 5 - LES CLAUSES PARTICULIERES \n\n'
        },
        {
          id: 'si-clause-particuliere-liste-clause-particulieres',
          repetition: {
            source: 'CLAUSE_PARTICULIERES',
            item: 'CLAUSE_PARTICULIERE'
          },
          content: '{{ CLAUSE_PARTICULIERE.CONTENU }}\n',
          prerequisites: {
            repeats: {
              CLAUSE_PARTICULIERES: {
                variables: {
                  'CLAUSE_PARTICULIERE.CONTENU': true
                },
                conditions: {},
                repeats: {},
                raws: {}
              }
            }
          }
        }
      ],
      prerequisites: {
        conditions: {
          CLAUSE_PARTICULIERE: true
        }
      }
    },
    {
      id: 'si-non-clause-particuliere-value',
      condition: '!CLAUSE_PARTICULIERE_VALUE',
      content:
        '## 5 - LES CLAUSES PARTICULIERES \n\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\n',
      prerequisites: {
        conditions: {
          CLAUSE_PARTICULIERE_VALUE: true
        }
      }
    },
    {
      id: 'si-mandat-exclusif',
      condition: 'MANDAT_EXCLUSIF',
      content: '# CONDITIONS GÉNÉRALES DU MANDAT EXCLUSIF CONSENTI ET ACCEPTÉ AUX CONDITIONS FIGURANT CI-DESSOUS\n',
      prerequisites: {
        conditions: {
          MANDAT_EXCLUSIF: true
        }
      }
    },
    {
      id: 'si-mandat-success',
      condition: 'MANDAT_SUCCESS',
      content: '# CONDITIONS GÉNÉRALES DU MANDAT SUCCESS CONSENTI ET ACCEPTÉ AUX CONDITIONS FIGURANT CI-DESSOUS\n',
      prerequisites: {
        conditions: {
          MANDAT_SUCCESS: true
        }
      }
    },
    {
      id: 'si-mandat-semi',
      condition: 'MANDAT_SEMI',
      content:
        '# CONDITIONS GÉNÉRALES DU MANDAT SEMI-EXCLUSIF CONSENTI ET ACCEPTÉ AUX CONDITIONS FIGURANT CI-DESSOUS\n',
      prerequisites: {
        conditions: {
          MANDAT_SEMI: true
        }
      }
    },
    {
      id: 'si-mandat-simple',
      condition: 'MANDAT_SIMPLE',
      content: '# CONDITIONS GÉNÉRALES DU MANDAT SIMPLE CONSENTI ET ACCEPTÉ AUX CONDITIONS FIGURANT CI-DESSOUS\n',
      prerequisites: {
        conditions: {
          MANDAT_SIMPLE: true
        }
      }
    },
    {
      id: 'conditions-concernant-le-mandataire-',
      children: [
        {
          id: 'conditions-concernant-le-mandataire--texte',
          content:
            '## CONDITIONS CONCERNANT LE MANDATAIRE :\n\n- Le mandataire s’engage à effectuer toute promotion du bien en procédant à sa diffusion en fonction de ses particularités sur les sites d’annonces immobilières spécialisés et ou généralistes référencés par le mandataire ;\n- Le mandataire s’engage à conserver, dans tous les cas, l’exemplaire du présent mandat par dérogation aux dispositions de l’article 2004 du Code civil ;\n- Le mandataire représenté par le négociateur désigné précédemment s’engage à :\n  - Effectuer une sélection préalable des candidats acquéreurs avant d’effectuer des visites permettant d’éluder toute personne insolvable ou mal intentionnée ;\n'
        },
        {
          id: 'conditions-concernant-le-mandataire--si-mandat-exclusif',
          condition: 'MANDAT_EXCLUSIF',
          content:
            '  - Pour **TOUT MANDAT EXCLUSIF** Réserver une mise en avant du bien sur des sites dédiés aux mandats exclusifs ;\n',
          prerequisites: {
            conditions: {
              MANDAT_EXCLUSIF: true
            }
          }
        },
        {
          id: 'conditions-concernant-le-mandataire--texte-1',
          content:
            '  - Rendre compte dans les conditions de l’article 6 de la loi du 2 janvier 1970 et de l’article 77 du décret du 20 juillet 1972 et à procéder régulièrement à une information par mail des actions entreprises et/ou réalisées ;\n  - Organiser avec le mandant un rendez-vous téléphonique ou physique tous les mois pour faire le point sur le déroulement de sa mission.\n\n'
        },
        {
          id: 'conditions-concernant-le-mandataire--saut-de-ligne',
          enhancedVariable: {
            type: 'lineBreak',
            parameters: {
              number: 1
            }
          }
        },
        {
          id: 'conditions-concernant-le-mandataire--texte-2',
          content:
            'Le mandataire ne pourra, en aucun cas, être considéré comme le gardien juridique des biens à vendre, sa mission étant essentiellement de rechercher un acquéreur. En conséquence, il appartiendra au mandant de prendre toutes dispositions, jusqu’à la vente, pour assurer la bonne conservation de ses biens et de souscrire toutes assurances qu’il estimerait nécessaires.\n\n'
        }
      ]
    },
    {
      id: 'conditions-concernant-le-mandant-',
      children: [
        {
          id: 'conditions-concernant-le-mandant--texte',
          content:
            "## CONDITIONS CONCERNANT LE MANDANT :\nEn conséquence du présent mandat, le mandant :\n\n- Déclare avoir la capacité pleine et entière de disposer desdits biens. En outre, le mandant déclare, sous sa responsabilité, ne faire l’objet, d’aucune mesure de protection de la personne (curatelle, tutelle…) ni d’aucune procédure collective, et notamment de redressement ou de liquidation judiciaires, **autres que celles éventuellement exposées ci-dessus** et que les biens, objets du présent mandat, ne font l’objet d’aucune procédure de saisie immobilière ;\n- Déclare renoncer expressément au droit de révocation ad nutum du présent mandat, par dérogation à l'article 2004 du Code civil ;\n- Déclare ne pas avoir consenti, par ailleurs, de mandat exclusif de vente non expiré ou non dénoncé et s'interdit de le faire ultérieurement sans avoir préalablement dénoncé le présent mandat ;\n- S'engage, le cas échéant, à fournir au mandataire sans délai le classement du bien au regard de sa performance énergétique, étant ici rappelé qu'en application de l'article L. 134-4-3 du code de la construction et de l'habitation, les annonces relatives à la vente afférentes à des biens immobiliers soumis au DPE doivent obligatoirement mentionner ce classement selon des modalités définies par décret en Conseil d'Etat ;\n- S'engage à produire toutes les pièces justificatives de propriété demandées par le mandataire et à l'informer de toutes modifications concernant le bien et/ou le propriétaire ;\n- Donne au mandataire tous pouvoirs pour réclamer toutes pièces utiles auprès de toutes personnes privées ou publiques, notamment le certificat d'urbanisme ainsi que celles relatives au contrôle de l'installation d'assainissement équipant le bien objet du présent mandat ;\n- Autorise expressément le mandataire à :\n  - Saisir l'ensemble des informations contenu dans le présent mandat sur tout fichier de traitement automatisé de données (cf. clause relative à la protection des données personnelles du mandant, ci-dessous) ;\n  - Faire tout ce qu'il jugera utile pour parvenir à la vente, effectuer toute publicité à sa convenance avec diffusion éventuelle de photos et notamment pose de panonceaux, insertion dans des supports électroniques aux frais du mandataire ;\n  - Indiquer, présenter et faire visiter les biens désignés sur le présent mandat à toutes personnes qu'il jugera utile. A cet effet, il s'oblige à lui assurer le moyen de visiter pendant le cours du présent mandat ;\n  - Substituer, faire appel à tout concours et faire tout ce qu'il jugera utile en vue de mener à bonne fin la conclusion de la vente des biens sus désignés ;\n  - Autorise l’agence à établir tout acte sous seing privé aux clauses et conditions nécessaires à l'accomplissement des présentes et recueillir la signature de l’acquéreur. Dans le respect de ses obligations légales, le mandant s’engage à fournir au mandataire dans les plus brefs délais tout document nécessaire à la rédaction de l’acte et notamment les diagnostics techniques obligatoires en application de l'article L. 271-4 du code de la construction et de l'habitation. Il sollicite à cet effet le concours du mandataire dans la recherche d'un diagnostiqueur chargé de la réalisation desdits diagnostics.\n  - Autorise le mandataire, en cas d’exercice d’un droit de préemption, à négocier et conclure avec le préempteur, bénéficiaire de ce droit, sauf à en référer à son mandant, lequel conserve la faculté d’accepter le prix finalement obtenu par le mandataire.\n- Autorise expressément l’agence à recevoir un versement d’un montant maximum de 10 % du prix total de la vente. Ce versement sera effectué par virement à la banque où est ouvert le compte séquestre du mandataire dont les numéros de compte et coordonnées figurent en tête de l'acte. (article 55 du décret du 20 juillet 1972).\n- Autorise expressément que lui soit adressé par courrier électronique toutes les notifications nécessaires dans le cadre de ce mandat, conformément aux dispositions de l’article L 100 du Code des Postes et des Communications Electroniques, à l'adresse mail indiquée au contrat.\n"
        },
        {
          id: 'conditions-concernant-le-mandant--saut-de-ligne',
          enhancedVariable: {
            type: 'lineBreak',
            parameters: {
              number: 1
            }
          }
        },
        {
          id: 'conditions-concernant-le-mandant--texte-1',
          content:
            '**Pendant le cours du présent mandat, ainsi que dans les 12 mois suivant l’expiration ou la résiliation de celui-ci, le mandant s’interdit de traiter directement ou par l’intermédiaire d’un autre mandataire avec un acheteur présenté à lui par le mandataire aux présentes, ou un mandataire substitué, y compris à des conditions différentes de prix de vente ou d’honoraires de l’agence. Cette interdiction vise tant la personne de l’acheteur que son conjoint, concubin ou partenaire de PACS avec lequel il se portera acquéreur, ou encore toute société dans laquelle ledit acheteur aurait une participation.**\n\n'
        },
        {
          id: 'conditions-concernant-le-mandant--si-mandat-exclusif',
          condition: 'MANDAT_EXCLUSIF',
          children: [
            {
              id: 'conditions-concernant-le-mandant--si-mandat-exclusif-texte',
              content:
                '<u>**En cas de mandat EXCLUSIF**</u>, **le mandant s’oblige à diriger vers le mandataire toutes les demandes ou offres. Il ne pourra traiter directement ou par l’intermédiaire d’un autre mandataire, la vente des biens ci-dessus désignés.**\n'
            },
            {
              id: 'conditions-concernant-le-mandant--si-mandat-exclusif-saut-de-ligne',
              enhancedVariable: {
                type: 'lineBreak',
                parameters: {
                  number: 1
                }
              }
            },
            {
              id: 'conditions-concernant-le-mandant--si-mandat-exclusif-texte-1',
              content:
                '**À défaut de respecter ces clauses, le mandataire aura droit à une indemnité forfaitaire, à titre de clause pénale, à la charge du mandant, d’un montant égal à celui des honoraires de l’agence toutes taxes comprises prévus au présent mandat.**\n'
            }
          ],
          prerequisites: {
            conditions: {
              MANDAT_EXCLUSIF: true
            }
          }
        },
        {
          id: 'conditions-concernant-le-mandant--si-mandat-simple',
          condition: 'MANDAT_SIMPLE',
          children: [
            {
              id: 'conditions-concernant-le-mandant--si-mandat-simple-texte',
              content:
                '**À défaut de respecter ces clauses, le mandataire aura droit à une indemnité forfaitaire, à titre de clause pénale, à la charge du mandant, d’un montant égal à celui des honoraires de l’agence toutes taxes comprises prévus au présent mandat.**\n'
            },
            {
              id: 'conditions-concernant-le-mandant--si-mandat-simple-saut-de-ligne',
              enhancedVariable: {
                type: 'lineBreak',
                parameters: {
                  number: 1
                }
              }
            },
            {
              id: 'conditions-concernant-le-mandant--si-mandat-simple-texte-1',
              content:
                'Si le mandant vend sans l’intervention du mandataire dans le cadre d’un **mandat SIMPLE**, il s’oblige à en informer sans délai le mandataire, par mail ou lettre, en lui précisant le nom et l’adresse de l’acquéreur.\n\n'
            }
          ],
          prerequisites: {
            conditions: {
              MANDAT_SIMPLE: true
            }
          }
        },
        {
          id: 'conditions-concernant-le-mandant--si-mandat-semi',
          condition: 'MANDAT_SEMI',
          content:
            '<u>**En cas de mandat SEMI_EXCLUSIF**</u> le mandant ne pourra pas traiter par l’intermédiaire d’un autre mandataire ou une autre agence, la vente des biens ci-dessus désignés.\n**À défaut de respecter ces clauses, le mandataire aura droit à une indemnité forfaitaire, à titre de clause pénale, à la charge du mandant, d’un montant égal à celui des honoraires de l’agence toutes taxes comprises prévus au présent mandat.**\nSi le mandant vend sans l’intervention du mandataire dans le cadre d’un **mandat SEMI-EXCLUSIF**, il s’oblige à en informer sans délai le mandataire, par mail ou lettre, en lui précisant le nom et l’adresse de l’acquéreur.\n',
          prerequisites: {
            conditions: {
              MANDAT_SEMI: true
            }
          }
        },
        {
          id: 'conditions-concernant-le-mandant--saut-de-ligne-1',
          enhancedVariable: {
            type: 'lineBreak',
            parameters: {
              number: 1
            }
          }
        },
        {
          id: 'conditions-concernant-le-mandant--texte-2',
          content:
            "Si le présent mandat porte sur un ou plusieurs lots ou fractions de lots de copropriété, il est ici rappelé que l’article 46 de la loi n° 65-557 du 10 juillet 1965 dispose que, sauf pour les caves, garages, emplacements de stationnement ou lots ou fractions de lots d'une superficie inférieure à 8 m2, toute promesse unilatérale de vente, tout contrat réalisant ou constatant la vente d'un lot ou d'une fraction de lot mentionne la superficie de la partie privative de ce lot ou de cette fraction de lot. La nullité de l'acte peut être invoquée sur le fondement de l'absence de toute mention de cette superficie. Si la superficie est supérieure à celle exprimée dans l'acte, l'excédent de mesure ne donne lieu à aucun supplément de prix. Si la superficie est inférieure de plus d'un vingtième à celle exprimée dans l'acte, le vendeur, à la demande de l'acquéreur, supporte une diminution du prix proportionnelle à la moindre mesure. Si le présent mandat porte sur un ou plusieurs biens immobiliers situés dans des zones couvertes par un plan de prévention des risques technologiques ou par un plan de prévention des risques naturels prévisibles ou miniers, prescrit ou approuvé, ou dans des zones de sismicité définies par décret en Conseil d’Etat, il est ici rappelé que conformément à l’article L. 125-5 du code de l’environnement, l’acquéreur est informé par le vendeur de l’existence des risques visés par ces plans ou ce décret. De plus, lorsqu’un immeuble bâti a subi un sinistre ayant donné lieu au versement d’une indemnité d’assurance garantissant les risques de catastrophes naturelles ou technologiques visés respectivement par les articles L. 125-2 et L 128-2 du code des assurances, le vendeur est tenu d’informer par écrit l’acquéreur de tout sinistre survenu pendant la période où il a été propriétaire ou dont il a été lui-même informé en application des présentes dispositions. En cas de non-respect, l’acquéreur peut poursuivre la résolution du contrat ou demander au juge une diminution du prix de vente.\n\n"
        }
      ]
    },
    {
      id: 'protection-des-donnees-personnelles-du-mandant-',
      content:
        '## PROTECTION DES DONNÉES PERSONNELLES DU MANDANT :\n\nVos données personnelles collectées dans le cadre du présent mandat font l’objet d’un traitement nécessaire à son exécution. Elles sont susceptibles d’être utilisées dans le cadre de l’application de réglementations comme celle relative à la lutte contre le blanchiment des capitaux et le financement du terrorisme. Vos données personnelles sont conservées pendant toute la durée de l’exécution du présent mandat, augmentée des délais légaux de prescription applicable. Elles sont destinées à l’agence qui porte la responsabilité du traitement des données personnelles. Conformément à la loi informatique et libertés, vous bénéficiez d’un droit d’accès, de rectification, de suppression, d’opposition et de portabilité de vos données en vous adressant à **IMMO RESEAU – 44 Allée des Cinq Continents, ZAC Le Chêne Ferré, 44120 VERTOU – <EMAIL>.** Vous pouvez porter toute réclamation devant la Cnil (www.cnil.fr). Dans le cas où des coordonnées téléphoniques ont été recueillies, vous êtes informé(e)(s) de la faculté de vous inscrire sur la liste d’opposition au démarchage téléphonique prévue en faveur des consommateurs (article L. 223-1 du code de la consommation).\n\n'
    },
    {
      id: 'mediation-de-la-consommation-reglement-amiable-des-litiges-',
      content:
        '## MÉDIATION DE LA CONSOMMATION – RÈGLEMENT AMIABLE DES LITIGES : \nPour tout litige afférent à l’exécution du présent mandat, le mandant, s’il est un « consommateur » au sens de l’article liminaire du code de la consommation, est informé qu’il peut saisir le médiateur de la consommation, soit par voie électronique à : **<EMAIL>** soit par courrier postal à l’adresse suivante : **VIVONS MIEUX ENSEMBLE – 465 avenue de la Libération - 54000 NANCY.**\n\n'
    },
    {
      id: 'faculte-de-retractation-du-mandant-conditions-delais-et-modalites-d-exercice-',
      children: [
        {
          id: 'faculte-de-retractation-du-mandant-conditions-delais-et-modalites-d-exercice--texte',
          content:
            '## FACULTÉ DE RÉTRACTATION DU MANDANT (CONDITIONS, DÉLAIS ET MODALITES D’EXERCICE) : \nEn application de l’article L. 221-18 du code de la consommation, le mandant dispose d’un délai de rétractation de quatorze jours sans avoir à motiver sa décision pour renoncer au présent mandat. Ce délai court à compter de la conclusion du présent mandat, à savoir au lendemain de sa ratification et prend fin à minuit du dernier jour du délai. Si ce dernier jour est un samedi, dimanche ou jour férié/chômé, le délai est prorogé jusqu’au premier jour ouvrable suivant. Si les informations relatives au droit de rétractation n’ont pas été fournies au mandant dans les conditions prévues au 2 de l’article L. 221-5 du code de la consommation, ce délai de rétractation est prolongé de douze mois (art. L 221-20 du code de la consommation). Toutefois, lorsque la délivrance de ces informations intervient pendant cette prolongation, le délai de rétractation expire au terme d’une période de quatorze jours à compter du jour où le mandant a reçu ces informations. Le mandant informe le mandataire de sa décision de rétractation en lui adressant, avant l’expiration du délai de rétractation, le formulaire de rétractation ci-dessous ou toute autre déclaration, dénuée d’ambiguïté, exprimant sa volonté de se rétracter. La charge de la preuve de l’exercice du droit de rétractation pèse sur le mandant.\n**Si le mandant souhaite que l’exécution du présent mandat commence avant la fin du délai de rétractation, le mandataire doit recueillir sa demande expresse sur papier ou sur support durable. Dans ce cas et à condition que le mandant ait préalablement et expressément renoncé à son droit de rétractation, ce droit ne pourra pas être exercé si le mandat est pleinement exécuté avant la fin du délai de rétractation.**\n'
        },
        {
          id: 'faculte-de-retractation-du-mandant-conditions-delais-et-modalites-d-exercice--saut-de-ligne',
          enhancedVariable: {
            type: 'lineBreak',
            parameters: {
              number: 1
            }
          }
        },
        {
          id: 'faculte-de-retractation-du-mandant-conditions-delais-et-modalites-d-exercice--texte-1',
          content:
            '\nCela rappelé, les propriétaires **autorisent expressement** le mandataire à commencer à exécuter le présent mandat avant l’expiration du délai de rétractation susvisé.\n\n'
        },
        {
          id: 'faculte-de-retractation-du-mandant-conditions-delais-et-modalites-d-exercice--saut-de-ligne-1',
          enhancedVariable: {
            type: 'lineBreak',
            parameters: {
              number: 1
            }
          }
        },
        {
          id: 'faculte-de-retractation-du-mandant-conditions-delais-et-modalites-d-exercice--si-signature-electronique',
          condition: 'SIGNATURE_ELECTRONIQUE',
          content:
            "\nDocument signé électroniquement par l’ensemble des parties, chacune d’elles en conservant un exemplaire original sur un support durable garantissant l'intégrité de l'acte. \n",
          prerequisites: {
            conditions: {
              SIGNATURE_ELECTRONIQUE: true
            }
          }
        }
      ]
    },
    {
      id: 'si-non-signature-electronique',
      condition: '!SIGNATURE_ELECTRONIQUE',
      children: [
        {
          id: 'si-non-signature-electronique-texte',
          content:
            '## Signatures\nMots nuls \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ \nLignes nulles \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ \nChiffres nuls \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ \n\nFait à \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_  en double exemplaire, dont un est remis au propriétaire qui le reconnaît.\n\nLe \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_ \n\n**LE MANDANT, LE(S) PROPRIÉTAIRE(S)**\nSignature précédée de la mention manuscrite :\n_« Bon pour mandat {{ MANDAT_TYPE }} »_\n',
          prerequisites: {
            variables: {
              MANDAT_TYPE: true
            }
          }
        },
        {
          id: 'si-non-signature-electronique-saut-de-ligne',
          enhancedVariable: {
            type: 'lineBreak',
            parameters: {
              number: 3
            }
          }
        },
        {
          id: 'si-non-signature-electronique-texte-1',
          content:
            '**LE MANDATAIRE, représenté par  \\_\\_\\_\\_\\_\\_\\_\\_\\_\\_\\_**\nSignature précédée de la mention manuscrite :\n_« Mandat accepté »_\n'
        }
      ],
      prerequisites: {
        conditions: {
          SIGNATURE_ELECTRONIQUE: true
        }
      }
    }
  ],
  mapping: {
    conditions: {
      TEMPLATE_PP: {
        value: "_.isOperationTemplate(['PROPRIETES_PRIVEES', 'IMMOBILIER'])",
        dependencies: []
      },
      BAREME_PP_NOUVEAU: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].bareme_pp_statut_triple.value === 'bareme'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'bareme_pp_statut_triple'
          }
        ]
      },
      BAREME_PP_ANCIEN: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].bareme_pp_statut.value === 'oui'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'bareme_pp_statut'
          }
        ]
      },
      PRESENCE_BIEN: {
        value: '_.size(RAWS.BIENS) > 0',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS'
          }
        ]
      },
      MANDAT_LIBRE: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_statut_location.value === 'statut_location_libre'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_statut_location'
          }
        ]
      },
      MANDAT_LOUE: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_statut_location.value === 'statut_location_loue'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_statut_location'
          }
        ]
      },
      MANDAT_SEMI: {
        value: "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_type.value === 'semi'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_type'
          }
        ]
      },
      MANDAT_SIMPLE: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_type.value === 'simple'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_type'
          }
        ]
      },
      MANDAT_EXCLUSIF: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_type.value === 'exclusif'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_type'
          }
        ]
      },
      MANDAT_SUCCESS: {
        value: '',
        dependencies: []
      },
      'MANDAT.CHARGE_VENDEUR': {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_honoraires_charge_simple.value === 'vendeur'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_honoraires_charge_simple'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.CONDITIONS_GENERALES[0].id',
            questionId: 'prix_vente_total'
          }
        ]
      },
      'MANDAT.CHARGE_ACQUEREUR': {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_honoraires_charge_simple.value === 'acquereur'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_honoraires_charge_simple'
          }
        ]
      },
      MANDAT_TROIS: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].pp_mandat_duree.value === '3'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'pp_mandat_duree'
          }
        ]
      },
      MANDAT_SIX: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].pp_mandat_duree.value === '6'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'pp_mandat_duree'
          }
        ]
      },
      MANDAT_NEUF: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].pp_mandat_duree.value === '9'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'pp_mandat_duree'
          }
        ]
      },
      CLAUSE_PARTICULIERE: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].clause_particuliere.value === 'oui'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'clause_particuliere'
          }
        ]
      },
      CLAUSE_PARTICULIERE_VALUE: {
        value: 'answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].clause_particuliere.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'clause_particuliere'
          }
        ]
      },
      SIGNATURE_ELECTRONIQUE: {
        value:
          "answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_signature_electronique.value === 'oui'",
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_signature_electronique'
          }
        ]
      }
    },
    variables: {
      MANDAT_TYPE: {
        value:
          '({simple:"SIMPLE",semi:"SEMI-EXCLUSIF",exclusif:"EXCLUSIF",succes:"SUCCES"})[answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_type.value]',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_type'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_honoraires_montant'
          }
        ]
      },
      'BOX.UNCHECKED': {
        value: '_.getVisualCheckBoxUnchecked()',
        dependencies: []
      },
      'PRIX.VENTE': {
        value:
          '_.formatPriceToNumberAndLetter(answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.CONDITIONS_GENERALES[0].id].prix_vente_total.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.CONDITIONS_GENERALES[0].id',
            questionId: 'prix_vente_total'
          }
        ]
      },
      HONORAIRES_TOTAL: {
        value:
          '_.formatPriceToNumberAndLetter(answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_honoraires_montant.value)',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_honoraires_montant'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'bareme_pp_2022_2'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'bareme_pp_2025'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'formulaire_retractation_pp'
          },
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_etat_locatif'
          }
        ]
      },
      HONORAIRES_ET_PRIX: {
        value: '_.formatPriceToNumberAndLetter(RAWS.PRIX_ET_HONORAIRE)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PRIX_ET_HONORAIRE'
          }
        ]
      }
    },
    repeats: {
      COPROPRIETES: {
        value: 'RAWS.COPROPRIETES',
        item: 'COPROPRIETE',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'COPROPRIETES'
          }
        ],
        mapping: {
          conditions: {},
          variables: {
            KEY: {
              value: 'COPROPRIETE.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            }
          },
          repeats: {
            BIENS: {
              value: 'branches[COPROPRIETE.id].IMMEUBLES',
              item: 'BIEN',
              dependencies: [],
              mapping: {
                conditions: {
                  TYPE_AUTRE: {
                    value: "answers[BIEN.id].nature_bien.value === 'nature_bien_autre'",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIEN.id',
                        questionId: 'nature_bien'
                      }
                    ]
                  },
                  DESIGNATION_VALUE: {
                    value: 'answers[BIEN.id].designation.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIEN.id',
                        questionId: 'designation'
                      }
                    ]
                  }
                },
                variables: {
                  KEY: {
                    value: 'BIEN.id',
                    dependencies: []
                  },
                  NAME: {
                    value: '',
                    dependencies: []
                  },
                  ORDER: {
                    value: 'RAWS.NUMEROS_BIENS[BIEN.id]',
                    dependencies: [
                      {
                        type: 'RAW',
                        rawId: 'NUMEROS_BIENS'
                      }
                    ]
                  },
                  TYPE: {
                    value:
                      '({nature_bien_cave:"Cave",nature_bien_cellier:"Cellier",nature_bien_parking:"Parking",nature_bien_garage:"Garage",nature_bien_local:"Local commercial ou professionnel",nature_bien_autre:"Autre",nature_bien_appartement:"Appartement",nature_bien_duplex:"Duplex",nature_bien_triplex:"Triplex",nature_bien_maison:"Maison en copropriété"})[answers[BIEN.id].nature_bien.value]',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIEN.id',
                        questionId: 'nature_bien'
                      }
                    ]
                  },
                  NUMERO_LOT_COPROPRIETE: {
                    value: 'answers[BIEN.id].numero_lot.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIEN.id',
                        questionId: 'numero_lot'
                      }
                    ]
                  },
                  TYPE_AUTRE: {
                    value: 'answers[BIEN.id].nature_bien_autre.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIEN.id',
                        questionId: 'nature_bien_autre'
                      }
                    ]
                  },
                  DESIGNATION: {
                    value: 'answers[BIEN.id].designation.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIEN.id',
                        questionId: 'designation'
                      }
                    ]
                  },
                  MESURAGE_CARREZ: {
                    value: '_.formatNumber(answers[BIEN.id].mesurage_carrez_superficie.value)',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIEN.id',
                        questionId: 'mesurage_carrez_superficie'
                      }
                    ]
                  },
                  ADRESSE: {
                    value: 'answers[BIEN.id].adresse.value.formattedAddress',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIEN.id',
                        questionId: 'adresse'
                      }
                    ]
                  }
                },
                repeats: {
                  ANNEXES: {
                    value: 'RAWS.ANNEXES',
                    item: 'ANNEXES',
                    dependencies: [
                      {
                        type: 'RAW',
                        rawId: 'ANNEXES'
                      }
                    ],
                    mapping: {
                      conditions: {
                        TYPE_AUTRE: {
                          value: "answers[ANNEXES.id].nature_bien.value === 'nature_bien_autre'",
                          dependencies: [
                            {
                              type: 'RECORD_QUESTION',
                              recordIdSource: 'ANNEXES.id',
                              questionId: 'nature_bien'
                            }
                          ]
                        }
                      },
                      variables: {
                        KEY: {
                          value: 'ANNEXES.id',
                          dependencies: []
                        },
                        NAME: {
                          value: '',
                          dependencies: []
                        },
                        ORDER: {
                          value: '',
                          dependencies: []
                        },
                        TYPE: {
                          value:
                            '({nature_bien_appartement:"Un Appartement",nature_bien_duplex:"Un Duplex",nature_bien_triplex:"Un Triplex",nature_bien_maison:"Une maison en copropriété",nature_bien_cave:"Une cave",nature_bien_cellier:"Un cellier",nature_bien_parking:"Un parking",nature_bien_garage:"Un garage",nature_bien_local:"Un Local professionnel",nature_bien_autre:"Autre"})[answers[ANNEXES.id].nature_bien.value]',
                          dependencies: [
                            {
                              type: 'RECORD_QUESTION',
                              recordIdSource: 'ANNEXES.id',
                              questionId: 'nature_bien'
                            }
                          ]
                        },
                        NUMERO: {
                          value: 'answers[ANNEXES.id].numero_lot.value',
                          dependencies: [
                            {
                              type: 'RECORD_QUESTION',
                              recordIdSource: 'ANNEXES.id',
                              questionId: 'numero_lot'
                            }
                          ]
                        },
                        TYPE_AUTRE: {
                          value: 'answers[ANNEXES.id].nature_bien_autre.value',
                          dependencies: [
                            {
                              type: 'RECORD_QUESTION',
                              recordIdSource: 'ANNEXES.id',
                              questionId: 'nature_bien_autre'
                            }
                          ]
                        },
                        DESIGNATION: {
                          value: 'answers[ANNEXES.id].designation.value',
                          dependencies: [
                            {
                              type: 'RECORD_QUESTION',
                              recordIdSource: 'ANNEXES.id',
                              questionId: 'designation'
                            }
                          ]
                        }
                      },
                      repeats: {},
                      raws: []
                    }
                  }
                },
                raws: [
                  {
                    id: 'BRANCHE_BIEN',
                    value: 'branchesRecord[BIEN.id].BIEN_VENDU[0]',
                    dependencies: []
                  },
                  {
                    id: 'BIENS',
                    value: 'branches[COPROPRIETE.id].IMMEUBLES',
                    dependencies: []
                  },
                  {
                    id: 'ANNEXES',
                    value: 'branches[BIEN.id].LOT_ANNEXE',
                    dependencies: []
                  },
                  {
                    id: 'LOTS_HABITATION',
                    value: "_.uniqBy(_.filterRecords(RAWS.BIENS, [['BIEN', 'LOT_HABITATION']]), 'id')",
                    dependencies: [
                      {
                        type: 'RAW',
                        rawId: 'COPROPRIETES_CONTENT'
                      }
                    ]
                  },
                  {
                    id: 'ADDRESSE',
                    value: 'answers[COPROPRIETE.id].adresse.value.formattedAddress',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'COPROPRIETE.id',
                        questionId: 'adresse'
                      }
                    ]
                  }
                ]
              }
            }
          },
          raws: [
            {
              id: 'EDD_LISTE',
              value: "_.getRepeatValues(answers[COPROPRIETE.id], 'reglement_list_reglement')",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'COPROPRIETE.id',
                  questionId: 'reglement_list_reglement'
                }
              ]
            },
            {
              id: 'EDD',
              value: "_.orderBy(RAWS.EDD_LISTE, 'date.value')",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'COPROPRIETE.id',
                  questionId: 'reglement_list_reglement'
                }
              ]
            },
            {
              id: 'PREMIER_EDD',
              value: 'RAWS.EDD[0]',
              dependencies: [
                {
                  type: 'RAW',
                  rawId: 'EDD'
                }
              ]
            },
            {
              id: 'EDD_LISTE_MODIFICATIF',
              value: '_.drop(RAWS.EDD, 1)',
              dependencies: [
                {
                  type: 'RAW',
                  rawId: 'EDD'
                }
              ]
            },
            {
              id: 'EDDV_LISTE',
              value: "_.getRepeatValues(answers[COPROPRIETE.id], 'eddv_list')",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'COPROPRIETE.id',
                  questionId: 'eddv_list'
                }
              ]
            },
            {
              id: 'EDDV',
              value: "_.orderBy(RAWS.EDDV_LISTE, 'date.value')",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'COPROPRIETE.id',
                  questionId: 'eddv_list'
                }
              ]
            },
            {
              id: 'PREMIER_EDDV',
              value: 'RAWS.EDDV[0]',
              dependencies: [
                {
                  type: 'RAW',
                  rawId: 'EDDV'
                }
              ]
            },
            {
              id: 'EDDV_LISTE_MODIFICATIF',
              value: '_.drop(RAWS.EDDV, 1)',
              dependencies: [
                {
                  type: 'RAW',
                  rawId: 'EDDV'
                }
              ]
            },
            {
              id: 'DATE_CONSTRUCTION',
              value: 'answers[COPROPRIETE.id].construction.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'COPROPRIETE.id',
                  questionId: 'construction'
                }
              ]
            }
          ]
        }
      },
      BIEN_DETACHES_HABITATION: {
        value: 'RAWS.BIENS_HABITATION',
        item: 'BIENS_HABITATION',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS_HABITATION'
          }
        ],
        mapping: {
          conditions: {
            TYPE_AUTRE: {
              value: "answers[BIENS_HABITATION.id].nature_bien.value === 'nature_bien_autre'",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_HABITATION.id',
                  questionId: 'nature_bien'
                }
              ]
            },
            DESIGNATION_VALUE: {
              value: 'answers[BIENS_HABITATION.id].designation.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_HABITATION.id',
                  questionId: 'designation'
                }
              ]
            }
          },
          variables: {
            KEY: {
              value: 'BIENS_HABITATION.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            TYPE: {
              value:
                '({nature_maison:"Maison individuelle",nature_mitoyen:"Maison mitoyenne",nature_immeuble:"Immeuble entier",nature_bien_autre:"Autre"})[answers[BIENS_HABITATION.id].nature_bien.value]',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_HABITATION.id',
                  questionId: 'nature_bien'
                }
              ]
            },
            TYPE_AUTRE: {
              value: 'answers[BIENS_HABITATION.id].nature_bien_autre.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_HABITATION.id',
                  questionId: 'nature_bien_autre'
                }
              ]
            },
            ADRESSE: {
              value: 'answers[BIENS_HABITATION.id].adresse.value.formattedAddress',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_HABITATION.id',
                  questionId: 'adresse'
                }
              ]
            },
            DESIGNATION: {
              value: 'answers[BIENS_HABITATION.id].designation.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_HABITATION.id',
                  questionId: 'designation'
                }
              ]
            }
          },
          repeats: {
            CADASTRE_PARCELLES: {
              value: "_.getRepeatValues(answers[BIENS_HABITATION.id], 'cadastre_parcelles')",
              item: 'PARCELLES',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_HABITATION.id',
                  questionId: 'cadastre_parcelles'
                }
              ],
              mapping: {
                conditions: {},
                variables: {
                  KEY: {
                    value: 'PARCELLES.$key.value',
                    dependencies: []
                  },
                  NAME: {
                    value: '',
                    dependencies: []
                  },
                  ORDER: {
                    value: '',
                    dependencies: []
                  },
                  SECTION: {
                    value: 'PARCELLES.section.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_section'
                      }
                    ]
                  },
                  NUMERO: {
                    value: 'PARCELLES.parcelle.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_parcelle'
                      }
                    ]
                  },
                  H: {
                    value: "_.formatNumber(PARCELLES.h.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_h'
                      }
                    ]
                  },
                  A: {
                    value: "_.formatNumber(PARCELLES.a.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_a'
                      }
                    ]
                  },
                  CA: {
                    value: "_.formatNumber(PARCELLES.c.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_c'
                      }
                    ]
                  }
                },
                repeats: {},
                raws: []
              }
            }
          },
          raws: []
        }
      },
      BIEN_DETACHES_NON_HABITATION: {
        value: 'RAWS.BIENS_NON_HABITATION',
        item: 'BIENS_NON_HABITATION',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS_NON_HABITATION'
          }
        ],
        mapping: {
          conditions: {
            TYPE_AUTRE: {
              value: "answers[BIENS_NON_HABITATION.id].nature_bien.value === 'nature_bien_autre'",
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_HABITATION.id',
                  questionId: 'nature_bien'
                }
              ]
            },
            DESIGNATION_VALUE: {
              value: 'answers[BIENS_NON_HABITATION.id].designation.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_HABITATION.id',
                  questionId: 'designation'
                }
              ]
            }
          },
          variables: {
            KEY: {
              value: 'BIENS_NON_HABITATION.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            TYPE: {
              value:
                '({nature_bien_immeuble:"Immeuble Entier",nature_bien_commercial:"Bien commercial ou professionnel",nature_garage:"Garage",nature_bien_autre:"Autre"})[answers[BIENS_NON_HABITATION.id].nature_bien.value]',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_HABITATION.id',
                  questionId: 'nature_bien'
                }
              ]
            },
            TYPE_AUTRE: {
              value: 'answers[BIENS_NON_HABITATION.id].nature_bien_autre.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_HABITATION.id',
                  questionId: 'nature_bien_autre'
                }
              ]
            },
            ADRESSE: {
              value: 'answers[BIENS_NON_HABITATION.id].adresse.value.formattedAddress',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_HABITATION.id',
                  questionId: 'adresse'
                }
              ]
            },
            DESIGNATION: {
              value: 'answers[BIENS_NON_HABITATION.id].designation.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_HABITATION.id',
                  questionId: 'designation'
                }
              ]
            }
          },
          repeats: {
            CADASTRE_PARCELLES: {
              value: "_.getRepeatValues(answers[BIENS_NON_HABITATION.id], 'cadastre_parcelles')",
              item: 'PARCELLES',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_HABITATION.id',
                  questionId: 'cadastre_parcelles'
                }
              ],
              mapping: {
                conditions: {},
                variables: {
                  KEY: {
                    value: 'PARCELLES.$key.value',
                    dependencies: []
                  },
                  NAME: {
                    value: '',
                    dependencies: []
                  },
                  ORDER: {
                    value: '',
                    dependencies: []
                  },
                  SECTION: {
                    value: 'PARCELLES.section.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_section'
                      }
                    ]
                  },
                  NUMERO: {
                    value: 'PARCELLES.parcelle.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_parcelle'
                      }
                    ]
                  },
                  H: {
                    value: "_.formatNumber(PARCELLES.h.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_h'
                      }
                    ]
                  },
                  A: {
                    value: "_.formatNumber(PARCELLES.a.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_a'
                      }
                    ]
                  },
                  CA: {
                    value: "_.formatNumber(PARCELLES.c.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_c'
                      }
                    ]
                  }
                },
                repeats: {},
                raws: []
              }
            }
          },
          raws: []
        }
      },
      BIEN_TAB: {
        value: 'RAWS.BIENS_TAB',
        item: 'BIENS_TAB',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS_TAB'
          }
        ],
        mapping: {
          conditions: {
            DESIGNATION_VALUE: {
              value: 'answers[BIENS_TAB.id].designation.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_TAB.id',
                  questionId: 'designation'
                }
              ]
            }
          },
          variables: {
            KEY: {
              value: 'BIENS_TAB.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            SUPERFICIE: {
              value: '_.formatNumber(answers[BIENS_TAB.id].programme_superficie.value)',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_TAB.id',
                  questionId: 'programme_superficie'
                }
              ]
            },
            ADRESSE: {
              value: 'answers[BIENS_TAB.id].adresse.value.formattedAddress',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_TAB.id',
                  questionId: 'adresse'
                }
              ]
            },
            DESIGNATION: {
              value: 'answers[BIENS_TAB.id].designation.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_TAB.id',
                  questionId: 'designation'
                }
              ]
            }
          },
          repeats: {
            CADASTRE_PARCELLES: {
              value: "_.getRepeatValues(answers[BIENS_TAB.id], 'cadastre_parcelles')",
              item: 'PARCELLES',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_TAB.id',
                  questionId: 'cadastre_parcelles'
                }
              ],
              mapping: {
                conditions: {},
                variables: {
                  KEY: {
                    value: 'PARCELLES.$key.value',
                    dependencies: []
                  },
                  NAME: {
                    value: '',
                    dependencies: []
                  },
                  ORDER: {
                    value: '',
                    dependencies: []
                  },
                  SECTION: {
                    value: 'PARCELLES.section.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_section'
                      }
                    ]
                  },
                  NUMERO: {
                    value: 'PARCELLES.parcelle.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_parcelle'
                      }
                    ]
                  },
                  H: {
                    value: "_.formatNumber(PARCELLES.h.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_h'
                      }
                    ]
                  },
                  A: {
                    value: "_.formatNumber(PARCELLES.a.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_a'
                      }
                    ]
                  },
                  CA: {
                    value: "_.formatNumber(PARCELLES.c.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        questionId: 'cadastre_parcelles_c'
                      }
                    ]
                  }
                },
                repeats: {},
                raws: [
                  {
                    id: 'PREFIXE',
                    value: 'PARCELLES.prefixe.value',
                    dependencies: []
                  }
                ]
              }
            }
          },
          raws: []
        }
      },
      BIEN_NON_TAB: {
        value: 'RAWS.BIENS_NON_TAB',
        item: 'BIENS_NON_TAB',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS_NON_TAB'
          }
        ],
        mapping: {
          conditions: {
            DESIGNATION_VALUE: {
              value: 'answers[BIENS_NON_TAB.id].designation.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_TAB.id',
                  questionId: 'designation'
                }
              ]
            }
          },
          variables: {
            KEY: {
              value: 'BIENS_NON_TAB.id',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            ADRESSE: {
              value: 'answers[BIENS_NON_TAB.id].adresse.value.formattedAddress',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_TAB.id',
                  questionId: 'adresse'
                }
              ]
            },
            DESIGNATION: {
              value: 'answers[BIENS_NON_TAB.id].designation.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_TAB.id',
                  questionId: 'designation'
                }
              ]
            }
          },
          repeats: {
            CADASTRE_PARCELLES: {
              value: "_.getRepeatValues(answers[BIENS_NON_TAB.id], 'cadastre_parcelles')",
              item: 'PARCELLES',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  recordIdSource: 'BIENS_NON_TAB.id',
                  questionId: 'cadastre_parcelles'
                }
              ],
              mapping: {
                conditions: {},
                variables: {
                  KEY: {
                    value: 'PARCELLES.$key.value',
                    dependencies: []
                  },
                  NAME: {
                    value: '',
                    dependencies: []
                  },
                  ORDER: {
                    value: '',
                    dependencies: []
                  },
                  SECTION: {
                    value: 'PARCELLES.section.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIENS_NON_TAB.id',
                        questionId: 'cadastre_parcelles_section'
                      }
                    ]
                  },
                  NUMERO: {
                    value: 'PARCELLES.parcelle.value',
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIENS_NON_TAB.id',
                        questionId: 'cadastre_parcelles_parcelle'
                      }
                    ]
                  },
                  H: {
                    value: "_.formatNumber(PARCELLES.h.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIENS_NON_TAB.id',
                        questionId: 'cadastre_parcelles_h'
                      }
                    ]
                  },
                  A: {
                    value: "_.formatNumber(PARCELLES.a.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIENS_NON_TAB.id',
                        questionId: 'cadastre_parcelles_a'
                      }
                    ]
                  },
                  CA: {
                    value: "_.formatNumber(PARCELLES.c.value).padStart(2,'0')",
                    dependencies: [
                      {
                        type: 'RECORD_QUESTION',
                        recordIdSource: 'BIENS_NON_TAB.id',
                        questionId: 'cadastre_parcelles_c'
                      }
                    ]
                  }
                },
                repeats: {},
                raws: [
                  {
                    id: 'PREFIXE',
                    value: 'PARCELLES.prefixe.value',
                    dependencies: []
                  }
                ]
              }
            }
          },
          raws: []
        }
      },
      CLAUSE_PARTICULIERES: {
        value:
          "_.getRepeatValues(answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id], 'clause_particuliere_liste')",
        item: 'CLAUSE_PARTICULIERE',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'clause_particuliere_liste'
          }
        ],
        mapping: {
          conditions: {},
          variables: {
            KEY: {
              value: 'CLAUSE_PARTICULIERE.$key.value',
              dependencies: []
            },
            NAME: {
              value: '',
              dependencies: []
            },
            ORDER: {
              value: '',
              dependencies: []
            },
            CONTENU: {
              value: 'CLAUSE_PARTICULIERE.clause_particuliere_liste_contenu.value',
              dependencies: [
                {
                  type: 'RECORD_QUESTION',
                  questionId: 'clause_particuliere_liste_clause_particuliere_liste_contenu'
                }
              ]
            }
          },
          repeats: {},
          raws: []
        }
      }
    },
    raws: [
      {
        id: 'BIENS',
        value: 'records.OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO.BIEN_VENDU',
        dependencies: []
      },
      {
        id: 'COPROPRIETES_CONTENT',
        value:
          "_.getRecordsBranches(records.OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO.BIEN_VENDU, 'CONTENU')",
        dependencies: []
      },
      {
        id: 'COPROPRIETES',
        value: '_.getCopros(records.OPERATION__IMMOBILIER__VENTE__BIENS_VENDUS_SANS_MONO.BIEN_VENDU)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'COPROPRIETES_CONTENT'
          }
        ]
      },
      {
        id: 'NUMEROS_BIENS',
        value: "_.add(_.mapIdx(RAWS.BIENS, ['id']), 1)",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS'
          }
        ]
      },
      {
        id: 'BIENS_HABITATION',
        value: "_.uniqBy(_.filterRecords(RAWS.BIENS, [['BIEN', 'INDIVIDUEL_HABITATION']]), 'id')",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS'
          }
        ]
      },
      {
        id: 'BIENS_TAB',
        value: "_.uniqBy(_.filterRecords(RAWS.BIENS, [['BIEN', 'TERRAIN_CONSTRUCTIBLE']]), 'id')",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS'
          }
        ]
      },
      {
        id: 'BIENS_NON_TAB',
        value: "_.uniqBy(_.filterRecords(RAWS.BIENS, [['BIEN', 'TERRAIN_NON_CONSTRUCTIBLE']]), 'id')",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS'
          }
        ]
      },
      {
        id: 'BIENS_NON_HABITATION',
        value: "_.uniqBy(_.filterRecords(RAWS.BIENS, [['BIEN', 'INDIVIDUEL_HORS_HABITATION']]), 'id')",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS'
          }
        ]
      },
      {
        id: 'BIENS_DETACHES',
        value: '_.concat(RAWS.BIENS_HABITATION,RAWS.BIENS_NON_HABITATION)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS_HABITATION'
          },
          {
            type: 'RAW',
            rawId: 'BIENS_NON_HABITATION'
          }
        ]
      },
      {
        id: 'BIENS_TERRAINS',
        value: '_.concat(RAWS.BIENS_NON_TAB,RAWS.BIENS_TAB)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS_NON_TAB'
          },
          {
            type: 'RAW',
            rawId: 'BIENS_TAB'
          }
        ]
      },
      {
        id: 'BIENS_MONO_HABITATION',
        value: "_.uniqBy(_.filterRecords(RAWS.BIENS, [['BIEN', 'MONOPROPRIETE_HABITATION']]), 'id')",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS'
          }
        ]
      },
      {
        id: 'BIENS_MONO_HORS_HABITATION',
        value: "_.uniqBy(_.filterRecords(RAWS.BIENS, [['BIEN', 'MONOPROPRIETE_HORS_HABITATION']]), 'id')",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS'
          }
        ]
      },
      {
        id: 'BIENS_MONO_TOTAL',
        value: '_.concat(RAWS.BIENS_MONO_HABITATION,RAWS.BIENS_MONO_HORS_HABITATION)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS_MONO_HABITATION'
          },
          {
            type: 'RAW',
            rawId: 'BIENS_MONO_HORS_HABITATION'
          }
        ]
      },
      {
        id: 'MONOPROPRIETE',
        value: "_.uniqBy(_.filterRecords(RAWS.COPROPRIETES_CONTENT, [['STRUCTURE', 'ENSEMBLE_IMMOBILIER']]), 'id')",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'COPROPRIETES_CONTENT'
          }
        ]
      },
      {
        id: 'AGENCE',
        value: 'records.OPERATION__IMMOBILIER__VENTE__AGENTS.AGENT_IMMOBILIER',
        dependencies: []
      },
      {
        id: 'CONSEILLER',
        value: 'records.OPERATION__IMMOBILIER__VENTE__MANDATAIRES.MANDATAIRE',
        dependencies: []
      },
      {
        id: 'TOTAL_INTERMEDIAIRE',
        value: '_.concat(RAWS.AGENCE,RAWS.CONSEILLER)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'AGENCE'
          },
          {
            type: 'RAW',
            rawId: 'CONSEILLER'
          }
        ]
      },
      {
        id: 'NUMERO_AGENT',
        value: "_.add(_.mapIdx(RAWS.AGENCE, ['id']), 1)",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'AGENCE'
          }
        ]
      },
      {
        id: 'LOT_HABITATION',
        value: "_.uniqBy(_.filterRecords(RAWS.BIENS, [['BIEN', 'LOT_HABITATION']]), 'id')",
        dependencies: [
          {
            type: 'RAW',
            rawId: 'BIENS'
          }
        ]
      },
      {
        id: 'TOTAL_HABITATION',
        value: '_.concat(RAWS.LOT_HABITATION,RAWS.BIENS_MONO_HABITATION,RAWS.BIENS_HABITATION)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'LOT_HABITATION'
          },
          {
            type: 'RAW',
            rawId: 'BIENS_MONO_HABITATION'
          },
          {
            type: 'RAW',
            rawId: 'BIENS_HABITATION'
          }
        ]
      },
      {
        id: 'TYPE_MANDAT',
        value: 'answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_type.value',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_type'
          }
        ]
      },
      {
        id: 'PRIX_VENTE',
        value:
          'answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.CONDITIONS_GENERALES[0].id].prix_vente_total.value || 0',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.CONDITIONS_GENERALES[0].id',
            questionId: 'prix_vente_total'
          }
        ]
      },
      {
        id: 'MANDANT',
        value: 'records.OPERATION__IMMOBILIER__VENTE__VENDEURS.VENDEUR',
        dependencies: []
      },
      {
        id: 'HONORAIRES_TOTAL',
        value:
          'answers[records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id].mandat_honoraires_montant.value || 0',
        dependencies: [
          {
            type: 'RECORD_QUESTION',
            recordIdSource: 'records.OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES.MANDAT[0].id',
            questionId: 'mandat_honoraires_montant'
          }
        ]
      },
      {
        id: 'PRIX_ET_HONORAIRE',
        value: '(RAWS.PRIX_VENTE || 0) + (RAWS.HONORAIRES_TOTAL || 0)',
        dependencies: [
          {
            type: 'RAW',
            rawId: 'PRIX_VENTE'
          },
          {
            type: 'RAW',
            rawId: 'HONORAIRES_TOTAL'
          }
        ]
      }
    ]
  },
  metadata: {}
};
