/**
 * API Teleactes
 *
 * 
 *
 * NOTE: This class is auto generated by OpenAPI Generator (https://openapi-generator.tech).
 * https://openapi-generator.tech
 * Do not edit the class manually.
 */
import { PersonneTeleactesPersonneMoraleDto } from './personne-teleactes-personne-morale-dto';
import { PersonneTeleactesPersonnePhysiqueDto } from './personne-teleactes-personne-physique-dto';


export interface PersonneTeleactesDto { 
    /**
     * id du LegalRecord correspondant à la personne
     */
    idPortalys?: string;
    /**
     * type de personne (physique/morale)
     */
    type?: PersonneTeleactesDtoTypeEnum;
    personnePhysique?: PersonneTeleactesPersonnePhysiqueDto;
    personneMorale?: PersonneTeleactesPersonneMoraleDto;
}
export enum PersonneTeleactesDtoTypeEnum {
    PHYSIQUE = 'PHYSIQUE',
    MORALE = 'MORALE'
};



