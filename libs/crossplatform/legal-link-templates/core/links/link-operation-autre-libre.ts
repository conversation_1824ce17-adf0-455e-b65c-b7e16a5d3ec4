// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalLinkTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const LinkOperationAutreLibre: LegalLinkTemplate = {
  config: {
    display: {
      label: 'Fiche libre',
      labelPlural: 'Fiches libres',
      labelWithArticle: 'une fiche libre',
      branches: {
        INFORMATIONS_LIBRES: {
          label: 'Informations libres',
          labelWithArticle: 'des Informations libres'
        }
      }
    },
    branches: {
      INFORMATIONS_LIBRES: {
        type: 'INFORMATIONS_LIBRES',
        reverseType: 'INFORMATIONS_LIBRES',
        to: {
          type: 'RECORD',
          specificTypes: [['OPERATION', 'AUTRE', 'LIBRE']]
        },
        constraints: {
          min: 1,
          max: 1
        },
        creation: {
          autoCreate: true,
          autoCreateOnly: true
        }
      }
    }
  },
  id: 'LINK__OPERATION__AUTRE_LIBRE',
  label: 'Opération',
  mynotaryTemplate: false,
  originTemplate: 'LINK__OPERATION__AUTRE_LIBRE',
  specificTypes: ['OPERATION', 'AUTRE_LIBRE'],
  type: 'LINK'
};
