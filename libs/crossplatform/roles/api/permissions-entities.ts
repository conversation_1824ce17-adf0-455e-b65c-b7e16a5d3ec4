/**
 * This file is auto generated by legal-templates-typescript-converter.ts
 * DO NOT EDIT THIS FILE MANUALLY
 */

/* eslint-disable typescript-sort-keys/string-enum */
export enum EntityType {
  DATA_TRANSFERT = 'DATA_TRANSFERT',
  DOCUMENT = 'DOCUMENT',
  INTERCONNECTION = 'INTERCONNECTION',
  INVOICES = 'INVOICES',
  MANAGEMENT_REGISTER = 'MANAGEMENT_REGISTER',
  ORGANIZATION = 'ORGANIZATION',
  RECEIVERSHIP_REGISTER = 'RECEIVERSHIP_REGISTER',
  TRANSACTION_REGISTER = 'TRANSACTION_REGISTER',
  PL_ETAT_CIVIL = 'PL_ETAT_CIVIL',

  /**
   * The list below match the list of all operations that can be created on the platform.
   * This value is compute from operation template specific type concatenated with the '__' as separator.
   */
  AGENCE_DIRECTE__IMMOBILIER = 'AGENCE_DIRECTE__IMMOBILIER',
  AJP__DOSSIER_DE_LOCATION_AJP = 'AJP__DOSSIER_DE_LOCATION_AJP',
  AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP = 'AJP__DOSSIER_DE_LOCATION_BIEN_PROFESSIONNEL_AJP',
  AJP__DOSSIER_DE_VENTE_AJP = 'AJP__DOSSIER_DE_VENTE_AJP',
  AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP = 'AJP__DOSSIER_DE_VENTE_BIEN_PROFESSIONNEL_AJP',
  ALLOWA__IMMOBILIER = 'ALLOWA__IMMOBILIER',
  AQUITANIS__PROGRAMME_ANCIEN = 'AQUITANIS__PROGRAMME_ANCIEN',
  AQUITANIS__PROGRAMME_BRS = 'AQUITANIS__PROGRAMME_BRS',
  AQUITANIS__RESERVATION_BRS = 'AQUITANIS__RESERVATION_BRS',
  AQUITANIS__VENTE_ANCIEN = 'AQUITANIS__VENTE_ANCIEN',
  AUTRE__LIBRE = 'AUTRE__LIBRE',
  AXO_ACTIF__IMMOBILIER__HABITATION = 'AXO_ACTIF__IMMOBILIER__HABITATION',
  BEAUX_VILLAGES__IMMOBILIER = 'BEAUX_VILLAGES__IMMOBILIER',
  BENEDIC__DOSSIER_DE_LOCATION_BENEDIC = 'BENEDIC__DOSSIER_DE_LOCATION_BENEDIC',
  BENEDIC__IMMOBILIER = 'BENEDIC__IMMOBILIER',
  BLOT__IMMOBILIER = 'BLOT__IMMOBILIER',
  C2I__CONTRAT_RECRUTEMENT = 'C2I__CONTRAT_RECRUTEMENT',
  C2I__FORMATION = 'C2I__FORMATION',
  C2I__FORMATION_MAESTRO = 'C2I__FORMATION_MAESTRO',
  CANNISIMMO__IMMOBILIER = 'CANNISIMMO__IMMOBILIER',
  CDC__IMMOBILIER__PROGRAMME = 'CDC__IMMOBILIER__PROGRAMME',
  CDC__IMMOBILIER__VENTE = 'CDC__IMMOBILIER__VENTE',
  CDC__PROGRAMME_IMMOBILIER_PSLA = 'CDC__PROGRAMME_IMMOBILIER_PSLA',
  CDC__VENTE_IMMOBILIER_PSLA = 'CDC__VENTE_IMMOBILIER_PSLA',
  CLESENCE__IMMOBILIER__PROGRAMME = 'CLESENCE__IMMOBILIER__PROGRAMME',
  CLESENCE__IMMOBILIER__PROGRAMME_NEUF = 'CLESENCE__IMMOBILIER__PROGRAMME_NEUF',
  CLESENCE__IMMOBILIER__VENTE = 'CLESENCE__IMMOBILIER__VENTE',
  CLESENCE__IMMOBILIER__VENTE_NEUF = 'CLESENCE__IMMOBILIER__VENTE_NEUF',
  COLDWELL_BANKER__CONTRAT_NEGOCIATEUR = 'COLDWELL_BANKER__CONTRAT_NEGOCIATEUR',
  COLDWELL_BANKER__IMMOBILIER = 'COLDWELL_BANKER__IMMOBILIER',
  COTE_PARTICULIERS__IMMOBILIER__VENTE = 'COTE_PARTICULIERS__IMMOBILIER__VENTE',
  DESIMO__IMMOBILIER__PROGRAMME = 'DESIMO__IMMOBILIER__PROGRAMME',
  DESIMO__IMMOBILIER__VENTE = 'DESIMO__IMMOBILIER__VENTE',
  DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME = 'DOMOFRANCE__IMMOBILIER_ACHEVE__PROGRAMME',
  DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE = 'DOMOFRANCE__IMMOBILIER_ACHEVE__VENTE',
  DOMOFRANCE__IMMOBILIER__PROGRAMME = 'DOMOFRANCE__IMMOBILIER__PROGRAMME',
  DOMOFRANCE__IMMOBILIER__VENTE = 'DOMOFRANCE__IMMOBILIER__VENTE',
  DUVAL__IMMOBILIER__PROGRAMME_VEFA = 'DUVAL__IMMOBILIER__PROGRAMME_VEFA',
  DUVAL__IMMOBILIER__VEFA_RESERVATION = 'DUVAL__IMMOBILIER__VEFA_RESERVATION',
  EFFICITY__IMMOBILIER = 'EFFICITY__IMMOBILIER',
  EFFICITY__IMMOBILIER_COMMERCIAL = 'EFFICITY__IMMOBILIER_COMMERCIAL',
  EFFICITY__IMMOBILIER__LOCATION__COMMERCIAL = 'EFFICITY__IMMOBILIER__LOCATION__COMMERCIAL',
  EFFICITY__IMMOBILIER__LOCATION__HABITATION = 'EFFICITY__IMMOBILIER__LOCATION__HABITATION',
  ERA__IMMOBILIER__LOCATION = 'ERA__IMMOBILIER__LOCATION',
  ERA__IMMOBILIER__VENTE = 'ERA__IMMOBILIER__VENTE',
  ERIGERE__ERIGERE_PROGRAMME_VEFA = 'ERIGERE__ERIGERE_PROGRAMME_VEFA',
  ERIGERE__ERIGERE_VEFA_RESERVATION = 'ERIGERE__ERIGERE_VEFA_RESERVATION',
  FOLLIOT__IMMOBILIER = 'FOLLIOT__IMMOBILIER',
  FOLLIOT__IMMOBILIER_PRO = 'FOLLIOT__IMMOBILIER_PRO',
  FRANCE_FORESTRY__IMMOBILIER = 'FRANCE_FORESTRY__IMMOBILIER',
  GIBOIRE__IMMOBILIER__VENTE = 'GIBOIRE__IMMOBILIER__VENTE',
  HERMES__IMMOBILIER__LOCATION_PRO = 'HERMES__IMMOBILIER__LOCATION_PRO',
  HERMES__IMMOBILIER__TRANSACTION_PRO = 'HERMES__IMMOBILIER__TRANSACTION_PRO',
  I3F__IMMOBILIER__BRS_PRELIMINAIRE = 'I3F__IMMOBILIER__BRS_PRELIMINAIRE',
  I3F__IMMOBILIER__BRS_PROGRAMME = 'I3F__IMMOBILIER__BRS_PROGRAMME',
  I3F__IMMOBILIER__PSLA_PRELIMINAIRE = 'I3F__IMMOBILIER__PSLA_PRELIMINAIRE',
  I3F__IMMOBILIER__PSLA_PROGRAMME = 'I3F__IMMOBILIER__PSLA_PROGRAMME',
  I3F__IMMOBILIER__VEFA_PROGRAMME = 'I3F__IMMOBILIER__VEFA_PROGRAMME',
  I3F__IMMOBILIER__VEFA_RESERVATION = 'I3F__IMMOBILIER__VEFA_RESERVATION',
  ICM__CONTRAT_APPORTEUR_DAFFAIRE_ICM = 'ICM__CONTRAT_APPORTEUR_DAFFAIRE_ICM',
  ICM__DOSSIER_DE_VENTE_ICM = 'ICM__DOSSIER_DE_VENTE_ICM',
  IDEAL__IMMOBILIER__PROGRAMME = 'IDEAL__IMMOBILIER__PROGRAMME',
  IDEAL__IMMOBILIER__VENTE = 'IDEAL__IMMOBILIER__VENTE',
  IMMOBILIER__BRS_PROGRAMME_HYBRIDE = 'IMMOBILIER__BRS_PROGRAMME_HYBRIDE',
  IMMOBILIER__BRS_VENTE_HYBRIDE = 'IMMOBILIER__BRS_VENTE_HYBRIDE',
  IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE = 'IMMOBILIER__CONSTRUCTION_MAISON_INDIVIDUELLE',
  IMMOBILIER__LOCATION = 'IMMOBILIER__LOCATION',
  IMMOBILIER__LOCATION_COMMERCIAL = 'IMMOBILIER__LOCATION_COMMERCIAL',
  IMMOBILIER__LOCATION_VISITE = 'IMMOBILIER__LOCATION_VISITE',
  IMMOBILIER__LOTISSEMENT_PROGRAMME = 'IMMOBILIER__LOTISSEMENT_PROGRAMME',
  IMMOBILIER__LOTISSEMENT_VENTE = 'IMMOBILIER__LOTISSEMENT_VENTE',
  IMMOBILIER__POLYNESIE__VENTE = 'IMMOBILIER__POLYNESIE__VENTE',
  IMMOBILIER__PSLA_PRELIMINAIRE = 'IMMOBILIER__PSLA_PRELIMINAIRE',
  IMMOBILIER__PSLA_PROGRAMME = 'IMMOBILIER__PSLA_PROGRAMME',
  IMMOBILIER__SOCIAL_PROGRAMME = 'IMMOBILIER__SOCIAL_PROGRAMME',
  IMMOBILIER__SOCIAL_VENTE = 'IMMOBILIER__SOCIAL_VENTE',
  IMMOBILIER__VENTES_PROGRAMME = 'IMMOBILIER__VENTES_PROGRAMME',
  IMMOBILIER__VENTE_ANCIEN = 'IMMOBILIER__VENTE_ANCIEN',
  IMMOBILIER__VENTE_ANCIEN_ESP = 'IMMOBILIER__VENTE_ANCIEN_ESP',
  IMMOBILIER__VENTE_BIEN_PROFESSIONNEL = 'IMMOBILIER__VENTE_BIEN_PROFESSIONNEL',
  IMMOBILIER__VENTE_NEUF = 'IMMOBILIER__VENTE_NEUF',
  IMMOBILIER__VENTE_VIAGER = 'IMMOBILIER__VENTE_VIAGER',
  IMOCONSEIL__IMMOBILIER__VENTE = 'IMOCONSEIL__IMMOBILIER__VENTE',
  ISM__IMMOBILIER = 'ISM__IMMOBILIER',
  I_PARTICULIERS__IMMOBILIER__ESPAGNE = 'I_PARTICULIERS__IMMOBILIER__ESPAGNE',
  I_PARTICULIERS__IMMOBILIER__HABITATION = 'I_PARTICULIERS__IMMOBILIER__HABITATION',
  I_PARTICULIERS__IMMOBILIER__LUXE = 'I_PARTICULIERS__IMMOBILIER__LUXE',
  I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL = 'I_PARTICULIERS__IMMOBILIER__PROFESSIONNEL',
  I_PARTICULIERS__IMMOBILIER__SUISSE = 'I_PARTICULIERS__IMMOBILIER__SUISSE',
  JOHN_TAYLOR_ALPILLES__VENTE_JOHN_TAYLOR_ALPILLES = 'JOHN_TAYLOR_ALPILLES__VENTE_JOHN_TAYLOR_ALPILLES',
  JOHN_TAYLOR_CORSE__VENTE_JOHN_TAYLOR_CORSE = 'JOHN_TAYLOR_CORSE__VENTE_JOHN_TAYLOR_CORSE',
  JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION = 'JOHN_TAYLOR__IMMOBILIER__LOCATION_HABITATION',
  JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION = 'JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION',
  KELLER_WILLIAMS__IMMOBILIER = 'KELLER_WILLIAMS__IMMOBILIER',
  KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL = 'KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL',
  KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION = 'KELLER_WILLIAMS__IMMOBILIER_LOCATION_HABITATION',
  KELLER_WILLIAMS__IMMOBILIER_VIAGER = 'KELLER_WILLIAMS__IMMOBILIER_VIAGER',
  KELLER_WILLIAMS__LOCATION_COMMERCIAL = 'KELLER_WILLIAMS__LOCATION_COMMERCIAL',
  KEREDES__IMMOBILIER_BRS = 'KEREDES__IMMOBILIER_BRS',
  KW_ABONDANCE__IMMOBILIER = 'KW_ABONDANCE__IMMOBILIER',
  LFEUR__IMMOBILIER__PROGRAMME = 'LFEUR__IMMOBILIER__PROGRAMME',
  LFEUR__IMMOBILIER__VENTE = 'LFEUR__IMMOBILIER__VENTE',
  LGM__IMMOBILIER = 'LGM__IMMOBILIER',
  MA_GESTION_LOCATIVE__LOCATION = 'MA_GESTION_LOCATIVE__LOCATION',
  MA_REGIE__LOCATION = 'MA_REGIE__LOCATION',
  MLS__IMMOBILIER = 'MLS__IMMOBILIER',
  MYNOTARY__CONTRAT_SAAS = 'MYNOTARY__CONTRAT_SAAS',
  MY_CHEZ_MOI__IMMOBILIER__VENTE = 'MY_CHEZ_MOI__IMMOBILIER__VENTE',
  ORGANISATION_INTERNE = 'ORGANISATION_INTERNE',
  ORPI__IMMOBILIER__LOCATION = 'ORPI__IMMOBILIER__LOCATION',
  ORPI__IMMOBILIER__VENTE = 'ORPI__IMMOBILIER__VENTE',
  OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE = 'OZANAM__IMMOBILIER__PSLA_PRELIMINAIRE',
  OZANAM__IMMOBILIER__PSLA_PROGRAMME = 'OZANAM__IMMOBILIER__PSLA_PROGRAMME',
  PIERRE_REVENTE__IMMOBILIER = 'PIERRE_REVENTE__IMMOBILIER',
  PODELIHA__IMMOBILIER__PROGRAMME = 'PODELIHA__IMMOBILIER__PROGRAMME',
  PODELIHA__IMMOBILIER__VENTE = 'PODELIHA__IMMOBILIER__VENTE',
  PRELLO__IMMOBILIER__LOCATION = 'PRELLO__IMMOBILIER__LOCATION',
  PROPRIETES_PRIVEES__BUSINESS = 'PROPRIETES_PRIVEES__BUSINESS',
  PROPRIETES_PRIVEES__IMMOBILIER = 'PROPRIETES_PRIVEES__IMMOBILIER',
  PROPRIETES_PRIVEES__IMMOBILIER_LOCATION = 'PROPRIETES_PRIVEES__IMMOBILIER_LOCATION',
  PROPRIETES_PRIVEES__IMMORESEAU = 'PROPRIETES_PRIVEES__IMMORESEAU',
  PROPRIETES_PRIVEES__IMMORESEAU_BUSINESS = 'PROPRIETES_PRIVEES__IMMORESEAU_BUSINESS',
  PROPRIETES_PRIVEES__IMMORESEAU_LOCATION = 'PROPRIETES_PRIVEES__IMMORESEAU_LOCATION',
  PROPRIETES_PRIVEES__MIZAPRI = 'PROPRIETES_PRIVEES__MIZAPRI',
  PROPRIETES_PRIVEES__PAUL_PARKER = 'PROPRIETES_PRIVEES__PAUL_PARKER',
  PROPRIETES_PRIVEES__RECRUTEMENT_PROPRIETES_PRIVEES = 'PROPRIETES_PRIVEES__RECRUTEMENT_PROPRIETES_PRIVEES',
  PROPRIETES_PRIVEES__REZOXIMO = 'PROPRIETES_PRIVEES__REZOXIMO',
  PROPRIETES_PRIVEES__VIAGER = 'PROPRIETES_PRIVEES__VIAGER',
  PVCI_SENS__IMMOBILIER__PROGRAMME = 'PVCI_SENS__IMMOBILIER__PROGRAMME',
  PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE = 'PVCI_SENS__IMMOBILIER__PROGRAMME_ACHEVE',
  PVCI_SENS__IMMOBILIER__VENTE = 'PVCI_SENS__IMMOBILIER__VENTE',
  PVCI_SENS__IMMOBILIER__VENTE_ACHEVE = 'PVCI_SENS__IMMOBILIER__VENTE_ACHEVE',
  PVCI_SENS__IMMOBILIER__VENTE_SEULE = 'PVCI_SENS__IMMOBILIER__VENTE_SEULE',
  RASQUAIN__IMMOBILIER__LOCATION = 'RASQUAIN__IMMOBILIER__LOCATION',
  RASQUAIN__IMMOBILIER__VENTE = 'RASQUAIN__IMMOBILIER__VENTE',
  RECRUTEMENT__AGENT = 'RECRUTEMENT__AGENT',
  RECRUTEMENT__KW_EXPAND__AGENT = 'RECRUTEMENT__KW_EXPAND__AGENT',
  RECRUTEMENT__KW_POLYNESIE__AGENT = 'RECRUTEMENT__KW_POLYNESIE__AGENT',
  RECRUTEMENT__MYNOTARY__AGENT = 'RECRUTEMENT__MYNOTARY__AGENT',
  SANTONI__IMMOBILIER = 'SANTONI__IMMOBILIER',
  SD_ACCESS__IMMOBILIER_HYBRIDE__PROGRAMME = 'SD_ACCESS__IMMOBILIER_HYBRIDE__PROGRAMME',
  SD_ACCESS__IMMOBILIER_HYBRIDE__VENTE = 'SD_ACCESS__IMMOBILIER_HYBRIDE__VENTE',
  SD_ACCESS__IMMOBILIER__PROGRAMME = 'SD_ACCESS__IMMOBILIER__PROGRAMME',
  SD_ACCESS__IMMOBILIER__VENTE = 'SD_ACCESS__IMMOBILIER__VENTE',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
  SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO = 'SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
  SEXTANT__DOSSIER_DE_VENTE_SEXTANT_LMNP = 'SEXTANT__DOSSIER_DE_VENTE_SEXTANT_LMNP',
  SEXTANT__IMMOBILIER = 'SEXTANT__IMMOBILIER',
  SEXTANT__IMMOBILIER_COMMERCIAL = 'SEXTANT__IMMOBILIER_COMMERCIAL',
  SYNDIC__GENERAL = 'SYNDIC__GENERAL',
  THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER = 'THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER',
  TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS = 'TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_BIENS_PROFESSIONNELS',
  TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION = 'TROIS_G_IMMO__TROIS_G_IMMO_LOCATION_HABITATION',
  TROIS_G_IMMO__TROIS_G_IMMO_NEUF = 'TROIS_G_IMMO__TROIS_G_IMMO_NEUF',
  TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE = 'TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE',
  TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN = 'TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN',
  TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN = 'TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN',
  TROIS_G_IMMO__TROIS_G_IMMO_VENTE_BIENS_PROFESSIONNELS = 'TROIS_G_IMMO__TROIS_G_IMMO_VENTE_BIENS_PROFESSIONNELS',
  TROIS_G_IMMO__TROIS_G_IMMO_VIAGER = 'TROIS_G_IMMO__TROIS_G_IMMO_VIAGER',
  UNIS__COPROPRIETE = 'UNIS__COPROPRIETE',
  UNIS__IMMOBILIER__COMMERCIAL = 'UNIS__IMMOBILIER__COMMERCIAL',
  UNIS__IMMOBILIER__HABITATION = 'UNIS__IMMOBILIER__HABITATION',
  UNIS__LOCATION__COMMERCIAL = 'UNIS__LOCATION__COMMERCIAL',
  UNIS__LOCATION__HABITATION = 'UNIS__LOCATION__HABITATION',
  UNIS__ORGANISATION_INTERNE = 'UNIS__ORGANISATION_INTERNE',
  UNIS__SOCIAL = 'UNIS__SOCIAL',
  VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME = 'VALLOIRE__IMMOBILIER_ACHEVE__PROGRAMME',
  VALLOIRE__IMMOBILIER_ACHEVE__VENTE = 'VALLOIRE__IMMOBILIER_ACHEVE__VENTE',
  VALLOIRE__IMMOBILIER_NEUF__PROGRAMME = 'VALLOIRE__IMMOBILIER_NEUF__PROGRAMME',
  VALLOIRE__IMMOBILIER_NEUF__VENTE = 'VALLOIRE__IMMOBILIER_NEUF__VENTE',
  VIAGER_CONSULTING__IMMOBILIER = 'VIAGER_CONSULTING__IMMOBILIER',
  WHITEBIRD__GENERAL_LOCATION = 'WHITEBIRD__GENERAL_LOCATION',
  WHITEBIRD__GENERAL_PROGRAMME = 'WHITEBIRD__GENERAL_PROGRAMME',
  WHITEBIRD__LOCATION_HABITATION = 'WHITEBIRD__LOCATION_HABITATION'
}
/* eslint-enable typescript-sort-keys/string-enum */

const organizationEntities = [
  EntityType.DATA_TRANSFERT,
  EntityType.DOCUMENT,
  EntityType.INTERCONNECTION,
  EntityType.INVOICES,
  EntityType.MANAGEMENT_REGISTER,
  EntityType.ORGANIZATION,
  EntityType.PL_ETAT_CIVIL,
  EntityType.RECEIVERSHIP_REGISTER,
  EntityType.TRANSACTION_REGISTER
];

export function isOperationEntity(entityType: EntityType): boolean {
  return !organizationEntities.includes(entityType);
}
