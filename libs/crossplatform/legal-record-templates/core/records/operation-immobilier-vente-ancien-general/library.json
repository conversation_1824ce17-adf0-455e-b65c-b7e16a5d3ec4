{"questionTemplates": {"PURGE": {"type": "SELECT", "choices": [{"id": "purge_electronique", "label": "Par recommandé électronique"}, {"id": "purge_papier", "label": "Par recommandé papier"}, {"id": "purge_main_propre", "label": "Par remise en main propre"}, {"id": "purge_aucune", "label": "Aucune car droit de rétractation non applicable"}]}, "LISTE_MEUBLE": {"type": "SELECT", "choices": [{"id": "liste_annexe", "label": "annexée au contrat"}, {"id": "liste_compromis", "label": "incluse dans le contrat"}]}, "PIECE": {"type": "SELECT", "choices": [{"id": "cuisine", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "salon", "label": "Salon"}, {"id": "salle_manger", "label": "<PERSON> à manger"}, {"id": "bureau", "label": "Bureau"}, {"id": "salle_eau", "label": "Salle d'eau"}, {"id": "salle_bains", "label": "Salle de bains"}, {"id": "chambre", "label": "Chambre"}, {"id": "wc", "label": "WC"}, {"id": "entrée", "label": "Entrée"}, {"id": "piece_autre", "label": "<PERSON><PERSON>"}]}, "DEPOT_GARANTIE": {"type": "SELECT", "choices": [{"id": "dix", "label": "10 % du prix de vente"}, {"id": "cinq", "label": "5 % du prix de vente"}, {"id": "depot_autre", "label": "Autre montant"}]}, "DEPOT_GARANTIE_VERSEMENT": {"type": "SELECT-BINARY", "choices": [{"id": "depot_garantie_agence", "label": "A l'Agence"}, {"id": "depot_garantie_notaire", "label": "Au Notaire"}]}, "DEPOT_GARANTIE_DATE": {"type": "SELECT", "choices": [{"id": "depot_garantie_compromis", "label": "Le jour du compromis de vente"}, {"id": "depot_garantie_quinze", "label": "Dans les quinze jours de la signature du compromis"}, {"id": "depot_garantie_retractation", "label": "A la fin du délai de rétractation"}, {"id": "depot_garantie_huit", "label": "Dans les huit jours à compter de la fin du délai de rétractation"}, {"id": "depot_garantie_autre_date", "label": "Un autre délai"}]}, "HONORAIRES_CHARGE": {"type": "SELECT", "choices": [{"id": "honoraires_charge_vendeur", "label": "<PERSON>endeur"}, {"id": "honoraires_charge_acquereur", "label": "de l'Acquéreur"}, {"id": "honoraires_charge_double", "label": "du Vendeur et de l'Acquéreur"}]}, "MANDAT_STATUT": {"type": "SELECT-BINARY", "choices": [{"id": "mandat_vente", "label": "<PERSON><PERSON><PERSON> de vente"}, {"id": "mandat_recherche", "label": "Mandat de recherche"}]}, "MANDAT_TYPE": {"type": "SELECT", "choices": [{"id": "simple", "label": "simple"}, {"id": "semi", "label": "semi-exclusif"}, {"id": "exclusif", "label": "exclusif"}]}, "MANDAT_FREQUENCE": {"type": "SELECT", "choices": [{"id": "systematique", "label": "a<PERSON><PERSON><PERSON> chaque visite"}, {"id": "hebdomadaire", "label": "chaque semaine"}, {"id": "frequence_autre", "label": "autre"}]}, "STATUT_LOCATION": {"type": "SELECT", "choices": [{"id": "locataire_parti", "label": "est parti de son propre chef en donnant congé"}, {"id": "locataire_conge_fait", "label": "a reçu un congé pour vendre pour quitter les lieux"}, {"id": "locataire_conge_subsidiaire", "label": "a reçu un premier congé pour vendre mais un second congé devra lui être adressé"}]}, "VENTE_CONGE": {"type": "SELECT", "choices": [{"id": "vente_conge_locataire_acquereur", "label": "<PERSON>ui et le locataire est l'acquéreur du bien"}, {"id": "vente_conge_locataire_refus", "label": "<PERSON>ui et le locataire a refusé d'acquérir le bien"}, {"id": "vente_conge_locataire_subsidiaire", "label": "Oui mais un second congé pour vendre devra lui être adressé"}, {"id": "vente_conge_locataire_parti", "label": "Non, le locataire a donné congé pour quitter le bien"}, {"id": "vente_conge_locataire_pas_fait", "label": "Non, aucun congé pour vendre n'a été adressé"}]}, "URBANISME_TYPE": {"type": "SELECT", "choices": [{"id": "urbanisme_permis_construire", "label": "Permis de construire"}, {"id": "urbanisme_permis_amenager", "label": "<PERSON><PERSON> d'aménager"}, {"id": "urbanisme_declaration", "label": "Déclaration préalable"}, {"id": "urbanisme_certificat_information", "label": "Un certificat d'urbanisme d'information"}, {"id": "urbanisme_certificat_preoperationnel", "label": "Un certificat d'urbanisme préopérationnel"}]}, "COPRO_FONDS_ALUR": {"type": "SELECT", "choices": [{"id": "copro_alur_total", "label": "Oui un remboursement de la totalité du fonds de travaux ALUR"}, {"id": "copro_alur_annuel", "label": "Oui un remboursement de la dernière cotisation annuelle du fonds de travaux ALUR"}, {"id": "copro_alur_sans", "label": "Non, aucun remboursement du fonds de travaux ALUR"}]}, "COPRO_DOCUMENTS_ALUR": {"type": "SELECT", "choices": [{"id": "copro_documents_alur_dematerialise", "label": "Par voie dématérialisée"}, {"id": "copro_documents_alur_main_propre", "label": "Par remise en main propre"}, {"id": "copro_documents_alur_sru", "label": "Ces documents sont annexés à l'acte et remis en même temps que la purge de la loi SRU"}]}, "RECHERCHE": {"type": "SELECT", "choices": [{"id": "recherche_pourcentage", "label": "Selon un pourcentage du prix de vente"}, {"id": "recherche_fixe", "label": "Selon un montant fixe"}]}, "DELEGATION": {"type": "SELECT", "choices": [{"id": "delegation_partage_moitie", "label": "A la moitié des honoraires prévus"}, {"id": "delegation_partage_autre", "label": "A une autre répartition"}]}, "RESILIATION_PARTIES": {"type": "SELECT", "choices": [{"id": "resiliation_parties_vendeur", "label": "D'une indemnité due par le Vendeur à l'Acquéreur"}, {"id": "resiliation_parties_acquereur", "label": "D'une indemnité due par l'Acquéreur au Vendeur"}, {"id": "resiliation_parties_sans", "label": "Sans aucune indemnité"}]}, "RESILIATION_AGENCE": {"type": "SELECT", "choices": [{"id": "resiliation_agence_vendeur", "label": "D'une indemnité versée à l'Agence due par le Vendeur "}, {"id": "resiliation_agence_acquereur", "label": "D'une indemnité versée à l'Agence due par l'Acquéreur"}, {"id": "resiliation_agence_sans", "label": "Sans versement d'indemnité à l'Agence"}]}, "ALUR_MODE_DOCUMENTS": {"type": "SELECT-BINARY", "choices": [{"id": "alur_remise_papier", "label": "En mains propres"}, {"id": "alur_remise_electronique", "label": "Par voie électronique"}]}, "ALUR__REMISE_DOCUMENTS": {"type": "SELECT", "choices": []}, "ALUR_REMISE_DOCUMENTS": {"type": "PICK_LIST", "choices": [{"id": "remise_document_rcp", "label": "Le règlement de copropriété et modificatifs"}, {"id": "remise_document_etat", "label": "Le pré-étatdaté - informations financières"}, {"id": "remise_document_carnet", "label": "Le carnet d'entretien"}, {"id": "remise_document_fiche", "label": "La fiche synthétique de la Copropriété"}, {"id": "remise_document_pv", "label": "Les derniers PV de l'Assemblée Générale"}], "multiple": true}, "TRACFIN_RISQUE": {"type": "SELECT", "choices": [{"id": "tracfin_faible", "label": "Risque faible - 1"}, {"id": "tracfin_modere", "label": "Risque moderé - 2"}, {"id": "tracfin_fort", "label": "Risque fort - 3"}, {"id": "tracfin_critique", "label": "Risque critique - 4"}]}, "PROCURATION_VENDRE": {"type": "PICK_LIST", "choices": [{"id": "procuration_vente_compromis", "label": "Pour signer le compromis de vente"}, {"id": "procuration_vente_vente", "label": "Pour signer l'acte de vente définitif"}, {"id": "procuration_vente_mandat", "label": "Pour signer le mandat de vente"}], "multiple": true}, "PROCURATION_ACQUERIR": {"type": "SELECT", "choices": [{"id": "procuration_acquerir_compromis", "label": "Pour signer le compromis de vente"}, {"id": "procuration_acquerir_vente", "label": "Pour signer le compromis et l'acte de vente"}]}, "NOTAIRE_SCAPRIM": {"type": "SELECT", "choices": [{"id": "scaprim_lasaygues", "label": "LASAYGUES ET ASSOCIES SELARL"}, {"id": "scaprim_prevot", "label": "SCP PREVOT GERAUDIE BLANC CHAU"}]}, "MANDAT_AVENANT": {"type": "PICK_LIST", "choices": [{"id": "avenant_prix", "label": "Modification du prix de vente"}, {"id": "avenant_debiteur", "label": "Modification débiteur des honoraires"}, {"id": "avenant_honoraires", "label": "Modification montant des honoraires"}, {"id": "avenant_libre", "label": "Modification libre"}], "multiple": true}, "ESTIMATION": {"type": "SELECT", "choices": [{"id": "estimation_valeur_fixe", "label": "Par une valeur fixe"}, {"id": "estimation_valeur_fourchette", "label": "Entre deux estimations"}]}, "MEUBLES": {"type": "SELECT", "choices": [{"id": "liste_meuble_chaises", "label": "<PERSON><PERSON>"}, {"id": "liste_meuble_canape", "label": "Canapé"}, {"id": "liste_meuble_congelateur", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "liste_meuble_fauteuil", "label": "Fauteuil"}, {"id": "liste_meuble_four", "label": "Four"}, {"id": "liste_meuble_hotte", "label": "Hotte"}, {"id": "liste_meuble_lave_vaisselle", "label": "Lave-<PERSON><PERSON><PERSON><PERSON>"}, {"id": "liste_meuble_luminaire", "label": "Luminaires"}, {"id": "liste_meuble_machine", "label": "Machine à laver"}, {"id": "liste_meuble_meuble_bas", "label": "<PERSON><PERSON><PERSON>"}, {"id": "liste_meuble_haut", "label": "<PERSON><PERSON><PERSON>"}, {"id": "liste_meuble_micro_onde", "label": "Micro-onde"}, {"id": "liste_meuble_miroir", "label": "Miroir"}, {"id": "liste_meuble_plaques_induction", "label": "Plaques Induction"}, {"id": "liste_meuble_plaques_vitro", "label": "Plaques Vitrocéramique"}, {"id": "liste_meuble_plauqes_gaz", "label": "Plaques Gaz"}, {"id": "liste_meuble_refrigerateur", "label": "Réfrigérateur"}, {"id": "liste_meuble_seche", "label": "Sèche-Linge"}, {"id": "liste_meuble_table", "label": "Table"}, {"id": "liste_meuble_autre", "label": "<PERSON><PERSON>"}]}, "AVENANT_LISTE": {"type": "PICK_LIST", "choices": [{"id": "avenant_prix", "label": "Modification du prix de vente"}, {"id": "avenant_pret", "label": "Modification du délai d'obtention du prêt"}, {"id": "avenant_pret_montant", "label": "Modification des caractéristiques du prêt"}, {"id": "avenant_signature", "label": "Modification de la date extrême de signature"}, {"id": "avenant_honoraires", "label": "Modification des honoraires"}, {"id": "avenant_bien", "label": "Modification de la désignation du bien"}, {"id": "avenant_libre", "label": "Modification libre"}], "multiple": true}, "LOAN_TYPE": {"type": "SELECT", "choices": [{"id": "taux_fixe", "label": "<PERSON>r<PERSON><PERSON> à taux fixe"}, {"id": "taux_variable", "label": "<PERSON>r<PERSON><PERSON> à taux variable"}, {"id": "in_fine", "label": "<PERSON><PERSON><PERSON><PERSON> in fine"}, {"id": "pel", "label": "Prêt Plan épargne logement"}, {"id": "employeur", "label": "Prêt 1% employeur"}, {"id": "taux_zero", "label": "<PERSON><PERSON><PERSON><PERSON> à taux zéro"}, {"id": "taux_zero_plus", "label": "Prêt à taux zéro plus ou PTZ+"}, {"id": "accession_sociale", "label": "<PERSON><PERSON>êt à l'accession sociale"}, {"id": "conventionne", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "relais", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "paris_logement_0", "label": "Prêt Paris logement 0%"}, {"id": "travaux", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "autre", "label": "<PERSON><PERSON>"}]}, "PIERRE_SITUATION_LOCATIVE": {"type": "SELECT", "choices": [{"id": "pierre_situation_locative_1", "label": "Vente libre de toute gestion avec signature après échéance du bail et TVA à la charge du vendeur"}, {"id": "pierre_situation_locative_2", "label": "Vente libre de toute gestion avec signature avant l’échéance du bail et Résiliation du bail postérieure prévue au préalable. TVA à la charge de l’acquéreur)"}, {"id": "pierre_situation_locative_3", "label": "Vente avec poursuite du bail"}, {"id": "pierre_situation_locative_4", "label": "Vente avec poursuite du mandat de gestion"}, {"id": "pierre_situation_locative_5", "label": " Vente avec résiliation du mandat de gestion"}, {"id": "pierre_situation_locative_6", "label": "Libre de toute gestion"}]}, "PIERRE_VENDEUR_FISCALITE": {"type": "SELECT", "choices": [{"id": "pierre_vendeur_fiscalite_lmnp", "label": "En loueur meublé non professionnel"}, {"id": "pierre_vendeur_fiscalite_lmp", "label": "En loueur meublé professionnel"}, {"id": "pierre_vendeur_fiscalite_nu", "label": "En loueur nu"}, {"id": "pierre_vendeur_fiscalite_professionnel", "label": "Simplement en tant que professionnel"}]}, "VIAGER_STATUT": {"type": "SELECT", "choices": [{"id": "viager_usage", "label": "Vente en viager"}, {"id": "viager_terme", "label": "Vente à terme"}, {"id": "viager_usufruit", "label": "Vente de la nue-propriété"}]}, "VIAGER_OCCUPATION": {"type": "SELECT-BINARY", "choices": [{"id": "viager_occupation_libre", "label": "Libre"}, {"id": "viager_occupation_occupe", "label": "<PERSON><PERSON><PERSON><PERSON>"}]}, "PURGE_LIBRE": {"type": "SELECT", "choices": [{"id": "purge_electronique", "label": "Par recommandé électronique"}, {"id": "purge_papier", "label": "Par recommandé papier"}, {"id": "purge_aucune", "label": "Aucune car droit de rétractation non applicable"}]}, "VIAGER_REPARTITION_CHARGE": {"type": "SELECT", "choices": [{"id": "vendeur", "label": "<PERSON>endeur"}, {"id": "ac<PERSON><PERSON>", "label": "de l'Acquéreur"}], "multiple": true}, "VIAGER_RESERVE_OCCUPATION": {"type": "SELECT", "choices": [{"id": "temporaire", "label": "Temporaire"}, {"id": "perpetuelle", "label": "<PERSON><PERSON><PERSON><PERSON>"}]}, "BLOT_DEPOT_GARANTIE_STATUT": {"type": "SELECT", "choices": [{"id": "cheque", "label": "Le dépôt de garantie est versé par chèque lors de la signature"}, {"id": "cheque_posterieur", "label": "Le dépôt de garantie est versé par chèque après la signature"}, {"id": "virement", "label": "Le dépôt de garantie est versé par virement après la signature"}, {"id": "absence", "label": "Absence de dé<PERSON><PERSON> de garan<PERSON>"}]}, "BLOT_PURGE": {"type": "SELECT", "choices": [{"id": "papier", "label": "Par recommandé papier"}, {"id": "papier_adresse", "label": "Par recommandé papier à une autre adresse que celle de son état civil"}, {"id": "electronique", "label": "Par recommandé électronique"}]}, "VIAGER_STATUT_SIMPLE": {"type": "SELECT", "choices": [{"id": "usufruit", "label": "Réserve d'un droit d'usufruit"}, {"id": "habitation", "label": "Réserve d'un droit d'habitation"}]}, "ACQUEREUR_PRO_ENGAGEMENTS": {"type": "SELECT", "choices": [{"id": "construction", "label": "Engagement de construire (fiscalité à 125€ fixe)"}, {"id": "revente", "label": "Engagement de revente (fiscalité à 0,715%)"}]}, "APPORT_AFFAIRE": {"type": "SELECT", "choices": [{"id": "fixe", "label": "Selon un montant fixe forfaitaire"}, {"id": "pourcentage", "label": "Selon un pourcentage"}]}, "TVA": {"type": "SELECT", "choices": [{"id": "ttc", "label": "Toutes Taxes Comprises"}, {"id": "ht", "label": "Hors Taxes"}]}, "EFFICITY_DEPOT_GARANTIE": {"type": "SELECT", "choices": [{"id": "efficity", "label": "A Efficity"}, {"id": "notaire", "label": "Au notaire"}]}, "VC_TYPE_VENTE": {"type": "SELECT", "choices": [{"id": "viager", "label": "Vente en Viager"}, {"id": "terme", "label": "Vente à Terme"}, {"id": "usufruit", "label": "Vente de la Nue-Propriété"}]}, "ACQUEREUR_CONSOMMATEUR": {"type": "SELECT-BINARY", "choices": [{"id": "professionnelles", "label": "Professionnelles"}, {"id": "personnelles", "label": "<PERSON><PERSON>"}]}, "TODO_VENDEUR_LISTE": {"type": "PICK_LIST", "choices": [{"id": "physique", "label": "<PERSON>ne physique"}, {"id": "morale", "label": "Société"}], "multiple": true}, "TODO_VENDEUR_CAPACITE": {"type": "PICK_LIST", "choices": [{"id": "capable", "label": "Pleine capacité"}, {"id": "mineur", "label": "<PERSON><PERSON>"}, {"id": "sauvegarde", "label": "Sauvegarde de justice"}, {"id": "curatelle", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "tutelle", "label": "<PERSON><PERSON><PERSON>"}], "multiple": true}, "VENDEUR_ACQUEREUR": {"type": "SELECT-BINARY", "choices": [{"id": "ac<PERSON><PERSON>", "label": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"id": "vendeur", "label": "<PERSON><PERSON><PERSON>"}]}, "PRICE_CFP": {"type": "PRICE", "suffix": "CFP", "description": "<PERSON><PERSON><PERSON> compris et hors honoraires acquéreur ; ne pas déduire les honoraires vendeur"}, "CONGE_TYPE": {"type": "SELECT", "choices": [{"id": "meuble", "label": "Location en meublée"}, {"id": "premier", "label": "Premier congé"}, {"id": "second", "label": "Congé secondaire"}], "description": "Attention le locataire doit avoir accepté les recommandés électroniques au préalable pour une notification par AR24"}, "PROJET_ACQUEREUR_LISTE": {"type": "SELECT", "choices": [{"id": "principale", "label": "Résidence Principale"}, {"id": "secondaire", "label": "Résidence Secondaire"}, {"id": "pro", "label": "Locaux professionnels"}, {"id": "locatif", "label": "Investissement locatif"}, {"id": "autre", "label": "Autre projet"}]}, "CHEQUE_VIREMENT": {"type": "SELECT-BINARY", "choices": [{"id": "cheque", "label": "Chèque"}, {"id": "virement", "label": "Virement"}]}, "GIBOIRE_CHARGE_COPRO": {"type": "SELECT", "choices": [{"id": "compromis", "label": "Travaux votés avant le compromis sont à la charge du Vendeur + pouvoir donné à l'Acquéreur pour assister à l'AG"}, {"id": "vente", "label": "Travaux votés avant l'acte de vente sont à la charge du Vendeur"}], "description": "Prévilégiez la première hypothèse, qui permet à l'acquéreur de voter à une éventuelle AG entre le compromis et la vente. Si le pouvoir n'est pas donné par le Vendeur, il conservera la charge des travaux éventuellement votés."}, "NOTAIRE_AGENCE": {"type": "SELECT", "choices": [{"id": "agence", "label": "A l'Agence"}, {"id": "notaire", "label": "Au notaire"}]}, "CONSOMMATION_CONSTRUCTION": {"type": "SELECT-BINARY", "choices": [{"id": "consommation", "label": "Prix à la consommation"}, {"id": "construction", "label": "Coût de la construction"}]}, "FRAIS_VENDEUR": {"type": "SELECT", "choices": [{"id": "mise_en_vente", "label": "Frais de mise en vente (agence immobilière, négociation, publicité)"}, {"id": "diag", "label": "Frais nécessires pour transférer le bien (contrôle, mainlevée...)"}, {"id": "autre", "label": "Autres frais supplémentaires"}], "multiple": true}, "AUTORISATION_FORMALITE_VENDEUR": {"type": "SELECT", "choices": [{"id": "agence", "label": "Par l'Agence"}, {"id": "notaire", "label": "Par le Notaire"}]}, "CONJOINT_INTERVENTION_FAMILIAL": {"type": "SELECT", "choices": [{"id": "signature", "label": "Intervention par signature à l'acte"}, {"id": "annexe", "label": "Intervention par accord annexé à l'acte"}], "description": "En cas de signature de l'acte, pensez à ajouter le signataire lors du paramétrage"}, "CONJOINT_STATUT": {"type": "SELECT", "choices": [{"id": "epoux", "label": "<PERSON><PERSON><PERSON>"}, {"id": "epouse", "label": "Epouse"}, {"id": "cohabitant", "label": "Cohabitant légal"}]}, "OCCUPATION_STATUT": {"type": "SELECT", "choices": [{"id": "libre", "label": "Bien vendu libre"}, {"id": "vendeur_libre", "label": "Bien occupé par le vendeur mais libre à la vente"}, {"id": "locataire", "label": "Bien vendu au Locataire"}, {"id": "differee", "label": "Jouissance à une date ultérieure"}, {"id": "occupe", "label": "Bien occupé par un tiers"}]}, "OCCUPATION_VENDEUR": {"type": "SELECT", "choices": [{"id": "gratuit", "label": "Gratuitement"}, {"id": "payant", "label": "Moyennant Indemnité"}]}, "OCCUPATION_VENDEUR_PAIEMENT": {"type": "SELECT", "choices": [{"id": "journalier", "label": "En proportion des jours occupés"}, {"id": "mensuel", "label": "Tout mois entamé est dû"}]}, "FISCALITE_SOUMISSION": {"type": "SELECT", "choices": [{"id": "tva", "label": "à la TVA"}, {"id": "droit_enregistrement", "label": "aux droits d'enregistrement"}, {"id": "double", "label": "à la TVA et aux droits d'enregistrement"}]}, "TOTAL_PARTIEL": {"type": "SELECT", "choices": [{"id": "total", "label": "Total"}, {"id": "partiel", "label": "En partie"}]}, "SHAREPOINT_DOCUMENT_OBTENTION": {"type": "PICK_LIST", "choices": [{"id": "obtenu", "label": "Obtenu"}, {"id": "non_obtenu", "label": "Non Obtenu"}, {"id": "non_concerne", "label": "Non concerné"}]}, "ANTICIPEE_ETAT_LIEUX": {"type": "SELECT", "choices": [{"id": "realise", "label": "<PERSON><PERSON><PERSON><PERSON>"}, {"id": "a_faire", "label": "A faire"}, {"id": "aucun", "label": "Aucun état des lieux"}]}}, "conditions": {"MEUBLES": [{"id": "presence_meubles", "type": "EQUALS", "value": "oui"}], "DEPOT_GARANTIE": [{"id": "depot_garantie_statut", "type": "EQUALS", "value": "oui"}], "DEPOT_GARANTIE_AUTRE": [{"id": "depot_garantie_montant", "type": "EQUALS", "value": "depot_autre"}], "DEPOT_GARANTIE_AUTRE_DATE": [{"id": "depot_garantie_date", "type": "EQUALS", "value": "depot_garantie_autre_date"}], "PROVISION_FRAIS": [{"id": "provision_frais", "type": "EQUALS", "value": "oui"}], "AUTORISATION_URBANISME": [{"id": "autorisation_urbanisme", "type": "EQUALS", "value": "oui"}], "RENONCIATION_CS_FINANCEMENT": [{"id": "emprunt_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "non"}, {"id": "compromis_signature_electronique", "type": "EQUALS", "value": "oui"}, {"id": "compromis_acquereur_pro", "type": "EQUALS", "value": "personnelles"}], "RENONCIATION_CS_FINANCEMENT_PROMESSE": [{"id": "emprunt_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "non"}, {"id": "promesse_signature_electronique", "type": "EQUALS", "value": "oui"}, {"id": "compromis_acquereur_pro", "type": "EQUALS", "value": "personnelles"}], "LISTE_MEUBLE_ANNEXE": [{"id": "presence_meubles_liste_statut", "type": "EQUALS", "value": "liste_annexe"}], "LISTE_MEUBLE_COMPROMIS": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__GIBOIRE__IMMOBILIER__VENTE"]}, {"id": "presence_meubles_liste_statut", "type": "EQUALS", "value": "liste_compromis"}], "ACQUEREUR_PAS_PRO": [{"id": "compromis_acquereur_pro", "value": "personnelles", "type": "EQUALS"}], "RENONCIATION_CS_FINANCEMENT_PROCURATION": [{"id": "emprunt_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "non"}, {"id": "procuration_acquerir_electronique", "value": "oui", "type": "EQUALS"}], "SCAPRIM_ACQUEREUR": [{"id": "vente_scaprim_acquereur_notaire", "value": "oui", "type": "EQUALS"}], "PURGE_MANUSCRITE_ELECTRONIQUE": [{"id": "compromis_signature_electronique", "value": "oui", "type": "EQUALS"}, {"id": "compromis_purge", "value": "purge_main_propre", "type": "EQUALS"}], "PURGE_MANUSCRITE_ELECTRONIQUE_PROMESSE": [{"id": "promesse_signature_electronique", "value": "oui", "type": "EQUALS"}, {"id": "compromis_purge", "value": "purge_main_propre", "type": "EQUALS"}], "MEUBLE_AUTRE": [{"id": "liste_meuble_designation", "value": "liste_meuble_autre", "type": "EQUALS"}], "PIECE_AUTRE": [{"id": "liste_meuble_piece", "value": "piece_autre", "type": "EQUALS"}], "PIERRE_SITUATION_LOCATIVE_2": [{"id": "pierre_situation_locative", "value": "pierre_situation_locative_2", "type": "EQUALS"}], "PIERRE_MOBILIER": [{"id": "presence_meubles", "value": "oui", "type": "EQUALS"}], "PIERRE_LOCATION_2": [{"id": "pierre_situation_locative", "value": "pierre_situation_locative_2", "type": "EQUALS"}], "PIERRE_LOCATION_3": [{"id": "pierre_situation_locative", "value": "pierre_situation_locative_3", "type": "EQUALS"}], "PIERRE_LOCATION_4": [{"id": "pierre_situation_locative", "value": "pierre_situation_locative_4", "type": "EQUALS"}], "PRESENCE_MEUBLE": [{"id": "presence_meubles", "value": "oui", "type": "EQUALS"}], "CLAUSES_PARTICULIERES_MN": [{"id": "clause_particuliere", "value": "oui", "type": "EQUALS"}], "AD_PROVISION_TRAVAUX": [{"id": "ad_financement_travaux", "value": "oui", "type": "EQUALS"}], "AD_RESERVE": [{"id": "ad_copropriete_reserves", "value": "oui", "type": "EQUALS"}], "AD_CLAUSES_PARTICULIERES_MN": [{"id": "ad_clause_particuliere", "value": "oui", "type": "EQUALS"}], "AD_RENONCIATION_CS_FINANCEMENT": [{"id": "emprunt_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "non"}, {"id": "ad_signature_electronique", "type": "EQUALS", "value": "oui"}], "RENONCIATION_CS_FINANCEMENT_PP": [{"id": "pp_emprunt_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "non"}, {"id": "compromis_signature_electronique", "type": "EQUALS", "value": "oui"}], "BLOT_MEUBLE": [{"id": "blot_presence_meubles", "value": "oui", "type": "EQUALS"}], "BLOT_LOGEMENT_FAMILIAL": [{"id": "blot_logement_famille", "value": "oui", "type": "EQUALS"}, {"id": "blot_compromis_signature_electronique", "value": "oui", "type": "EQUALS"}], "AD_FRAIS_LIBRE": [{"id": "ad_frais_acte_libre", "value": "oui", "type": "EQUALS"}], "BLOT_MEUBLE_VALORISATION": [{"id": "blot_meubles_valorisation", "value": "oui", "type": "EQUALS"}], "RENONCIATION_CS_FINANCEMENT_BLOT": [{"id": "emprunt_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "non"}, {"id": "blot_compromis_signature_electronique", "value": "oui", "type": "EQUALS"}], "PRESENCE_VALEUR_AD": [{"conditions": [{"id": "meubles_valorisation", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AGENCE_DIRECTE__IMMOBILIER"]}], "PRESENCE_MEUBLE_PAS_AD": [{"conditions": [{"id": "presence_meubles", "type": "EQUALS", "value": "oui"}], "type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__AGENCE_DIRECTE__IMMOBILIER", "OPERATION__GIBOIRE__IMMOBILIER", "OPERATION__KEREDES__IMMOBILIER_BRS"]}], "VC_VIAGER": [{"id": "vc_type_vente", "value": "viager", "type": "EQUALS"}], "VC_TERME": [{"id": "vc_type_vente", "value": "terme", "type": "EQUALS"}], "VC_VIAGER_MIXTE": [{"id": "vc_viager_mixte", "value": "oui", "type": "EQUALS"}], "AVENANT_MANDAT": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["IMMOBILIER_VENTE_ANCIEN_AVENANT_MANDAT", "TRANSACTION__AVENANT_MANDAT_ANGLAIS", "BEAUX_VILLAGES__IMMOBILIER__AVENANT_MANDAT", "JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION__AVENANT_MANDAT", "IMOCONSEIL_IMMOBILIER_VENTE_AVENANT_MANDAT", "IMMOBILIER_POLYNESIE_VENTE_MANDAT_VENTE_AVENANT", "KELLER_WILLIAMS_IMMOBILIER_MANDAT_VENTE_SIMPLE_AVENANT_KELLER", "KELLER_WILLIAMS_IMMOBILIER_COMANDAT_AVENANT", "PROPRIETES_PRIVEES_IMMOBILIER_AVENANT_MANDAT_VENTE", "PROPRIETES_PRIVEES_BUSINESS_AVENANT_MANDAT", "PROPRIETES_PRIVEES_IMMORESEAU_AVENANT_MANDANT", "PROPRIETES_PRIVEES_REZOXIMO_AVENANT_MANDAT", "EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY", "I_PARTICULIERS_IMMOBILIER_HABITATION_AVENANT_MANDAT", "IMMOBILIER__POLYNESIE__VENTE__AVENANT_MANDAT_SUCCESS_VAPP", "GIBOIRE_IMMOBILIER_VENTE_AVENANT_MANDAT", "IMOCONSEIL_IMMOBILIER_VENTE_CONTRAT_PRELIMINAIRE_ACHAT", "AXO_ACTIF_IMMOBILIER_HABITATION_AVENANT_MANDAT"]}], "AVENANT_PRIX": [{"id": "mandat_avenant_type", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "MANDAT", "0"], "type": "CONTAINS", "value": "avenant_prix"}], "AVENANT_PRIX_PP": [{"id": "mandat_avenant_type_pp", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "MANDAT", "0"], "type": "CONTAINS", "value": "avenant_prix"}], "AVENANT_PRIX_PRESENT_COMPROMIS": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE", "BEAUX_VILLAGES__IMMOBILIER__COMPROMIS", "IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS", "IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER", "IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE", "PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP", "IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT", "IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE", "IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT", "IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE", "IMMOBILIER_VENTE_ANCIEN_BON_VISITE", "IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS", "GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE", "GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT", "GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE", "GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS", "EFFICITY_IMMOBILIER_BON_VISITE", "EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS", "AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE"]}, {"type": "EMPTY", "id": "nouveau_prix_de_vente"}], "CONTRAT_AUTRE_COMPROMIS": [{"type": "DIFFERENT_CONTRACT_MODELS", "model": ["IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE", "BEAUX_VILLAGES__IMMOBILIER__COMPROMIS", "IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS", "IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER", "IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE", "PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP", "IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL", "IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT", "IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE", "IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT", "IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE", "IMMOBILIER_VENTE_ANCIEN_BON_VISITE", "AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE", "IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS", "GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT", "GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE", "GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE", "GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS", "EFFICITY_IMMOBILIER_BON_VISITE", "EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS"]}], "AVENANT_PAS_PRIX": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__EFFICITY__IMMOBILIER", "OPERATION__IMOCONSEIL__IMMOBILIER__VENTE", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION"]}, {"type": "EQUALS_CONTRACT_MODELS", "model": ["IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE", "BEAUX_VILLAGES__IMMOBILIER__COMPROMIS", "IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS", "IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER", "IMMOBILIER_POLYNESIE_VENTE_TRANSMISSION_NOTAIRE", "IMMOBILIER_VENTE_BIEN_PROFESSIONNEL_COMPROMIS_PROFESSIONNEL", "IMMOBILIER_VENTE_ANCIEN_PROMESSE_ACHAT", "IMMOBILIER_VENTE_ANCIEN_PROMESSE_VENTE", "IMMOBILIER_POLYNESIE_VENTE_PROMESSE_ACHAT", "IMMOBILIER_POLYNESIE_VENTE_PROMESSE_VENTE", "IMMOBILIER_VENTE_ANCIEN_BON_VISITE", "IMMOBILIER_VENTE_ANCIEN_BON_VISITE_ANGLAIS", "AXO_ACTIF_IMMOBILIER_HABITATION_BON_VISITE"]}, {"id": "mandat_avenant_type", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "MANDAT", "0"], "type": "NOT_CONTAINS", "value": "avenant_prix"}], "AVENANT_PAS_PRIX_PP": [{"type": "DIFFERENT_TEMPLATES", "templates": ["OPERATION__EFFICITY__IMMOBILIER", "OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION"]}, {"type": "EQUALS_CONTRACT_MODELS", "model": ["PROPRIETES_PRIVEES_IMMOBILIER_COMPROMIS_PP", "GIBOIRE_IMMOBILIER_VENTE_COMPROMIS_VENTE", "GIBOIRE_IMMOBILIER_VENTE_AVENANT_COMPROMIS", "GIBOIRE_IMMOBILIER_VENTE_PROCURATION_ACHAT", "GIBOIRE_IMMOBILIER_VENTE_PROCURATION_VENTE"]}, {"id": "mandat_avenant_type_pp", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "MANDAT", "0"], "type": "NOT_CONTAINS", "value": "avenant_prix"}], "DEPOT_GARANTIE_AGENCE": [{"id": "depot_garantie_versement", "value": "depot_garantie_agence", "type": "EQUALS"}], "AVENANT_EFFICITY": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__EFFICITY__IMMOBILIER"]}, {"type": "EQUALS_CONTRACT_MODELS", "model": ["IMMOBILIER_VENTE_ANCIEN_COMPROMIS_DE_VENTE", "BEAUX_VILLAGES__IMMOBILIER__COMPROMIS", "EFFICITY_IMMOBILIER_BON_VISITE", "EFFICITY_IMMOBILIER_BON_VISITE_ANGLAIS", "IMMOBILIER_VENTE_ANCIEN_CONSTITUTION_DOSSIER", "IMMOBILIER_VENTE_ANCIEN_COMPROMIS_ANGLAIS"]}, {"id": "mandat_avenant_signature_electronique", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "MANDAT", "0"], "type": "EMPTY"}], "": [], "FICHE_CLOTURE": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["IMMOBILIER_VENTE_ANCIEN_FICHE_CLOTURE"]}], "CONTRAT_I_PARTICULIERS": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["I_PARTICULIERS_IMMOBILIER_HABITATION_CONTROLE_QUALITE"]}], "KW_VIAGER": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_SUCCESS_VIAGER", "KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_SUCCESS_VIAGER_A_TERME", "KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW", "KELLER_WILLIAMS_IMMOBILIER_VIAGER_BON_VISITE", "KELLER_WILLIAMS_IMMOBILIER_VIAGER_OFFRE_ACHAT", "KELLER_WILLIAMS_IMMOBILIER_VIAGER_OFFRE_ACHAT_TERME"]}], "AVENANT": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["KELLER_WILLIAMS_IMMOBILIER_VIAGER_MANDAT_VENTE_VIAGER_SIMPLE_KW_AVENANT"]}], "SUSPICION_TRACFIN": [{"id": "suspicion_tracfin", "value": "oui", "type": "EQUALS"}], "PP_FICHE_SYNTHESE": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["PROPRIETES_PRIVEES_IMMOBILIER_CONSTITUTION_DOSSIER"]}], "CPA_IMOCONSEIL": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__IMOCONSEIL__IMMOBILIER__VENTE"]}], "RENONCIATION_CS_FINANCEMENT_PROCURATION_GIBOIRE": [{"id": "emprunt_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "non"}, {"id": "compromis_signature_electronique", "value": "oui", "type": "EQUALS"}], "RENONCIATION_30_GIBOIRE": [{"id": "mention_manuscrite_procuration_giboire_30", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "oui"}, {"id": "emprunt_statut", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "oui"}, {"id": "compromis_signature_electronique", "value": "oui", "type": "EQUALS"}], "MEUBLE_GIBOIRE": [{"conditions": [{"id": "presence_meubles", "type": "EQUALS", "value": "oui"}], "type": "EQUALS_TEMPLATES", "templates": ["OPERATION__GIBOIRE__IMMOBILIER__VENTE"]}], "MEUBLES_CHIFFRES": [{"id": "meubles_valorisation", "value": "oui", "type": "EQUALS"}], "EFFICITY_AVENANT_CONTRAT": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY"]}], "EFFICITY_PAS_AVENANT": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__EFFICITY__IMMOBILIER"]}, {"type": "DIFFERENT_CONTRACT_MODELS", "model": ["EFFICITY_IMMOBILIER_MANDAT_AVENANT_EFFICITY"]}, {"id": "mandat_avenant_signature_electronique", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "MANDAT", "0"], "type": "NOT_EMPTY"}], "AVENANT_PAS_PRIX_SIMPLE": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION"]}, {"id": "mandat_avenant_type_pp", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "MANDAT", "0"], "type": "NOT_CONTAINS", "value": "avenant_prix"}], "SHAREPOINT_VENDEUR_MANDAT": [{"id": "taylor_sharepoint_vendeur_mandat", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_VENDEUR_TAXE": [{"id": "taylor_sharepoint_vendeur_taxe", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_VENDEUR_CNI": [{"id": "taylor_sharepoint_vendeur_cni", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_VENDEUR_TITRE": [{"id": "taylor_sharepoint_vendeur_titre", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_VENDEUR_KYC": [{"id": "taylor_sharepoint_vendeur_kyc", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_VENDEUR_OPEN": [{"id": "taylor_sharepoint_vendeur_open", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_VENDEUR_GEL": [{"id": "taylor_sharepoint_vendeur_gel", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_VENDEUR_GOOGLE": [{"id": "taylor_sharepoint_vendeur_google", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_VENDEUR_TRACFIN": [{"id": "taylor_sharepoint_vendeur_tracfin", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_VENDEUR_DOSSIER": [{"id": "taylor_sharepoint_vendeur_dossier", "value": "obtenu", "type": "CONTAINS"}], "OPERATION_JOHN_TAYLOR": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION", "OPERATION__JOHN_TAYLOR_CORSE__VENTE_JOHN_TAYLOR_CORSE", "OPERATION__JOHN_TAYLOR_ALPILLES__VENTE_JOHN_TAYLOR_ALPILLES"]}], "SHAREPOINT_ACQUEREUR_OFFRE": [{"id": "taylor_sharepoint_acquereur_offre", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_CNI": [{"id": "taylor_sharepoint_acquereur_cni", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_OPEN": [{"id": "taylor_sharepoint_acquereur_open", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_GEL": [{"id": "taylor_sharepoint_acquereur_gel", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_GOOGLE": [{"id": "taylor_sharepoint_acquereur_google", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_TRACFIN": [{"id": "taylor_sharepoint_acquereur_tracfin_partie_un", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_PROMESSE": [{"id": "taylor_sharepoint_acquereur_promesse", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_TRANSACTION": [{"id": "taylor_sharepoint_acquereur_transaction", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_FACTURE": [{"id": "taylor_sharepoint_acquereur_facture", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_TRACFIN_DEUX": [{"id": "taylor_sharepoint_acquereur_tracfin_partie_deux", "value": "obtenu", "type": "CONTAINS"}], "SHAREPOINT_ACQUEREUR_VENTE": [{"id": "taylor_sharepoint_acquereur_vente", "value": "obtenu", "type": "CONTAINS"}], "VENTE_LOGEMENT_ACCORD_ANNEXE": [{"id": "belgique_vente_logement_familial_intervention", "value": "annexe", "type": "EQUALS"}], "OPERATION_ZONE_LOCATION": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER"]}], "ANTICIPEE_ETAT_LIEUX_REALISE": [{"id": "anticipee_etat_lieux", "value": "realise", "type": "EQUALS"}], "RENONCIATION_PRET_PARTIEL": [{"id": "pret_partiel", "recordPath": ["OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES", "FINANCEMENT_ACQUEREUR", "0"], "type": "EQUALS", "value": "oui"}, {"id": "compromis_signature_electronique", "type": "EQUALS", "value": "oui"}, {"id": "compromis_acquereur_pro", "type": "EQUALS", "value": "personnelles"}]}}