{"questionTemplates": {"INTERMEDIAIRE_STATUT": {"type": "SELECT", "choices": [{"id": "representant", "label": "Représentant légal de l'agence"}, {"id": "agent_co_salarie", "label": "Agent <PERSON><PERSON><PERSON><PERSON> de l'agence"}, {"id": "agent_co_independannt", "label": "Agent commercial indépendant"}, {"id": "agent_co_portage", "label": "Sous portage salarial"}, {"id": "agent_co_structure", "label": "Mandataire avec carte Professionnelle, via une société"}]}, "REPRESENTANT_STATUT": {"type": "SELECT", "choices": [{"id": "representant_legal", "label": "Représentant légal de la société"}, {"id": "representant_pouvoir", "label": "Représentant en vertu d'un pouvoir"}]}, "INTERMEDIAIRE_STATUT_KW": {"type": "SELECT", "choices": [{"id": "agent_mega", "label": "Mega Agent titulaire d'une carte professionnelle"}, {"id": "agent_co_independannt", "label": "Agent commercial"}, {"id": "agent_co_salarie", "label": "Agent sa<PERSON><PERSON>"}, {"id": "gerant", "label": "<PERSON><PERSON><PERSON>"}]}, "INTERMEDIAIRE_STATUT_PP": {"type": "SELECT", "choices": [{"id": "agent", "label": "Agent Commercial"}, {"id": "portage", "label": "Sous Portage Salarial"}]}, "INTERMEDIAIRE_STATUT_COTE_PARTICULIER": {"type": "SELECT", "choices": [{"id": "gerant", "label": "<PERSON><PERSON><PERSON>"}, {"id": "responsable", "label": "Responsable de l'agence"}, {"id": "negociateur", "label": "Négociateur salarié"}, {"id": "agent_co", "label": "Agent commercial"}]}, "PRELEVEMENT_TYPE": {"choices": [{"id": "recurrent", "label": "<PERSON><PERSON><PERSON> r<PERSON>"}, {"id": "ponctuel", "label": "Paiement ponctuel"}], "type": "SELECT"}, "BAREME_PP": {"choices": [{"id": "bareme_ancien", "label": "Ancien barème national avant Juin 2025"}, {"id": "bareme_nouveau", "label": "Nouveau barème national après Juin 2025"}, {"id": "bareme_derogatoire", "label": "Bar<PERSON> propre"}], "type": "SELECT"}}, "conditions": {"CCI": [{"id": "agence_carte_cci_statut", "type": "EQUALS", "value": "oui"}], "BAREME_PP": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__PROPRIETES_PRIVEES__IMMOBILIER", "OPERATION__PROPRIETES_PRIVEES__BUSINESS", "OPERATION__PROPRIETES_PRIVEES__IMMOBILIER_LOCATION", "OPERATION__PROPRIETES_PRIVEES__MIZAPRI", "OPERATION__PROPRIETES_PRIVEES__VIAGER"]}, {"id": "bareme_pp_statut", "value": "oui", "type": "EQUALS"}], "BAREME_PAS_PP": [{"id": "bareme_pp_statut", "value": "non", "type": "EQUALS"}], "BAREME_NOUVEAU_PAS_PP": [{"id": "bareme_pp_statut_triple", "value": "bareme_derogatoire", "type": "EQUALS"}], "BAREME_PP_2025": [{"id": "bareme_pp_statut_triple", "value": "bareme_nouveau", "type": "EQUALS"}], "BAREME_PP_ANCIEN": [{"id": "bareme_pp_statut_triple", "value": "bareme_ancien", "type": "EQUALS"}], "BAREME_PP_REZOXIMMO": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__PROPRIETES_PRIVEES__REZOXIMO"]}, {"id": "bareme_pp_statut", "value": "oui", "type": "EQUALS"}], "BAREME_PP_IMMORESEAU": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__PROPRIETES_PRIVEES__IMMORESEAU", "OPERATION__PROPRIETES_PRIVEES__PAUL_PARKER", "OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_BUSINESS", "OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_LOCATION"]}, {"id": "bareme_pp_statut", "value": "oui", "type": "EQUALS"}], "BAREME_PP_PAUL_PARKER": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__PROPRIETES_PRIVEES__PAUL_PARKER"]}, {"id": "bareme_pp_statut", "value": "oui", "type": "EQUALS"}], "PACK_PP_PRO": [{"id": "pack_pp", "recordPath": ["OPERATION__RECRUTEMENT__AGENT__GENERAL", "INFO_GENERALE", "0"], "type": "EQUALS", "value": "pro"}]}}