// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordPersonnePhysiqueIntermediaireImmobilier: LegalRecordTemplate = {
  config: {
    tags: {
      email: {
        order: 0,
        questionId: 'email'
      },
      telephone: {
        questionId: 'telephone',
        format: 'PHONE',
        order: 1
      },
      informations_personnelles_date_naissance: {
        questionId: 'informations_personnelles_date_naissance',
        format: 'DATE',
        order: 2
      }
    },
    recordLinks: [],
    search: ['nom', 'prenoms'],
    duplicate: ['nom', 'prenoms']
  },
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'homme',
              label: 'Monsieur'
            },
            {
              id: 'femme',
              label: 'Madame'
            }
          ],
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'sexe',
          label: 'Civilité',
          type: 'SELECT'
        },
        {
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'nom',
          label: 'Nom',
          type: 'TEXT',
          uppercase: 'UPPERCASE'
        },
        {
          description: "Les prénoms composés doivent être séparés par un trait d'union",
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'prenoms',
          label: 'Prénom(s)',
          type: 'TEXT',
          uppercase: 'WORD'
        },
        {
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'email',
          label: 'Email',
          type: 'EMAIL'
        },
        {
          description: 'Un téléphone portable est préférable pour la signature électronique.',
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'telephone',
          label: 'Téléphone',
          type: 'PHONE'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'adresse',
          label: 'Adresse',
          type: 'ADDRESS'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'informations_personnelles_date_naissance',
          label: 'Date de naissance',
          type: 'DATE'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'informations_personnelles_pays_naissance',
          label: 'Pays de naissance',
          type: 'COUNTRY'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'informations_personnelles_ville_naissance',
          label: 'Ville de naissance',
          type: 'TEXT'
        },
        {
          id: 'informations_personnelles_nationalite',
          label: 'Nationalité',
          type: 'COUNTRY'
        },
        {
          id: 'informations_personnelles_nom_pere',
          label: 'Prénom et nom du Père',
          type: 'TEXT'
        },
        {
          id: 'informations_personnelles_nom_mere',
          label: 'Prénom et nom de la Mère',
          type: 'TEXT'
        }
      ],
      id: '1482723f_3c94_4434_84dc_1e2100b63a32',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'societe_appartenance',
          label: "Société d'appartenance",
          type: 'TEXT'
        },
        {
          id: 'assurance_rcp_nom',
          label: "Nom de l'établissement d'assurance de la responsabilité civile professionnelle",
          type: 'TEXT'
        },
        {
          id: 'assurance_rcp_numero',
          label: 'Numéro de contrat - responsabilité civile professionnelle',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'representant',
              label: "Représentant légal de l'agence"
            },
            {
              id: 'agent_co_salarie',
              label: "Agent salarié de l'agence"
            },
            {
              id: 'agent_co_independannt',
              label: 'Agent commercial indépendant'
            },
            {
              id: 'agent_co_portage',
              label: 'Sous portage salarial'
            },
            {
              id: 'agent_co_structure',
              label: 'Mandataire avec carte Professionnelle, via une société'
            }
          ],
          id: 'intermediaire_statut',
          label: "Statut au sein de l'agence",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'gerant',
              label: 'Gérant'
            },
            {
              id: 'responsable',
              label: "Responsable de l'agence"
            },
            {
              id: 'negociateur',
              label: 'Négociateur salarié'
            },
            {
              id: 'agent_co',
              label: 'Agent commercial'
            }
          ],
          id: 'intermediaire_statut_cote_particulier',
          label: "Statut au sein de l'agence",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'agent_mega',
              label: "Mega Agent titulaire d'une carte professionnelle"
            },
            {
              id: 'agent_co_independannt',
              label: 'Agent commercial'
            },
            {
              id: 'agent_co_salarie',
              label: 'Agent salarié'
            },
            {
              id: 'gerant',
              label: 'Gérant'
            }
          ],
          id: 'intermediaire_statut_kw',
          label: "Statut au sein de l'agence",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'agent',
              label: 'Agent Commercial'
            },
            {
              id: 'portage',
              label: 'Sous Portage Salarial'
            }
          ],
          id: 'intermediaire_statut_pp',
          label: "Statut au sein de l'agence",
          type: 'SELECT'
        },
        {
          id: 'intermediaire_qualite',
          label: 'Statut / qualité',
          type: 'TEXT'
        },
        {
          id: 'intermediaire_qualite_date',
          label: 'Date du pouvoir',
          type: 'DATE'
        },
        {
          id: 'numero_ss',
          label: 'Numéro de sécurité sociale',
          type: 'TEXT'
        },
        {
          id: 'numero_ipi',
          label: 'Numéro IPI',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'numero_rsca_statut',
          label: 'Le numéro RSAC est-il déjà connu ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'numero_rsac',
          label: "Numéro d'inscription au Registre Spécial des Agents Commerciaux",
          type: 'TEXT'
        },
        {
          id: 'numero_rsac_commune',
          label: 'Commune du tribunal du Registre Spécial des Agents Commerciaux',
          type: 'TEXT'
        },
        {
          id: 'commune_activite',
          label: "Commune d'activité",
          type: 'TEXT'
        },
        {
          id: 'immatriculation_numero',
          label: "Numéro d'immatriculation professionnelle",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mandataire_carte_professionnelle_statut',
          label: "Mandataire détenteur d'une carte professionnelle",
          type: 'SELECT-BINARY'
        },
        {
          id: 'kw_mega_societe',
          label: 'Nom de la société du Méga Agent',
          type: 'TEXT'
        },
        {
          id: 'mandataire_societe_denomination',
          label: 'Dénomination de la société détentrice de la carte Pro',
          type: 'TEXT'
        },
        {
          id: 'mandataire_societe_siege',
          label: 'Siège social de la société',
          type: 'ADDRESS'
        },
        {
          id: 'kw_mega_societe_rcs',
          label: "Numéro d'immatriculation de la société",
          type: 'TEXT'
        },
        {
          id: 'kw_mega_societe_carte',
          label: 'Numéro de carte professionnelle de la société',
          type: 'TEXT'
        },
        {
          id: 'kw_mega_societe_cci',
          label: 'Commune de la CCI délivrant la carte professionnelle',
          type: 'TEXT'
        },
        {
          id: 'kw_tracfin',
          label: 'Nom du déclarant TRACFIN de référence',
          type: 'TEXT'
        },
        {
          id: 'pouvoir_donneur_designation',
          label: 'Désignation de la personne ayant consenti le pouvoir',
          type: 'TEXT'
        },
        {
          id: 'pouvoir_donneur_designation_statut',
          label: 'Qualité / statut de la personne donnant le pouvoir',
          type: 'TEXT'
        },
        {
          id: 'carte_pro_cnefaf',
          label: 'Carte professionnelle CNEFAF',
          type: 'UPLOAD'
        },
        {
          id: 'employeur_immatriculation_numero',
          label: "Numéro d'immatriculation de l'entité juridique employeur",
          type: 'TEXT'
        },
        {
          id: 'employeur_siege',
          label: "Siège de l'entité juridique",
          type: 'ADDRESS'
        }
      ],
      id: '8a4aadf2_a477_4a3b_ac25_4405a74fd6e6',
      label: 'Informations professionnelles',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'deuxieme_dir_statut',
          label: 'Ajouter un 2e Directeur',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'homme',
              label: 'Monsieur'
            },
            {
              id: 'femme',
              label: 'Madame'
            }
          ],
          id: 'sexe_deuxieme_dir',
          label: 'Civilité',
          type: 'SELECT'
        },
        {
          id: 'prenoms_deuxieme_dir',
          label: 'Prénoms',
          type: 'TEXT'
        },
        {
          id: 'nom_deuxieme_dir',
          label: 'Nom',
          type: 'TEXT'
        },
        {
          id: 'qualite_deuxieme_dir',
          label: 'Statut / qualité',
          type: 'TEXT'
        }
      ],
      id: 'acb9f501_2747_4ec4_8291_7abb069a8486',
      label: 'Informations - 2e Dir',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'troisieme_dir_statut',
          label: 'Ajouter un 3e Directeur',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'homme',
              label: 'Monsieur'
            },
            {
              id: 'femme',
              label: 'Madame'
            }
          ],
          id: 'sexe_troisieme_dir',
          label: 'Civilité',
          type: 'SELECT'
        },
        {
          id: 'prenoms_troisieme_dir',
          label: 'Prénoms',
          type: 'TEXT'
        },
        {
          id: 'nom_troisieme_dir',
          label: 'Nom',
          type: 'TEXT'
        },
        {
          id: 'qualite_troisieme_dir',
          label: 'Statut / qualité',
          type: 'TEXT'
        }
      ],
      id: 'dd4ef5ca_0a1f_4dde_942b_6f0b963f9566',
      label: 'Informations - 3e Dir',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'bareme_pp_statut',
          label: 'Le barème des honoraires utilisé est-il le barème général du réseau ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'bareme_ancien',
              label: 'Ancien barème national avant Juin 2025'
            },
            {
              id: 'bareme_nouveau',
              label: 'Nouveau barème national après Juin 2025'
            },
            {
              id: 'bareme_derogatoire',
              label: 'Barème propre'
            }
          ],
          id: 'bareme_pp_statut_triple',
          label: 'Quel barème des honoraires est utilisé',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'bareme_pp_statut_triple',
                value: 'bareme_ancien',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_BAREME_PP_2022'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'bareme_pp_2022_2',
          label: 'Barème des honoraires - Propriétés Privées',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'bareme_pp_statut_triple',
                value: 'bareme_nouveau',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_BAREME_PP_2025'
          },
          id: 'bareme_pp_2025',
          label: 'Barème des honoraires - Propriétés Privées Juin 2025',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'bareme_pp_statut',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__PROPRIETES_PRIVEES__IMMOBILIER',
                  'OPERATION__PROPRIETES_PRIVEES__BUSINESS',
                  'OPERATION__PROPRIETES_PRIVEES__IMMOBILIER_LOCATION',
                  'OPERATION__PROPRIETES_PRIVEES__MIZAPRI',
                  'OPERATION__PROPRIETES_PRIVEES__VIAGER'
                ]
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_BAREME_PP_VIAGER'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'bareme_pp_viager',
          label: 'Barème des honoraires - Propriétés Privées Viager',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'bareme_pp_statut_triple',
                value: 'bareme_ancien',
                type: 'EQUALS'
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_BAREME_PP_BUSINESS'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'bareme_pp_business',
          label: 'Barème des honoraires - Propriétés Privées Business',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'bareme_pp_statut',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__PROPRIETES_PRIVEES__REZOXIMO']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_BAREME_PP_REZOXIMO'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'bareme_pp_rezoximo',
          label: 'Barème des honoraires - Rezoximo',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'bareme_pp_statut',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__PROPRIETES_PRIVEES__IMMORESEAU',
                  'OPERATION__PROPRIETES_PRIVEES__PAUL_PARKER',
                  'OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_BUSINESS',
                  'OPERATION__PROPRIETES_PRIVEES__IMMORESEAU_LOCATION'
                ]
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_BAREME_PP_IMMORESEAU'
          },
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'bareme_pp_immoreseau',
          label: 'Barème des honoraires - Immoréseau',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'bareme_pp_statut',
                value: 'oui',
                type: 'EQUALS'
              },
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__PROPRIETES_PRIVEES__PAUL_PARKER']
              }
            ]
          ],
          defaultAnswer: {
            resourceId: 'STATIC_FILES_BAREME_PP_PAUL_PARKER'
          },
          id: 'bareme_pp_paul_parker',
          label: 'Barème des honoraires - Paul Parker',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'bareme_pp_statut',
                value: 'non',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'bareme_pp_statut_triple',
                value: 'bareme_derogatoire',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'bareme_pp_libre',
          label: 'Barème des honoraires',
          type: 'UPLOAD'
        }
      ],
      id: 'a0e905fd_a34c_4522_b48b_3b6ad406f5cc',
      label: 'Droit de la consommation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'representant_legal',
              label: 'Représentant légal de la société'
            },
            {
              id: 'representant_pouvoir',
              label: "Représentant en vertu d'un pouvoir"
            }
          ],
          id: 'representant_staut',
          label: 'Statut du Représentant',
          type: 'SELECT'
        },
        {
          id: 'representant_statut_libre',
          label: 'Qualité du représentant',
          type: 'TEXT'
        }
      ],
      id: '55023c2c_0651_4db0_ab98_9144c0a17a49',
      label: 'Informations sur la Représentation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mediation',
          label: "Désignation d'un médiateur",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'mediateur_nom',
              label: 'Nom du médiateur',
              type: 'TEXT'
            },
            {
              id: 'mediateur_adresse',
              label: 'Adresse du médiateur',
              type: 'ADDRESS'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'mediateur_site_statut',
              label: 'Le médiateur possède un site internet',
              type: 'SELECT-BINARY'
            },
            {
              id: 'mediateur_site',
              label: 'Site internet du médiateur',
              type: 'TEXT'
            },
            {
              id: 'mediateur_email',
              label: 'Email du médiateur',
              type: 'TEXT'
            },
            {
              id: 'mediateur_telephone',
              label: 'Téléphone du mediateur',
              type: 'PHONE'
            }
          ],
          id: 'd365b876_e4f4_4bb7_8efe_7c9dc891c68d',
          label: 'CONDITION_BLOCK_Médiateur',
          type: 'CONDITION_BLOCK'
        },
        {
          id: 'mediateur_interne_nom',
          label: 'Personne en interne en charge du traitement des réclamations',
          type: 'TEXT'
        }
      ],
      id: '2c5ca937_1edf_46e9_863a_81c06f4fa9b6',
      label: 'Médiation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'mandat_sepa_iban',
          label: 'Numéro IBAN du compte',
          type: 'TEXT'
        },
        {
          id: 'mandat_sepa_bic',
          label: 'Code BIC du compte',
          type: 'TEXT'
        },
        {
          id: 'mandat_sepa_banque',
          label: 'Etablissement bancaire teneur du compte',
          type: 'TEXT'
        },
        {
          id: 'mandat_sepa_banque_adresse',
          label: "Adresse de l'établissement bancaire teneur du compte",
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'recurrent',
              label: 'Paiement récurrent'
            },
            {
              id: 'ponctuel',
              label: 'Paiement ponctuel'
            }
          ],
          id: 'mandat_sepa_prelevement_type',
          label: 'Type de prélèvement',
          type: 'SELECT'
        }
      ],
      id: 'bc4a3e97_9bb0_409a_8c9e_36f1972f0f05',
      label: 'Prélèvement Mandat SEPA',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'cni',
          label: 'Carte identité recto/verso',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'photo_identite',
          label: "Photo d'identité",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'justificatif_domicile',
          label: 'Justificatif de domicile',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'curriculum_vitae',
          label: 'Curriculum Vitae',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'immatriculation_rsac',
          label: 'Extrait immatriculation RSAC',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'rsac_k',
          label: 'RSAC (extrait K)',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'justificatif_siret',
          label: 'Justificatif SIRET',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'pack_pp',
                recordPath: ['OPERATION__RECRUTEMENT__AGENT__GENERAL', 'INFO_GENERALE', '0'],
                type: 'EQUALS',
                value: 'pro'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'justificatif_chiffre_affaire',
          label: 'Justificatif Chiffre affaire',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'casier_judiciaire',
          label: 'Casier Judiciaire',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'iban',
          label: 'IBAN',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'bic',
          label: 'BIC',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'demande_rcp',
          label: 'Demande Assurance RCP',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'attestaiton_rcp',
          label: 'Attesation RCP',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'carte_collaborateur',
          label: 'Carte collaborateur',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_agent_commercial',
          label: "Mandat d'agent commercial",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'carte_vitale',
          label: 'Carte Vitale',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'RIB_mandataire',
          label: 'RIB',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'mandat_sepa',
          label: 'Mandat SEPA',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'bsa_us',
          label: 'BSA US',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'securite_sociale_bsa',
          label: 'N° sécurité sociale bénéficiaire BSA',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'contrat_bsa',
          label: 'Contrat de prestation de service BSA FRANCE',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'contrat_coaching',
          label: 'Contrat coaching',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'demande_attestation_habilitation',
          label: "Demande d'attestation d'habilitation",
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'cheque_cci',
          label: 'Chèque CCI 55€',
          type: 'UPLOAD'
        }
      ],
      id: '2149bf94_469a_40bb_8980_cdb41c363ff4',
      label: 'Documents',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__PERSONNE__PHYSIQUE__INTERMEDIAIRE_IMMOBILIER',
  label: 'Intermédiaire Immobilier',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__PERSONNE__PHYSIQUE__INTERMEDIAIRE_IMMOBILIER',
  specificTypes: ['PERSONNE', 'PHYSIQUE', 'INTERMEDIAIRE_IMMOBILIER'],
  type: 'RECORD'
};
