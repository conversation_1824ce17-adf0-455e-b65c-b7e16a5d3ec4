// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationI3FImmobilierVefaReservation: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          id: 'quotites',
          label: "Quotités d'acquisition des réservataires",
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'plan_vente_personnalise',
          label: 'Le réservataire souhaite-t-il un plan de vente personnalisé ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'vente_tma',
          label: 'La réservation comporte-t-elle une procédure de Travaux Modificatifs Acquéreurs ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'vente_substitution',
          label: 'La réservation prévoit-elle une faculté de substitution ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'vente_notification',
          label: "Le Réservataire souhaite-t-il autoriser l'envoi de notifications dématérialisées ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'investissement_principal',
              label: 'Acquérir une résidence principale ou secondaire'
            },
            {
              label: "Un investissement locatif sans bénéficier d'un dispositif fiscal particulier ",
              id: 'investissement_locatif'
            },
            {
              id: 'investissement_locatif_fiscal_malraux',
              label: 'Un investissement locatif - dispositif Malraux'
            },
            {
              id: 'investissement_locatif_fiscal_censi',
              label: 'Un investissement locatif - dispositif Censi-Bouvard'
            }
          ],
          id: 'programme_investissement',
          label: 'Le bien pourra être acquis pour',
          type: 'SELECT'
        },
        {
          choices: [
            {
              label: 'Acquérir une résidence principale ou secondaire sans taux réduit',
              id: 'investissement_social_residence_principale'
            },
            {
              label: 'Acquérir une résidence principale au taux réduit de 5,5%',
              id: 'investissement_social_tva'
            },
            {
              label: 'Un investissement locatif dans le logement social sans dispositif fiscal',
              id: 'investissement_social_locatif_social'
            },
            {
              id: 'investissement_locatif_social_fiscal_malraux',
              label: 'Un investissement locatif - dispositif Malraux'
            },
            {
              id: 'investissement_locatif_social_fiscal_censi',
              label: 'Un investissement locatif - dispositif Censi-Bouvard'
            }
          ],
          id: 'programme_investissement_social',
          label: 'Le bien pourra être acquis pour',
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'vente_tva_20',
              label: '20 %'
            },
            {
              id: 'vente_tva_5',
              label: '5,5 %'
            }
          ],
          id: 'vente_tva',
          label: 'Taux de TVA applicable',
          type: 'SELECT'
        },
        {
          children: [
            {
              id: 'vente_pinel_montant',
              label: 'Montant des frais des intermédiaires',
              type: 'PRICE'
            },
            {
              id: 'vente_pinel_pourcentage',
              label: 'Pourcentage du prix de revient',
              type: 'NUMBER'
            }
          ],
          conditions: [
            [
              {
                id: 'programme_investissement',
                value: 'investissement_locatif_fiscal_pinel',
                type: 'EQUALS'
              }
            ],
            [
              {
                id: 'programme_investissement_social',
                value: 'investissement_social_locatif_fiscal_pinel',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'f6ef7ac7_8934_4e01_bee1_125dde06ae3b',
          label: 'Dispositif PINEL :',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'vente_notaire_programme',
              label: 'Souhaite se faire représenter par le notaire du programme'
            },
            {
              id: 'vente_notaire_reservataire',
              label: 'Souhaite se faire représenter par son notaire'
            },
            {
              id: 'vente_notaire_indecis',
              label: 'Souhaite communiquer cette information plus tard'
            }
          ],
          id: 'vente_notaire_reservataire',
          label: 'Le Réservataire',
          type: 'SELECT'
        },
        {
          children: [
            {
              id: 'notaire_reservataire_etude',
              label: "Nom de l'étude",
              type: 'TEXT'
            },
            {
              id: 'notaire_reservataire_prenom',
              label: 'Prénom du notaire',
              type: 'TEXT'
            },
            {
              id: 'notaire_reservataire_nom',
              label: 'Nom du notaire',
              type: 'TEXT'
            },
            {
              id: 'notaire_reservataire_adresse',
              label: 'Adresse du notaire',
              type: 'ADDRESS'
            },
            {
              id: 'notaire_reservataire_telephone',
              label: 'Telephone du notaire',
              type: 'PHONE'
            },
            {
              id: 'notaire_reservataire_email',
              label: 'Email du notaire',
              type: 'EMAIL'
            }
          ],
          conditions: [
            [
              {
                id: 'vente_notaire_reservataire',
                value: 'vente_notaire_reservataire',
                type: 'EQUALS'
              }
            ]
          ],
          id: '16a98a94_3477_496f_8bd9_11dbf95a3b8f',
          label: 'Information sur le notaire',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'vente_offre',
          label: 'Une offre commerciale est-elle proposée au Réservataire ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'vente_offre',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'vente_offre_descriptif',
          label: "Décrivez l'offre",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'vente_electronique',
          label: 'Le contrat de réservation est-il signé de manière électronique ?',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'vente_professionnel',
          label: 'Le Réservataire est il considéré comme un professionnel ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'vente_date',
          label: "Date d'expiration du contrat de Réservation",
          type: 'DATE'
        },
        {
          conditions: [
            [
              {
                id: 'emprunt_statut',
                recordPath: ['OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION__CONTRAT_RESERVATION', 'FINANCEMENT', '0'],
                type: 'EQUALS',
                value: 'non'
              },
              {
                id: 'vente_electronique',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'renonciation_financement_reservation',
          label: 'Renonciation financement',
          templateId: 'renonciationFinancement.pdf',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'negociation_statut',
          label: 'Un intermédiaire immobilier est-il intervenu dans les négociations ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'negociation_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'negociation_nom',
          label: 'Nom de la société',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'negociation_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'negociation_adresse',
          label: 'Adresse de la société',
          type: 'ADDRESS'
        }
      ],
      id: '812c9592_fd6a_4296_9ee3_1da23efc34b6',
      label: 'Information sur la réservation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'frais_copro_acquereur_copro',
              label: "Les frais d'actes de vente et de mise en copropriété sont à la charge de l'Acquéreur"
            },
            {
              id: 'frais_copro_acquereur',
              label:
                "Seuls les frais d'acte de vente sont à la charge de l'Acquéreur, sans les frais de mise en copropriété"
            },
            {
              id: 'frais_copro_vendeur_total',
              label: "Les frais d'acte de vente sont à la charge du Vendeur ; acte en main"
            },
            {
              id: 'frais_copro_vendeur',
              label:
                "Les frais d'acte de vente sont à la charge du Vendeur, mais les frais de mise en copropriété sont à la charge de l'Acquéreur"
            }
          ],
          id: 'frais_copro',
          label: "Modalité de paiement des frais d'acte",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'frais_indi_acquereur',
              label: "Les frais d'acte sont à la charge de l'Acquéreur"
            },
            {
              id: 'frais_indi_vendeur',
              label: "Les frais d'acte sont à la charge du Vendeur, acte en main"
            }
          ],
          id: 'frais_indi',
          label: "Modalité de paiement des frais d'acte",
          type: 'SELECT'
        }
      ],
      id: 'dfaa3b45_d7c1_4342_88d5_b18e612518f4',
      label: "Frais d'acte",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'avenant_date_contrat_initial',
          label: 'Date de signature du contrat initial',
          type: 'DATE'
        },
        {
          choices: [
            {
              id: 'prix',
              label: 'Modification du prix'
            },
            {
              id: 'delai',
              label: 'Modification du délai de signature'
            },
            {
              id: 'libre',
              label: 'Modification libre'
            }
          ],
          id: 'avenant_motifs',
          label: "Motifs de l'avenant",
          multiple: true,
          type: 'SELECT'
        },
        {
          id: 'avenant_prix',
          label: 'Nouveau prix de vente',
          type: 'PRICE'
        },
        {
          id: 'avenant_date',
          label: 'Nouvelle date extrême de signature',
          type: 'DATE'
        },
        {
          id: 'avenant_libre',
          label: 'Modification libre',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'avenant_electronique',
          label: 'Signature électronique',
          type: 'SELECT-BINARY'
        }
      ],
      id: '9e1758kl_po45_pm21_1zer_0909a660fi54',
      label: 'Avenant',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'accord_enquete',
          label: 'Le réservataire autorise la Coopérative à lui adresser toute information ou enquête',
          type: 'SELECT-BINARY'
        }
      ],
      id: 'ba62bd47_99ad_47d5_81d6_2e25bd33ddc8',
      label: 'Bulletin de souscription',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION',
  label: 'Réservation VEFA',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__I3F__IMMOBILIER__VEFA_RESERVATION',
  specificTypes: ['OPERATION', 'I3F', 'IMMOBILIER', 'VEFA_RESERVATION'],
  type: 'RECORD'
};
