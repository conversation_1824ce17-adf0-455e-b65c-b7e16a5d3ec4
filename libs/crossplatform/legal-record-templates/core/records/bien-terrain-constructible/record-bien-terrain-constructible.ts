// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordBienTerrainConstructible: LegalRecordTemplate = {
  config: {
    tags: {
      programme_superficie: {
        questionId: 'programme_superficie',
        format: 'AREA',
        order: 0
      }
    },
    recordLinks: [
      {
        id: 'LOTISSEMENT',
        specificTypes: ['COMPOSITION', 'LOTISSEMENT'],
        label: 'Lotissement',
        constraints: {
          max: 1,
          min: 0
        },
        creationQuestion: {
          choices: [
            {
              id: 'other',
              label: 'Oui'
            },
            {
              id: 'empty',
              label: 'Non'
            }
          ],
          label: "Présence d'un lotissement"
        }
      }
    ],
    search: ['adresse'],
    duplicate: ['adresse']
  },
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'existence_cadastre',
          label: 'La parcelle possède des références cadastrales propres',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'existence_cadastre',
                value: 'non',
                type: 'DIFFERENT'
              }
            ]
          ],
          id: 'cadastre_section_libre',
          label: 'Section cadastre',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'existence_cadastre',
                value: 'non',
                type: 'DIFFERENT'
              }
            ]
          ],
          id: 'cadastre_numero_libre',
          label: 'Numéro cadastre',
          type: 'TEXT'
        },
        {
          id: 'numero_lot',
          label: 'Numéro de lot',
          type: 'TEXT'
        },
        {
          id: 'programme_superficie',
          label: 'Superficie du Bien',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'mandat_superficie_mandant',
              label: 'Le Mandant'
            },
            {
              id: 'mandat_superficie_mandataire',
              label: 'Le Mandataire'
            },
            {
              id: 'mandat_superficie_diagnostiqueur',
              label: 'Un Diagnostiqueur'
            }
          ],
          id: 'mandat_superficie_fourniture',
          label: 'La superficie est établie par',
          type: 'SELECT'
        },
        {
          filters: {
            qualificationQuestions: true,
            recordOnly: true
          },
          id: 'adresse',
          label: 'Adresse du bien',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'terrain_bornage',
          label: 'Bornage du terrain',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'terrain_bornage_nom',
              label: 'Nom du géomètre-expert réalisant le bornage',
              type: 'TEXT'
            },
            {
              id: 'terrain_bornage_adresse',
              label: 'Adresse du géomètre-expert',
              type: 'ADDRESS'
            },
            {
              id: 'terrain_bornage_date',
              label: 'Date du bornage',
              type: 'DATE'
            }
          ],
          conditions: [
            [
              {
                id: 'terrain_bornage',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'b9b5bedf_ae2d_48d4_bead_5517d1a3045c',
          label: 'CONDITION_BLOCK_Bornage',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              id: 'cadastre_parcelle_libre',
              label: 'Parcelle cadastrale',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelle_section_libre',
              label: 'Section cadastrale',
              type: 'TEXT'
            },
            {
              id: 'cadastre_parcelle_numero_libre',
              label: 'Numero cadastral',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'presence_volume',
              label: 'Volume',
              type: 'SELECT-BINARY'
            },
            {
              id: 'numero_lot_volume',
              label: 'Numéro de volume',
              type: 'TEXT'
            },
            {
              id: 'quartier',
              label: 'Quartier',
              type: 'TEXT'
            },
            {
              children: [
                {
                  id: 'cadastre_parcelles_prefixe',
                  label: 'Préfixe',
                  type: 'TEXT'
                },
                {
                  id: 'cadastre_parcelles_section',
                  label: 'Section',
                  type: 'TEXT'
                },
                {
                  id: 'cadastre_parcelles_parcelle',
                  label: 'Parcelle',
                  type: 'TEXT'
                },
                {
                  id: 'cadastre_parcelles_lieudit',
                  label: 'Lieudit',
                  type: 'TEXT'
                },
                {
                  id: 'cadastre_parcelles_h',
                  label: 'Hectare',
                  type: 'NUMBER'
                },
                {
                  id: 'cadastre_parcelles_a',
                  label: 'Are',
                  type: 'NUMBER'
                },
                {
                  id: 'cadastre_parcelles_c',
                  label: 'Centiare',
                  type: 'NUMBER'
                },
                {
                  id: 'cadastre_parcelles_superficie',
                  label: 'Superficie',
                  suffix: 'm2',
                  type: 'NUMBER'
                },
                {
                  id: 'cadastre_parcelles_cadastre_non_renove',
                  label: 'Référence cadastre non rénové',
                  type: 'TEXT'
                }
              ],
              id: 'cadastre_parcelles',
              label: 'Ajouter les références cadastrales',
              repetition: {
                label: [
                  {
                    type: 'TEXT',
                    value: 'Section '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'cadastre_parcelles_section'
                  },
                  {
                    type: 'TEXT',
                    value: ' n° '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'cadastre_parcelles_parcelle'
                  }
                ],
                max: 1000,
                min: 1,
                type: 'REF_CADASTRALES'
              },
              type: 'REPEAT'
            }
          ],
          id: '29b5fddd_0ad0_4eb9_b80f_8519d99a7de3',
          label: 'Description du bien',
          type: 'CATEGORY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'designation_statut',
          label: 'Ajouter une désignation supplémentaire du Bien',
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'designation',
          label: 'Désignation du bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'terrain_division',
          label: "Le terrain fait-il l'objet d'une division ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cadastre_division',
          label: "Le bien provient-il de la division d'une parcelle plus grande ?",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cadastre_division_a_venir',
          label: 'Division cadastrale',
          type: 'SELECT-BINARY'
        },
        {
          id: 'cadastre_division_parcelle_mere',
          label: 'Parcelle mère de la division',
          type: 'TEXT'
        },
        {
          id: 'cadastre_division_geometre_nom',
          label: 'Nom du Géomètre ayant effectué la division',
          type: 'TEXT'
        },
        {
          id: 'cadastre_division_geometre_adresse',
          label: 'Adresse du Géomètre ayant effectué la division',
          type: 'ADDRESS'
        },
        {
          id: 'designation_avenant',
          label: 'Désignation modifiée du bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'viabilisation',
          label: 'Viabilisation du terrain',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'vendeur',
              label: 'du Vendeur'
            },
            {
              id: 'acquereur',
              label: "de l'Acquereur"
            }
          ],
          id: 'viabilisation_charge',
          label: 'La viabilisation du terrain sera à la charge',
          type: 'SELECT'
        },
        {
          id: 'surface_plancher',
          label: 'Surface de plancher maximale constructible',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          id: 'emprise_sol',
          label: 'Emprise au sol totale',
          suffix: 'm2',
          type: 'NUMBER'
        },
        {
          description:
            'décrire le terrain en reprenant ses éléments caractéristiques qui figurent au plan de bornage (arbres, clôtures, futaies, murs mitoyens, cours d’eau, etc.)',
          id: 'description_libre',
          label: 'Description du terrain',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                  'OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP'
                ]
              }
            ]
          ],
          id: 'designation_precisions',
          label: 'Ajouter des précisions sur la désignation du Bien',
          type: 'SELECT-BINARY'
        },
        {
          id: 'designation_precisions_texte',
          label: 'Précisions sur la désignation du Bien',
          type: 'TEXTAREA'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cuve_stockage_petrolier',
          label: 'Cuve de stockage de produits pétroliers',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'monument_historique',
          label: 'Bien situé dans un périmètre de protection des monuments historiques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'proximite_activites_agricoles',
          label: "Proximité d'activités agricoles - industrielles - artisanales - commerciales",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'espace_boise_classe',
          label: 'Espace boisé classé',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'sepulture',
          label: 'Présence de sépulture - cimetière',
          type: 'SELECT-BINARY'
        }
      ],
      id: '299a271d_1988_4910_85a8_d4703015cd9c',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'occupation_statut',
          label: 'Bien actuellement loué',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [],
                  id: 'location_bail_liste_location_bail_type',
                  label: 'Type de bail',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'location_bail_liste_location_bail_type',
                        value: '',
                        type: 'EQUALS'
                      },
                      {
                        id: 'occupation_statut',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'location_bail_liste_location_bail_type_autre',
                  label: 'Précisez le type de bail',
                  type: 'TEXT'
                },
                {
                  id: 'location_bail_liste_location_bail_date_signature',
                  label: 'Date de signature du bail',
                  type: 'DATE'
                },
                {
                  id: 'location_bail_liste_location_bail_date_expiration',
                  label: "Date d'expiration du bail",
                  type: 'DATE'
                },
                {
                  id: 'location_bail_liste_location_bail_locataires',
                  label: 'Liste des locataires',
                  type: 'TEXTAREA'
                }
              ],
              id: 'location_bail_liste',
              label: 'Baux',
              repetition: {
                label: [
                  {
                    type: 'VARIABLE',
                    value: 'location_bail_liste_location_bail_locataires'
                  }
                ],
                max: 1000,
                min: 1
              },
              type: 'REPEAT'
            },
            {
              choices: [],
              id: 'location_partielle_statut',
              label: 'La location porte',
              type: 'SELECT'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'contrat_bail',
              label: 'Contrat de bail',
              type: 'UPLOAD'
            }
          ],
          conditions: [
            [
              {
                id: 'occupation_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: '359d9fa7_4198_4072_aea2_6633fb99c050',
          label: 'Information sur les baux',
          type: 'CATEGORY'
        }
      ],
      id: 'f2b7413e_3b64_4e0b_a161_0f995c194848',
      label: 'Occupation du bien',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              id: 'origine_propriete_origine_proprietaire',
              label: 'Noms des propriétaires et biens concernés par cette origine de propriété',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'origine_propriete_acquisition',
                  label: 'Acquisition'
                },
                {
                  id: 'origine_propriete_donation',
                  label: 'Donation'
                },
                {
                  id: 'origine_propriete_succession',
                  label: 'Succession'
                },
                {
                  id: 'origine_propriete_partage',
                  label: 'Partage - Licitation'
                },
                {
                  id: 'origine_propriete_echange',
                  label: 'Echange'
                },
                {
                  id: 'origine_propriete_adjudication',
                  label: 'Adjudication - Vente aux enchères'
                },
                {
                  id: 'origine_propriete_remembrement',
                  label: 'Remembrement'
                }
              ],
              id: 'origine_propriete_origine_liste',
              label: 'Le bien a été reçu par',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'origine_propriete_acquisition',
                  label: 'Acquisition'
                },
                {
                  id: 'origine_propriete_vefa',
                  label: 'VEFA'
                },
                {
                  id: 'origine_propriete_donation',
                  label: 'Donation'
                },
                {
                  id: 'origine_propriete_succession',
                  label: 'Succession'
                },
                {
                  id: 'origine_propriete_partage',
                  label: 'Partage - Licitation'
                },
                {
                  id: 'origine_propriete_echange',
                  label: 'Echange'
                },
                {
                  id: 'origine_propriete_adjudication',
                  label: 'Adjudication - Vente aux enchères'
                },
                {
                  id: 'origine_propriete_remembrement',
                  label: 'Remembrement'
                }
              ],
              id: 'origine_propriete_origine_liste_giboire',
              label: 'Le bien a été reçu par',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_donation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_donation_unique',
              label: 'Propriétaire enfant unique',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'origine_propriete_origine_donation_donateur_decede',
              label: 'Donateur décédé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_donation_unique',
                    value: 'non',
                    type: 'EQUALS'
                  },
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_donation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_donation_deces',
              label: 'Succession du donateur reglée',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_succession_statut',
              label: 'Succession déjà réglée',
              type: 'SELECT-BINARY'
            },
            {
              id: 'origine_propriete_origine_notaire_nom',
              label: 'Nom du notaire',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_notaire_ville',
              label: 'Lieu de résidence du notaire',
              type: 'TEXT',
              uppercase: 'WORD'
            },
            {
              id: 'origine_propriete_origine_defunt_prenom',
              label: 'Prénom du défunt',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_defunt_nom',
              label: 'Nom du défunt',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_defunt_date',
              label: 'Date du décès',
              type: 'DATE'
            },
            {
              id: 'origine_propriete_origine_date',
              label: "Date de l'acte",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'origine_propriete_origine_publication',
              label: 'Acte publié',
              type: 'SELECT-BINARY'
            },
            {
              id: 'origine_propriete_origine_spf',
              label: 'Service de publicité foncière',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_spf_date',
              label: 'Date de publication',
              type: 'DATE'
            },
            {
              id: 'origine_propriete_origine_volume',
              label: 'Volume',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_volume_numero',
              label: 'Numéro de Volume',
              type: 'TEXT'
            },
            {
              id: 'origine_propriete_origine_annee_construction',
              label: 'Année des constructions:',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_adjudication',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_origine_tribunal',
              label: 'Ville du tribunal',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_acquisition',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_acquisition',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_echange',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_echange',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_donation',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_donation',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_partage',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_partage',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  },
                  {
                    id: 'origine_propriete_origine_succession_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_succession_pas_reglee',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_succession',
                    type: 'EQUALS'
                  },
                  {
                    id: 'origine_propriete_origine_succession_statut',
                    value: 'non',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_succession',
              label: 'Titre de propriété - Attestation de dévolution',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_adjudication',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_adjudication',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            },
            {
              conditions: [
                [
                  {
                    id: 'origine_propriete_origine_liste',
                    value: 'origine_propriete_remembrement',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'origine_propriete_op_remembrement',
              label: 'Titre de propriété',
              type: 'UPLOAD'
            }
          ],
          id: 'origine_propriete',
          label: 'Ajouter une origine de propriété',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: ' '
              },
              {
                type: 'VARIABLE',
                value: 'origine_propriete_origine_liste'
              },
              {
                type: 'TEXT',
                value: ' - '
              },
              {
                type: 'VARIABLE',
                value: 'origine_propriete_origine_date'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'servitudes',
          label: 'Servitudes',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'servitudes_note',
          label: 'Les servitudes sont-elles reprises dans une note annexée ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'servitude_type',
          label: 'Type de servitude',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'servitudes',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'servitudes_liste',
          label: 'Liste des servitudes',
          type: 'TEXTAREA'
        },
        {
          id: 'servitude_notaire_nom',
          label: 'Nom du notaire ayant reçu la servitude',
          type: 'TEXT'
        },
        {
          id: 'servitude_notaire_ville',
          label: 'Ville du notaire',
          type: 'TEXT'
        },
        {
          id: 'servitude_notaire_date',
          label: 'Date de constitution de la servitude',
          type: 'DATE'
        },
        {
          id: 'montant_taxe_fonciere',
          label: 'Montant de la dernière taxe foncière',
          type: 'PRICE'
        },
        {
          id: 'montant_taxe_fonciere_cfp',
          label: 'Montant de la dernière taxe foncière',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'annee_taxe_fonciere',
          label: 'Année de la dernière taxe foncière',
          type: 'YEAR'
        }
      ],
      id: 'e4de11b4_f90c_42d7_9ae7_6d93df4b6d0a',
      label: 'Origine de propriété',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'terrain_bornage',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'plan_bornage',
          label: 'Plan de bornage',
          type: 'UPLOAD'
        },
        {
          conditions: [
            [
              {
                id: 'servitudes',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'servitude_acte',
          label: 'Rappel de servitude',
          type: 'UPLOAD'
        },
        {
          filters: {
            recordOnly: true,
            mustBeExcludedInOperationConfig: true
          },
          id: 'external_cadastre_situation',
          label: 'Plan Cadastral',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'attestation_surface_plancher',
          label: 'Attestation de surface de plancher',
          type: 'UPLOAD'
        }
      ],
      id: '88274099_b6ce_4164_a2ad_1c4bd9c9ae7d',
      label: 'Documents Généraux',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_a_jour',
          label: 'Les diagnostics sont à jour',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostics_resultats',
          label: 'Le résultat des diagnostics est indiqué dans le compromis',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostics_techniques_termites_commune',
          label: 'Commune concernée par les termites',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'diagnostic_termites_informatif_effectue',
          label: 'Diagnostic termites tout de même réalisé',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'diag_mls_present',
              label: 'Le Mandant remet le diagnostic à la signature du mandat'
            },
            {
              id: 'diag_mls_mandant',
              label: 'Le Mandant établit le diagnostic avant le compromis'
            },
            {
              id: 'diag_mls_mandataire',
              label: "Le Mandant charge le Mandataire d'établir le diagnostic"
            }
          ],
          id: 'termites_mandat_mls',
          label: 'Concernant le diagnostic termites',
          type: 'SELECT'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_termite_details_status_diagnostic_termite',
              label: 'Le diagnostic a-t-il été effectué ?',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'diagnostic_termites_details_infos_diagnostiqueur_termites_name',
                  label: 'Nom du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostic_termites_details_infos_diagnostiqueur_termites_societe',
                  label: 'Société du diagnostiqueur',
                  type: 'TEXT'
                },
                {
                  id: 'diagnostic_termites_details_infos_diagnostiqueur_termites_address',
                  label: 'Adresse du diagnostiqueur',
                  type: 'ADDRESS'
                },
                {
                  id: 'diagnostic_termites_details_date_diagnostic_termites',
                  label: 'Date du diagnostic',
                  type: 'DATE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_termites_details_termites',
                  label: 'Contamination par les termites',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'diagnostic_termites_details_termites_resultat',
                  label: 'Résultat du diagnostic termite',
                  type: 'TEXTAREA'
                },
                {
                  filters: {
                    mustBeIncludedInOperationConfig: true
                  },
                  id: 'attestation_diagnostiqueur',
                  label: 'Attestation Diagnostiqueur',
                  type: 'UPLOAD'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'diagnostics_techniques_termites_commune',
                        value: 'oui',
                        type: 'EQUALS'
                      }
                    ],
                    [
                      {
                        type: 'EQUALS_CONTRACT_MODELS',
                        model: ['EFFICITY__TRANSACTION__PREPARATION_COMPROMIS']
                      }
                    ]
                  ],
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'diagnostic_termite',
                  label: 'Diagnostic Termite',
                  type: 'UPLOAD'
                }
              ],
              id: 'fe5236be_bf4d_4d7b_8242_99fa96e82540',
              label: 'CONDITION_BLOCK_Informations sur le diagnostiqueur',
              type: 'CONDITION_BLOCK'
            }
          ],
          id: '26a3203f_a608_4cb2_a31d_99cd4f85e897',
          label: 'CONDITION_BLOCK_Zone concernée par les termites',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_termites_details_termites_informatif',
              label: 'Contamination par les termites',
              type: 'SELECT-BINARY'
            }
          ],
          id: '2baa19dd_1a71_48a6_8bfc_cb07fed407df',
          label: 'CONDITION_BLOCK_Zone non concernée par les termites',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              description:
                "Énoncer ici éventuellement les études environnementales et les mesures de dépollution dont le terrain fait l'objet",
              id: 'icpe_declaration',
              label: 'Déclaration sur les installations classées',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'zone_erp_statut',
              label: "La parcelle est située dans un périmètre d'un plan de prévention des risques",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'zone_erp_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'erp_date_arrete',
              label: "Date de l'arrêté préfectoral approuvant les risques",
              type: 'DATE'
            },
            {
              conditions: [
                [
                  {
                    id: 'zone_erp_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'erp_zone_risques_liste',
              label: 'Risques concernés dans cette zone',
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'zone_erp_statut',
                    value: 'non',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'erp_prefecture_date',
              label: "Date de l'état des risques délivré par la préfécture",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'erp_pollution_sol',
              label: "Bien situé dans un secteur d'information sur les sols",
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'erp_pollution_sol',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'pollution_sol_description',
              label: "Description du secteur d'information sur les sols",
              type: 'TEXT'
            },
            {
              id: 'pollution_sol_description_date',
              label: "Date de l'arrêté préfectoral définissant le secteur",
              type: 'DATE'
            },
            {
              id: 'zone_bruit_resultat',
              label: "Résultat de l'état des nuisances sonores",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'icpe_statut',
              label: 'Le terrain a-t-il supporté des installations classées (polluantes) ?',
              type: 'SELECT-BINARY'
            },
            {
              id: 'icpe_description',
              label: "Type d'installation classée exploitée",
              type: 'TEXT'
            },
            {
              id: 'icpe_autorisation_date',
              label: "Date d'autorisation d'exploitation par le préfet",
              type: 'DATE'
            },
            {
              id: 'icpe_danger',
              label: "Dangers et inconvénients émanant de l'exploitation",
              type: 'TEXT'
            },
            {
              id: 'icpe_erp_redacteur',
              label: "Organisme délivrant l'état de la pollution des sols",
              type: 'TEXT'
            },
            {
              id: 'icpe_erp_resultat',
              label: "Résultat de l'état de pollution des sols",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'icpe_vendeur_exploitant',
              label: "Le Vendeur était-il l'exploitant de cette installation classée ?",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'icpe_radioactif',
              label: 'Cette activité a-t-elle entrainé le stockage de substances chimiques ou radioactives ?',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'icpe_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'erp_sol',
              label: 'Etat de pollution des sols',
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'diag_mls_present',
                  label: 'Le Mandant remet le diagnostic à la signature du mandat'
                },
                {
                  id: 'diag_mls_mandant',
                  label: 'Le Mandant établit le diagnostic avant le compromis'
                },
                {
                  id: 'diag_mls_mandataire',
                  label: "Le Mandant charge le Mandataire d'établir le diagnostic"
                }
              ],
              id: 'erp_mandat_mls',
              label: "Concernant l'état des risques",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'erp_global',
              label:
                'Le bien est-il situé dans une zone couverte par un plan de prévention des risques naturels, miniers ou technologiques?',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_environnemental_erp_indemnite_assurance',
              label: "Sinistre d'origine catastrophe naturelle survenu sur le bien",
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: "N'est pas concerné par un plan de prévention des risques naturels",
                      id: 'aucun_plan'
                    },
                    {
                      label: "Est situé Dans le périmètre d'un plan de prévention des risques naturels prescrits",
                      id: 'plan_prescrit'
                    },
                    {
                      label: "Est situé Dans le périmètre d'un plan de prévention des risques naturels anticipés",
                      id: 'plan_anticipe'
                    },
                    {
                      label: "Est situé Dans le périmètre d'un plan de prévention des risques naturels approuvés",
                      id: 'plan_approuve'
                    }
                  ],
                  id: 'ppr_naturels_ppr_naturels_liste',
                  label: 'Risques naturels',
                  multiple: true,
                  type: 'SELECT'
                }
              ],
              id: 'efb9213e_d00d_4ab3_87aa_10ca29963fc6',
              label: 'CONDITION_BLOCK_Plan de prévention des risques naturels',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: "N'est pas concerné par un plan de prévention des risques miniers",
                      id: 'aucun_plan'
                    },
                    {
                      label: "Est situé Dans le périmètre d'un plan de prévention des risques miniers prescrits",
                      id: 'plan_prescrit'
                    },
                    {
                      label: "Est situé Dans le périmètre d'un plan de prévention des risques miniers anticipés",
                      id: 'plan_anticipe'
                    },
                    {
                      label: "Est situé Dans le périmètre d'un plan de prévention des risques miniers approuvés",
                      id: 'plan_approuve'
                    }
                  ],
                  id: 'ppr_miniers_ppr_miniers_liste',
                  label: 'Risques miniers',
                  multiple: true,
                  type: 'SELECT'
                }
              ],
              id: '9c537ece_d8e9_4c74_b990_a63cbc545a58',
              label: 'CONDITION_BLOCK_Plan de prévention des risques miniers',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: "N'est pas concerné par un plan de prévention des risques technologiques",
                      id: 'aucun_plan'
                    },
                    {
                      label: "Est situé Dans le périmètre d'un plan de prévention des risques technologiques prescrits",
                      id: 'plan_prescrit'
                    },
                    {
                      label: "Est situé Dans le périmètre d'un plan de prévention des risques technologiques anticipés",
                      id: 'plan_anticipe'
                    },
                    {
                      label: "Est situé Dans le périmètre d'un plan de prévention des risques technologiques approuvés",
                      id: 'plan_approuve'
                    }
                  ],
                  id: 'ppr_technologiques_ppr_technologiques_liste',
                  label: 'Risques technologiques',
                  multiple: true,
                  type: 'SELECT'
                }
              ],
              id: 'd748a3b0_14dc_4206_8416_288cefd561e4',
              label: 'CONDITION_BLOCK_Plan de prévention des risques technologiques',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  id: 'zone_sismicite_zone_sismicite_classe',
                  label: 'Zone de sismicité',
                  type: 'NUMBER'
                }
              ],
              id: 'f9aa46ab_fda7_4a72_b2a9_8b11b6ed2896',
              label: 'CONDITION_BLOCK_Zone de sismicité',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'potentiel_radon_potentiel_radon_classe',
                  label: 'Commune à potentiel radon classée niveau 3',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'potentiel_radon_potentiel_radon_classe_libre',
                  label: 'La commune se trouve en zone RADON',
                  type: 'TEXT'
                }
              ],
              id: '872902f1_0d48_4d91_a87f_f105769255b1',
              label: 'CONDITION_BLOCK_Potentiel Radon',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_retrait_cote_commune',
                  label: 'Commune exposée au retrait des côtes',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'erp_retrait_cote_bien',
                  label: 'Bien exposé au retrait des côtes',
                  type: 'SELECT-BINARY'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'erp_retrait_cote_bien',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'erp_retrait_cote_bien_fiche',
                  label: 'Retrait de côtes - Extrait des prescriptions applicables',
                  type: 'UPLOAD'
                }
              ],
              id: '362bd14f_fe50_4e77_aadb_bddc3c038f5e',
              label: 'CONDITION_BLOCK_Retrait de côte',
              type: 'CONDITION_BLOCK'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'erp',
              label: 'Etat des risques',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'external_georisque',
              label: 'Georisques',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'carte_radon',
              label: 'Carte RADON',
              type: 'UPLOAD'
            },
            {
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'carte_peb',
              label: "Carte Plan d'exposition des bruits des aérodromes",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'faible',
                  label: "Zone d'exposition faible"
                },
                {
                  id: 'modere',
                  label: "Zone d'exposition modérée"
                },
                {
                  id: 'forte',
                  label: "Zone d'exposition forte"
                },
                {
                  id: 'inconnue',
                  label: "Zone d'exposition non classée"
                }
              ],
              id: 'zone_argiles_type',
              label: "Classement de la zone concernant l'aléa argile",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'zone_bruit',
              label: "Zone soumise à un plan d'exposition au bruit des aérodromes",
              type: 'SELECT-BINARY'
            },
            {
              id: 'plan_exposition_bruit_zonage',
              label: "Classement de la zone concernant le plan d'exposition au bruit des aérodromes",
              type: 'TEXT'
            },
            {
              conditions: [
                [
                  {
                    id: 'zone_bruit',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'plan_exposition_bruit',
              label: "Plan d'exposition au bruit des aérodromes",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'diagnostic_geotechnique_zone_concernee',
              label: 'Réalisation d’une étude géotechnique',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'geotechnique_zone_moyen',
                      label: 'Zone à risques moyens'
                    },
                    {
                      id: 'geotechnique_zone_fort',
                      label: 'Zone à risques forts'
                    }
                  ],
                  id: 'diagnostic_geotechnique_zone_type',
                  label: 'Type de zone',
                  type: 'SELECT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'diagnostic_geotechnique_statut',
                  label: "L'étude géotechnique a-t-elle été réalisée?",
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      id: 'diagnostic_geotechnique_date',
                      label: "Date de réalisation de l'étude géotechnique",
                      type: 'DATE'
                    },
                    {
                      id: 'diagnostic_geotechnique_diagnostiqueur_nom',
                      label: 'Nom du diagnostiqueur',
                      type: 'TEXT'
                    },
                    {
                      id: 'diagnostic_geotechnique_diagnostiqueur_adresse',
                      label: 'Adresse du diagnostiqueur',
                      type: 'ADDRESS'
                    },
                    {
                      id: 'etude_geotechnique',
                      label: 'Etude géotechnique',
                      type: 'UPLOAD'
                    }
                  ],
                  conditions: [
                    [
                      {
                        id: 'diagnostic_geotechnique_statut',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'diagnostic_geotechnique_zone_concernee',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ]
                  ],
                  id: 'ca14a2a6_e595_475a_9d6a_36ed18dd02b0',
                  label: 'Etude géotechnique',
                  type: 'CATEGORY'
                }
              ],
              conditions: [
                [
                  {
                    id: 'diagnostic_geotechnique_zone_concernee',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ]
              ],
              id: 'fe022090_b518_4a92_9a48_5e239533c29e',
              label: 'Zone concernée par une étude géotechnique',
              type: 'CATEGORY'
            }
          ],
          id: '37de4cf3_5a33_4427_823c_40646be4f5e0',
          label: 'Diagnostics environnementaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'obligation_debroussaillement_statut',
              label: 'Bien concerné par une obligation de débroussaillement',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'obligation_debroussaillement_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'obligation_debroussaillement_doc',
              label: 'Attestation de débroussaillement',
              type: 'UPLOAD'
            }
          ],
          id: '0b769dbd_cea0_4806_858e_d2920c02ca78',
          label: 'Obligation de débroussaillement',
          type: 'CATEGORY'
        }
      ],
      id: 'cedd394d_8051_4659_a43f_365d2e6f1d65',
      label: 'Diagnostic',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'visite_prix',
          label: 'Prix du bien',
          type: 'PRICE'
        },
        {
          id: 'visite_honoraires',
          label: 'Montant des honoraires',
          type: 'PRICE'
        },
        {
          id: 'programme_prix_vente',
          label: 'Prix de vente Hors Taxe',
          type: 'PRICE'
        },
        {
          id: 'programme_prix_vente_ttc',
          label: 'Prix de vente TTC',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'tva_5',
              label: '5,5 %'
            },
            {
              id: 'tva_20',
              label: '20 %'
            }
          ],
          id: 'programme_prix_tva',
          label: 'Taux de TVA applicable',
          type: 'SELECT'
        },
        {
          choices: [],
          id: 'programme_disponibilite',
          label: 'Disponibilité du bien',
          type: 'SELECT'
        },
        {
          id: 'programme_montant_honoraires_ttc',
          label: 'Montant des honoraires TTC',
          type: 'PRICE'
        },
        {
          filters: {
            mustBeExcludedInOperationConfig: true
          },
          id: 'taxe_fonciere',
          label: 'Taxe foncière',
          type: 'UPLOAD'
        },
        {
          id: 'plan_lot',
          label: 'Plan du lot',
          type: 'UPLOAD'
        },
        {
          id: 'certificat_surface_plancher',
          label: 'Certificat surface de plancher',
          type: 'UPLOAD'
        }
      ],
      id: '4457957b_d606_460d_b972_0a15c935170a',
      label: 'Informations sur la vente',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'belgique_revenu_cadastral',
          label: 'Revenu cadastral du bien',
          type: 'PRICE'
        }
      ],
      id: '19cb539e_8b22_4770_afb8_2d296c5c3d4f',
      label: 'Revenu cadastral',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes',
          label: 'Présence de servitudes',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes_titre',
          label: 'Servitudes contenue au titre de propriété',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_presence_servitudes_titre_liste',
          label: 'Liste des servitudes',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_presence_servitudes_vendeur',
          label: 'Servitudes octroyée par le Vendeur',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_presence_servitudes_vendeur_liste',
          label: 'Liste des servitudes',
          type: 'TEXT'
        }
      ],
      id: '5b96c05a_b705_4088_99a3_d79013a6bebd',
      label: 'Servitudes',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_publicitaire_presence',
          label: 'Panneau publicitaire',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_publicitaire_presence_preemption',
          label: 'Le contrat contient un droit de préemption',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_presence',
          label: 'Panneaux photovoltaïques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_proprietaire',
          label: 'Vendeur propriétaire des panneaux photovoltaïques',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_presence_vente',
          label: 'Panneaux compris dans la vente',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'belgique_panneau_photovoltaiques_certificat',
          label: 'Vendeur bénéficiaire de certificats verts',
          type: 'SELECT-BINARY'
        },
        {
          id: 'belgique_panneau_photovoltaiques_certificat_liste',
          label: 'Liste de certificats verts',
          type: 'TEXTAREA'
        }
      ],
      id: 'd2478dc6_ec62_42d3_9ab8_b64733f42100',
      label: 'Panneaux / Enseignes',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_diu_presence',
              label: 'Bien concerné par un DIU',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_diu_liste',
              label: 'Liste des travaux concernés',
              type: 'TEXTAREA'
            }
          ],
          id: '0954ea83_95c3_4071_b163_1f1415aaa44d',
          label: "Dossier d'intervention ultérieur",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_date_1981',
              label: "Installation électrique datant d'avant le 01/10/1981",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'PV de contrôle établi',
                  id: 'etabli'
                },
                {
                  label: 'PV de contrôle non établi',
                  id: 'non_etabli'
                },
                {
                  label: 'Dispense de contrôle',
                  id: 'dispense'
                }
              ],
              id: 'belgique_pv_controle_electricite_statut',
              label: "Contrôle de l'installation électrique",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_rapport',
              label: 'Rapport de visite remis',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_pv_controle_electricite_date',
              label: 'Date de réalisation du PV de contrôle',
              type: 'DATE'
            },
            {
              id: 'belgique_pv_controle_electricite_nom',
              label: 'Nom de la société ayant réalisé le PV',
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_controle_electricite_conformite',
              label: 'Installation conforme',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'Démolition du bâtiment',
                  id: 'demolition'
                },
                {
                  label: "Rénovation de l'installation",
                  id: 'renovation'
                }
              ],
              id: 'belgique_pv_controle_electricite_dispense_raison',
              label: 'Raison de la dispense',
              type: 'SELECT'
            }
          ],
          id: '78e5f278_1094_468b_a5e4_ac68e18f2f33',
          label: 'Electricité',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_amiante_presence',
              label: 'Amiante utilisée lors de la construction ou présence dans certains éléments',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_amiante_liste',
              label: "Situation de l'amiante dans le bien",
              type: 'TEXT'
            }
          ],
          id: '6fc89cc6_05de_415c_bc45_9613a87ba5f9',
          label: 'Amiante',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_peb_effectue',
              label: 'PEB effectué',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_peb_numero',
              label: 'Numéro du PEB',
              type: 'TEXT'
            },
            {
              id: 'belgique_peb_expert',
              label: "Nom de l'expert",
              type: 'TEXT'
            },
            {
              id: 'belgique_peb_date',
              label: "Date d'établissement du PEB",
              type: 'DATE'
            },
            {
              id: 'belgique_peb_classe',
              label: 'Classe énergétique du bien',
              type: 'TEXT'
            }
          ],
          id: '4b5faedf_fb13_4ff9_842e_31ae3b361152',
          label: 'Performance énergétique du bâtiment',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_toiture',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_toiture',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_toiture',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'e250613b_4add_403e_8332_3edd148de327',
              label: 'Toiture(s)',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_menuiserie',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_menuiserie',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_menuiserie',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '9a951431_e577_4f98_b74d_a98a96a6e3f7',
              label: 'Menuiserie extérieure',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_electrique',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_electrique',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_electrique',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '9f166a5b_b2bf_4d10_a064_e8db2c5a3c2e',
              label: 'Installation electrique',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_sanitaire',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_sanitaire',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_sanitaire',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '5cd0fc89_09a3_402f_ba75_a3cb1de47749',
              label: 'Sanitaires',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_chauffage',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_chauffage',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_chauffage',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: 'e6fe3372_bd5c_47ce_bf07_8c0f24f29b1c',
              label: 'Chauffage central',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_revetement_sol',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_revetement_sol',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_revetement_sol',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '036ae88f_96d1_4204_86b0_7afc3995b2a2',
              label: 'Revêtement sols',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      label: 'Neuf',
                      id: 'neuf'
                    },
                    {
                      label: 'Excellent',
                      id: 'excellent'
                    },
                    {
                      label: 'Remis à neuf',
                      id: 'remis_neuf'
                    },
                    {
                      label: 'Bon état',
                      id: 'bon_etat'
                    },
                    {
                      label: 'A rafraîchir',
                      id: 'rafraichir'
                    },
                    {
                      label: 'A rénover',
                      id: 'renover'
                    },
                    {
                      label: 'A réhabiliter',
                      id: 'rehabiliter'
                    },
                    {
                      label: 'A démolir',
                      id: 'demolir'
                    },
                    {
                      label: 'Autre',
                      id: 'autre'
                    }
                  ],
                  id: 'belgique_poste_revetement_mur',
                  label: 'Etat',
                  type: 'SELECT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'belgique_poste_revetement_mur',
                        value: 'autre',
                        type: 'EQUALS'
                      }
                    ]
                  ],
                  id: 'belgique_poste_autre_revetement_mur',
                  label: 'Autre état',
                  type: 'TEXT'
                }
              ],
              id: '5b8fe87c_ac9d_462f_8de1_3d59caa0bd8a',
              label: 'Revêtements murs',
              type: 'CATEGORY'
            }
          ],
          id: '32a3dc93_9dbe_4038_84f9_891b724c275e',
          label: 'Etat des Postes principaux',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_urbanisme_statut',
              label: "Bien concerné par un permis d'urbanisme",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_urbanisme_date',
              label: 'Date du permis',
              type: 'DATE'
            },
            {
              id: 'belgique_permis_urbanisme_lieu',
              label: 'Lieu de délivrance du permis',
              type: 'TEXT'
            },
            {
              id: 'belgique_permis_urbanisme_numero',
              label: 'Numéro de permis',
              type: 'TEXT'
            }
          ],
          id: '7dca7c8d_3ac2_4908_9641_ca7e8af274c6',
          label: "Permis d'urbanisme",
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_prime_wallonnie',
              label: 'Prime de la région Wallonne utilisée',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_prime_wallonnie_date',
              label: 'Date de la prime',
              type: 'DATE'
            },
            {
              id: 'belgique_prime_wallonnie_type',
              label: 'Type de la prime',
              type: 'TEXT'
            },
            {
              id: 'belgique_prime_wallonnie_montant',
              label: 'Montant de la prime',
              type: 'PRICE'
            }
          ],
          id: '02088962_fcb7_4196_a83a_92e7150cf64f',
          label: 'Prime',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_location',
              label: "Bien objet d'un permis de location",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_location_date',
              label: 'Date du permis de location',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_pv_logement_inoccupe',
              label: 'Bien objet d’un PV de constat de logement inoccupé',
              type: 'SELECT-BINARY'
            }
          ],
          id: '9b2f55be_b319_47bd_8d32_45a128fd6cbb',
          label: 'Habitation durable',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'belgique_usage_bien',
              label: 'Affectation du bien',
              type: 'TEXT'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_infraction_urbanistique',
                  label: 'Bien en infraction urbanistique ou environnementale',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_infraction_urbanistique_liste',
                  label: "Type d'infraction",
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_infraction_urbanistique_presence',
                  label: 'Infraction présente lors du transfert de propriété',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'caa39b53_91be_46c1_bc88_bcdd37d59d1f',
              label: 'Infraction',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_insalubrite',
                  label: 'Bien déclaré inhabitable ou insalubre',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'e4169b48_cc4d_4fa0_b532_32f50e659b3f',
              label: 'Insalubrité',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_inoccupe',
                  label: 'Immeuble déclaré inoccupé ou abandonné',
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'a352111e_cf56_4abf_bde6_789f675dbdd3',
              label: 'Inoccupation',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_alignement',
                  label: "Bien dans le périmètre d'un plan d'alignement",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'fd81ca83_72eb_486d_886d_a8880079c64e',
              label: 'Alignement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_travaux_presence',
                  label: 'Présence de travaux nécessitant un permis',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_responsabilite_decennale',
                  label: 'Bien concerné par la responsabilité décennale',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_travaux_juillet_2018',
                  label: 'Travaux soumis à permis délivré après le 1er juillet 2018',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      choices: [
                        {
                          id: 'non',
                          label: 'NON'
                        },
                        {
                          id: 'oui',
                          label: 'OUI'
                        }
                      ],
                      id: 'belgique_travaux_list_belgique_travaux_permis_obtenu',
                      label: 'Permis obtenu',
                      type: 'SELECT-BINARY'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_titre',
                      label: 'Titre des Travaux',
                      type: 'TEXT'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_description',
                      label: 'Description des travaux',
                      type: 'TEXTAREA'
                    },
                    {
                      id: 'belgique_travaux_list_belgique_travaux_date_achevement',
                      label: "Date d'achèvement des travaux",
                      type: 'DATE'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'belgique_travaux_list_belgique_travaux_permis_obtenu',
                            value: 'non',
                            type: 'EQUALS'
                          }
                        ]
                      ],
                      id: 'belgique_travaux_list_belgique_travaux_aministie',
                      label: 'Régime de régularisation / amnistie',
                      type: 'TEXTAREA'
                    }
                  ],
                  id: 'belgique_travaux_list',
                  label: 'Ajouter Travaux',
                  repetition: {
                    label: [
                      {
                        type: 'TEXT',
                        value: ' '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'belgique_travaux_list_belgique_travaux_titre'
                      },
                      {
                        type: 'TEXT',
                        value: ' - '
                      },
                      {
                        type: 'VARIABLE',
                        value: 'belgique_travaux_list_belgique_travaux_date_achevement'
                      }
                    ],
                    max: 1000,
                    min: 1
                  },
                  type: 'REPEAT'
                }
              ],
              id: '54fdbce5_591d_4eb7_9d07_f968eaf0c4cb',
              label: 'Travaux',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_statut',
                  label: 'Bien soumis à Lotissement',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_permis_delivrance',
                  label: "Permis d'urbanisation délivré",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_lotissement_notaire_nom',
                  label: "Notaire ayant reçu l'acte de division",
                  type: 'TEXT'
                },
                {
                  id: 'belgique_lotissement_notaire_date',
                  label: "Date de l'acte de division",
                  type: 'DATE'
                },
                {
                  id: 'belgique_lotissement_destination_vendu',
                  label: 'Destination du bien vendu',
                  type: 'TEXT'
                },
                {
                  id: 'belgique_lotissement_destination_conserver',
                  label: 'Destination du bien conservé par le vendeur',
                  type: 'TEXT'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_lotissement_cs_avis',
                  label: "Condition suspensive d'absence d'avis défavorable",
                  type: 'SELECT-BINARY'
                }
              ],
              id: 'c7d01ba0_ce48_487b_92fe_3e0cd31b90e4',
              label: 'Lotissement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_eau_usee_presence',
                  label: "Bien équipé d'un système d'épuration des eaux usées",
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_eau_usee_repartition_clause',
                  label: 'Ajouter la répartition des frais sur demande des intercommunales',
                  type: 'SELECT-BINARY'
                }
              ],
              id: '856ef9c6_84a9_4eca_a32a_e2525b685151',
              label: 'Equipement',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_zone_inondable',
                  label: 'Bien situé en zone inondable',
                  type: 'SELECT-BINARY'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_zone_inondable_inonde',
                  label: 'Bien a subi une inondation',
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_zone_inondable_etendue',
                  label: "Étendue de l'inondation",
                  type: 'TEXT'
                }
              ],
              id: '5452120f_6ad7_4e0d_8c7a_0f216df94e96',
              label: 'Zones inondables',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_mesure_expropriation',
                  label: "Mesure d'expropriation de l'immeuble",
                  type: 'SELECT-BINARY'
                }
              ],
              id: '9c2ac983_0bca_416e_8615_46efdda3009d',
              label: 'Expropriation',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'belgique_litige',
                  label: "Litige relatif à l'immeuble",
                  type: 'SELECT-BINARY'
                },
                {
                  id: 'belgique_litiges_liste',
                  label: 'Description des litiges',
                  type: 'TEXT'
                }
              ],
              id: '1e0322ef_6996_44de_9feb_d32376740860',
              label: 'Litige',
              type: 'CATEGORY'
            }
          ],
          id: 'c5bed9be_e280_49c7_b8e9_93da93a67b4b',
          label: 'Situation urbanistique',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  label: 'Raccordement antérieur au 1er Juin 2021',
                  id: 'raccordement_ante_2021'
                },
                {
                  label: 'Raccordement postérieur au 1er Juin 2021',
                  id: 'raccordement_post_2021'
                },
                {
                  label: 'Immeuble sur plan',
                  id: 'plan'
                },
                {
                  label: "Local où l'eau est fournie au public",
                  id: 'public'
                }
              ],
              id: 'belgique_certibeau_statut',
              label: 'Raccordement à la distribution publique de l’eau',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise',
              label: 'CertIBEau réalisé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_a_realiser',
              label: 'Le CertIBEau devra être réalisé',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_certibeau_resultat',
              label: 'Résultat du CertIBEau',
              type: 'TEXTAREA'
            },
            {
              id: 'belgique_certibeau_realise_date',
              label: "Date d'établissement du CertIBEau",
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise_conformite',
              label: 'CertIBEau délivré conforme',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_certibeau_realise_conformite_modification',
              label: "Modifications réalisés depuis l'établissement du CertIBEau",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_certibeau_realise_conformite_modification_liste',
              label: 'Liste des modifications intervenues',
              type: 'TEXTAREA'
            }
          ],
          id: '858b49a0_5fe5_4964_8bf6_234c04e33d03',
          label: 'CertIBeau',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_detecteur_incendie_statut',
              label: "Bien équipé de détecteur d'incendie",
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_detecteur_incendie_nombre',
              label: 'Nombre de détecteur',
              type: 'NUMBER'
            },
            {
              id: 'belgique_detecteur_incendie_pieces',
              label: 'Pièces équipées',
              type: 'TEXT'
            }
          ],
          id: '30920b09_4030_45d6_961a_3de0a5181fe8',
          label: 'Détecteur incendie',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_presence',
              label: "Présence d'une citerne",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'GAZ',
                  id: 'gaz'
                },
                {
                  label: 'MAZOUT',
                  id: 'mazout'
                }
              ],
              id: 'belgique_citerne_type',
              label: 'Type de citerne',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_capacite_3000',
              label: 'Capacité de la citerne supérieure à 3.000 litres',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'Aérienne',
                  id: 'aerienne'
                },
                {
                  label: 'Enterrée',
                  id: 'enterree'
                }
              ],
              id: 'belgique_citerne_enterree_aerienne',
              label: 'Situation de la citerne',
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_citerne_attestation',
              label: 'Attestation de conformité / certificat de visite de contrôle',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_citerne_mazout_capacite',
              label: 'Capacité de la citerne mazout',
              type: 'TEXT'
            }
          ],
          id: '5066199e_480c_4560_85d3_502ab6d26d5c',
          label: 'Citerne mazout / gaz',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_environnement_statut',
              label: "Bien objet d'un permis d'environnement / déclaration de classe 3",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  label: 'PERMIS',
                  id: 'permis'
                },
                {
                  label: 'DECLARATION',
                  id: 'declaration'
                }
              ],
              id: 'belgique_permis_environnement_statut_type',
              label: 'Type',
              type: 'SELECT-BINARY'
            },
            {
              id: 'belgique_permis_environnement_type',
              label: "Objet du permis d'environnement",
              type: 'TEXT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'belgique_permis_environnement_activite',
              label: 'Activité exercée imposant un tel permis ou déclaration',
              type: 'SELECT-BINARY'
            }
          ],
          id: 'd8c6dc5e_8aa9_4d26_95c0_6dcee7f7a2f1',
          label: "Permis d'environnement",
          type: 'CATEGORY'
        }
      ],
      id: 'af22f69e_ffca_4110_8f89_1bb9e66e56fe',
      label: 'Informations Administratives',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__BIEN__TERRAIN_CONSTRUCTIBLE',
  label: 'Terrain à bâtir',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__BIEN__TERRAIN_CONSTRUCTIBLE',
  specificTypes: ['BIEN', 'TERRAIN_CONSTRUCTIBLE'],
  type: 'RECORD'
};
