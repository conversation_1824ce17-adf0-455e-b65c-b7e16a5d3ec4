{"questionTemplates": {"RISQUES_NATURELS": {"type": "SELECT", "choices": [{"label": "N'est pas concerné par un plan de prévention des risques naturels", "id": "aucun_plan"}, {"label": "Est situé Dans le périmètre d'un plan de prévention des risques naturels prescrits", "id": "plan_prescrit"}, {"label": "Est situé Dans le périmètre d'un plan de prévention des risques naturels anticipés", "id": "plan_anticipe"}, {"label": "Est situé Dans le périmètre d'un plan de prévention des risques naturels approuvés", "id": "plan_approuve"}], "multiple": true}, "RISQUES_MINIERS": {"type": "SELECT", "choices": [{"label": "N'est pas concerné par un plan de prévention des risques miniers", "id": "aucun_plan"}, {"label": "Est situé Dans le périmètre d'un plan de prévention des risques miniers prescrits", "id": "plan_prescrit"}, {"label": "Est situé Dans le périmètre d'un plan de prévention des risques miniers anticipés", "id": "plan_anticipe"}, {"label": "Est situé Dans le périmètre d'un plan de prévention des risques miniers approuvés", "id": "plan_approuve"}], "multiple": true}, "RISQUES_TECHNOLOGIQUES": {"type": "SELECT", "choices": [{"label": "N'est pas concerné par un plan de prévention des risques technologiques", "id": "aucun_plan"}, {"label": "Est situé Dans le périmètre d'un plan de prévention des risques technologiques prescrits", "id": "plan_prescrit"}, {"label": "Est situé Dans le périmètre d'un plan de prévention des risques technologiques anticipés", "id": "plan_anticipe"}, {"label": "Est situé Dans le périmètre d'un plan de prévention des risques technologiques approuvés", "id": "plan_approuve"}], "multiple": true}, "RISQUES_SISMICITE": {"type": "SELECT", "choices": [{"label": "1", "id": "ppr_sismicite_un"}, {"label": "2", "id": "ppr_sismicite_deux"}, {"label": "3", "id": "ppr_sismicite_trois"}, {"label": "4", "id": "ppr_sismicite_quatre"}, {"label": "5", "id": "ppr_sismicite_cinq"}]}, "ADRESS": {"type": "SELECT", "choices": []}, "ORIGINE": {"type": "SELECT", "choices": [{"id": "origine_propriete_acquisition", "label": "Acquisition"}, {"id": "origine_propriete_donation", "label": "Donation"}, {"id": "origine_propriete_succession", "label": "Succession"}, {"id": "origine_propriete_partage", "label": "Partage - Licitation"}, {"id": "origine_propriete_echange", "label": "Echange"}, {"id": "origine_propriete_adjudication", "label": "Adjudication - Vente aux enchères"}, {"id": "origine_propriete_remembrement", "label": "Remembrement"}]}, "MANDAT_SUPERFICIE": {"type": "SELECT", "choices": [{"id": "mandat_superficie_mandant", "label": "Le Mandant"}, {"id": "mandat_superficie_mandataire", "label": "Le Mandataire"}, {"id": "mandat_superficie_diagnostiqueur", "label": "Un Diagnostiqueur"}]}, "DIAG_MLS": {"type": "SELECT", "choices": [{"id": "diag_mls_present", "label": "Le Mandant remet le diagnostic à la signature du mandat"}, {"id": "diag_mls_mandant", "label": "Le Mandant établit le diagnostic avant le compromis"}, {"id": "diag_mls_mandataire", "label": "Le Mandant charge le Mandataire d'établir le diagnostic"}]}, "TVA": {"type": "SELECT", "choices": [{"id": "tva_5", "label": "5,5 %"}, {"id": "tva_20", "label": "20 %"}]}, "DISPONIBILITE": {"type": "SELECT", "choices": []}, "BAIL_TYPE": {"type": "SELECT", "choices": []}, "LOCATION_PARTIELLE": {"type": "SELECT", "choices": []}, "VENDEUR_ACQUEREUR": {"type": "SELECT", "choices": [{"id": "vendeur", "label": "<PERSON>endeur"}, {"id": "ac<PERSON><PERSON>", "label": "<PERSON> l'Acquereur"}]}, "PRICE_CFP": {"type": "PRICE", "suffix": "CFP"}, "TEXT_DESCRIPTION_LIBRE": {"type": "TEXT", "description": "décrire le terrain en reprenant ses éléments caractéristiques qui figurent au plan de bornage (arbres, clôtures, futaies, murs mitoyens, cours d’eau, etc.)"}, "ZONE_ARGILE": {"type": "SELECT", "choices": [{"id": "faible", "label": "Zone d'exposition faible"}, {"id": "modere", "label": "Zone d'exposition modérée"}, {"id": "forte", "label": "Zone d'exposition forte"}, {"id": "inconnue", "label": "Zone d'exposition non classée"}]}, "STATUT_PV_ELECTRIQUE": {"type": "SELECT", "choices": [{"label": "PV de contrôle établi", "id": "etabli"}, {"label": "PV de contrôle non établi", "id": "non_etabli"}, {"label": "Dispense de contrôle", "id": "dispense"}]}, "ELECTRICITE_DISPENSE": {"type": "SELECT", "choices": [{"label": "Démolition du bâtiment", "id": "demolition"}, {"label": "Rénovation de l'installation", "id": "renovation"}]}, "BELGIQUE_CERTIBEAU_STATUT": {"type": "SELECT", "choices": [{"label": "Raccordement antérieur au 1er Juin 2021", "id": "raccordement_ante_2021"}, {"label": "Raccordement postérieur au 1er Juin 2021", "id": "raccordement_post_2021"}, {"label": "Immeuble sur plan", "id": "plan"}, {"label": "Local où l'eau est fournie au public", "id": "public"}]}, "GAZ_MAZOUT": {"type": "SELECT-BINARY", "choices": [{"label": "GAZ", "id": "gaz"}, {"label": "MAZOUT", "id": "mazout"}]}, "PERMIS_DECLARATION": {"type": "SELECT-BINARY", "choices": [{"label": "PERMIS", "id": "permis"}, {"label": "DECLARATION", "id": "declaration"}]}, "ETAT_POSTE_PRINCIPAUX": {"type": "SELECT", "choices": [{"label": "<PERSON><PERSON><PERSON>", "id": "neuf"}, {"label": "Excellent", "id": "excellent"}, {"label": "Remis à neuf", "id": "remis_neuf"}, {"label": "Bon état", "id": "bon_etat"}, {"label": "A rafraîchir", "id": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, {"label": "A rénover", "id": "renover"}, {"label": "A réhabiliter", "id": "rehabiliter"}, {"label": "A démolir", "id": "demolir"}, {"label": "<PERSON><PERSON>", "id": "autre"}]}, "AERIENNE_ENTERREE": {"type": "SELECT", "choices": [{"label": "Aérienne", "id": "aerienne"}, {"label": "Enterrée", "id": "enterree"}]}, "ORIGINE_GIBOIRE": {"type": "SELECT", "choices": [{"id": "origine_propriete_acquisition", "label": "Acquisition"}, {"id": "origine_propriete_vefa", "label": "VEFA"}, {"id": "origine_propriete_donation", "label": "Donation"}, {"id": "origine_propriete_succession", "label": "Succession"}, {"id": "origine_propriete_partage", "label": "Partage - Licitation"}, {"id": "origine_propriete_echange", "label": "Echange"}, {"id": "origine_propriete_adjudication", "label": "Adjudication - Vente aux enchères"}, {"id": "origine_propriete_remembrement", "label": "Remembrement"}]}}, "conditions": {"DIAGNOSTIC_GEOTECHNIQUE_ZONE_CONCERNEE": [{"id": "diagnostic_geotechnique_zone_concernee", "type": "EQUALS", "value": "oui"}], "DIAGNOSTIC_GEOTECHNIQUE_EFFECTUE": [{"id": "diagnostic_geotechnique_statut", "type": "EQUALS", "value": "oui"}], "ZONE_BRUIT": [{"id": "zone_bruit", "type": "EQUALS", "value": "oui"}], "BORNAGE": [{"id": "terrain_bornage", "value": "oui", "type": "EQUALS"}], "SERVITUDES": [{"id": "servitudes", "value": "oui", "type": "EQUALS"}], "OP_DONATION": [{"id": "origine_liste", "value": "origine_propriete_donation", "type": "EQUALS"}], "OP_DONATION_UNIQUE": [{"id": "origine_donation_unique", "value": "non", "type": "EQUALS"}], "OP_SUCCESSION": [{"id": "origine_liste", "value": "origine_propriete_succession", "type": "EQUALS"}], "OP_ADJUDICATION": [{"id": "origine_liste", "value": "origine_propriete_adjudication", "type": "EQUALS"}], "OP_ACQUISITION": [{"id": "origine_liste", "value": "origine_propriete_acquisition", "type": "EQUALS"}], "OP_ECHANGE": [{"id": "origine_liste", "value": "origine_propriete_echange", "type": "EQUALS"}], "OP_PARTAGE": [{"id": "origine_liste", "value": "origine_propriete_partage", "type": "EQUALS"}], "OP_SUCCESSION_TERMINEE": [{"id": "origine_succession_statut", "value": "oui", "type": "EQUALS"}], "OP_SUCCESSION_NON_TERMINEE": [{"id": "origine_succession_statut", "value": "non", "type": "EQUALS"}], "OP_REMEMBREMENT": [{"id": "origine_liste", "value": "origine_propriete_remembrement", "type": "EQUALS"}], "OCCUPATION": [{"id": "occupation_statut", "value": "oui", "type": "EQUALS"}], "LOCATION_BAIL_TYPE_AUTRE": [{"id": "location_bail_type", "value": "", "type": "EQUALS"}], "ICPE": [{"id": "icpe_statut", "value": "oui", "type": "EQUALS"}], "ZONE_TERMITES": [{"id": "diagnostics_techniques_termites_commune", "value": "oui", "type": "EQUALS"}], "REF_PROPRES": [{"id": "existence_cadastre", "value": "non", "type": "DIFFERENT"}], "ERP_LOTISSEMENT": [{"id": "zone_erp_statut", "value": "oui", "type": "EQUALS"}], "ERP_PAS_LOTISSEMENT": [{"id": "zone_erp_statut", "value": "non", "type": "EQUALS"}], "POLLUTION_LOTISSEMENT": [{"id": "erp_pollution_sol", "value": "oui", "type": "EQUALS"}], "PERMIS_NON_OBTENU": [{"id": "belgique_travaux_permis_obtenu", "value": "non", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_TOITURE": [{"id": "belgique_poste_toiture", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_MENUISERIE": [{"id": "belgique_poste_menuiserie", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_ELECTRIQUE": [{"id": "belgique_poste_electrique", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_SANITAIRE": [{"id": "belgique_poste_sanitaire", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_CHAUFFAGE": [{"id": "belgique_poste_chauffage", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_REVETEMENT_SOL": [{"id": "belgique_poste_revetement_sol", "value": "autre", "type": "EQUALS"}], "ETAT_POSTE_AUTRE_REVETEMENT_MUR": [{"id": "belgique_poste_revetement_mur", "value": "autre", "type": "EQUALS"}], "DEBROUSSAILLEMENT": [{"id": "obligation_debroussaillement_statut", "value": "oui", "type": "EQUALS"}], "PPRN_SIMPLE": [{"id": "erp_pprn_bien", "type": "EQUALS", "value": "oui"}], "PPRM_SIMPLE": [{"id": "erp_pprm_bien", "type": "EQUALS", "value": "oui"}], "PPRT_SIMPLE": [{"id": "erp_pprt_bien", "type": "EQUALS", "value": "oui"}], "RETRAIT_COTE": [{"id": "erp_retrait_cote_bien", "type": "EQUALS", "value": "oui"}], "RADON": [{"id": "potentiel_radon_classe", "type": "EQUALS", "value": "oui"}], "INDEMINTE_CATASTROPHE": [{"id": "indemnite_catastrophe", "type": "EQUALS", "value": "oui"}], "SISMICITE_2": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 2}], "SISMICITE_3": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 3}], "SISMICITE_4": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 4}], "SISMICITE_5": [{"id": "zone_sismicite_classe", "type": "EQUALS", "value": 5}], "TEMPLATE_PRECISION_DEROGATION": [{"type": "EQUALS_TEMPLATES", "templates": ["OPERATION__AJP__DOSSIER_DE_VENTE_AJP", "OPERATION__AJP__DOSSIER_DE_VENTE_BIEN_PROFESIONNEL_AJP"]}], "PREPARATION_EFFICITY": [{"type": "EQUALS_CONTRACT_MODELS", "model": ["EFFICITY__TRANSACTION__PREPARATION_COMPROMIS"]}]}}