// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordOperationImmobilierFinancement: LegalRecordTemplate = {
  config: {},
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'apport',
              label: 'Apport personnel'
            },
            {
              id: 'pret',
              label: 'Prêt'
            },
            {
              id: 'pret_etranger',
              label: "Prêt à l'étranger"
            },
            {
              id: 'vente',
              label: 'Vente immobilière en France'
            },
            {
              id: 'vente_etranger',
              label: "Vente immobilière à l'étranger"
            }
          ],
          id: 'mode_financement_liste',
          label: 'Mode de financement',
          multiple: true,
          type: 'SELECT'
        }
      ],
      id: '61cddc7f_f9ac_42ec_b85b_dd4083c71dd9',
      label: 'Financement',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'total',
              label: 'Emprunt total'
            },
            {
              id: 'partiel',
              label: 'Apport partiel 30%'
            },
            {
              id: 'cession',
              label: "Cession d'un bien immobilier"
            }
          ],
          id: 'mention_liste',
          label: 'La mention porte sur',
          type: 'SELECT'
        }
      ],
      id: '89fefeaf_611a_431e_bc16_eac280050918',
      label: 'Mention Manuscrite',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'emprunt_statut',
          label: 'Recours à un emprunt',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description:
            "En cas d'absence d'emprunt vous devez faire signer une renonciation à la condition suspensive de financement à chacun des acquéreurs lors du compromis de vente. Un modèle est téléchargeable dans les annexes.",
          id: 'pp_emprunt_statut',
          label: 'Le financement est il assuré par un emprunt ?',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'emprunt_statut',
                value: 'non',
                type: 'EQUALS'
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'renonciation_financement_pierre',
          label: 'Renonciation condition suspensive de financement',
          templateId: 'renonciationFinancement.pdf',
          type: 'UPLOAD'
        },
        {
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'renonciation_financement_scaprim',
          label: 'Renonciation condition suspensive de financement',
          templateId: 'renonciationFinancement.pdf',
          type: 'UPLOAD'
        },
        {
          id: 'emprunt_libre',
          label: "Montant total de l'emprunt",
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'mode_financement_liste',
                type: 'CONTAINS',
                value: 'pret'
              }
            ],
            [
              {
                id: 'mode_financement_liste',
                type: 'CONTAINS',
                value: 'pret_etranger'
              }
            ]
          ],
          id: 'emprunt_total',
          label: "Montant total de l'emprunt",
          type: 'PRICE'
        },
        {
          id: 'emprunt_duree_libre',
          label: "Durée totale de l'emprunt",
          suffix: 'ans',
          type: 'NUMBER'
        },
        {
          id: 'emprunt_duree_texte',
          label: "Durée totale de l'emprunt",
          type: 'TEXT'
        },
        {
          id: 'emprunt_taux_maximum_libre',
          label: "Taux maximal de l'emprunt",
          suffix: '%',
          type: 'NUMBER'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'emprunt_assurance_comprise',
          label: 'Assurance comprise',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'terrain',
              label: "L'acquisition du terrain"
            },
            {
              id: 'construction',
              label: "L'acquisition du terrain et la construction"
            }
          ],
          id: 'emprunt_construction',
          label: 'Le financement porte sur',
          type: 'SELECT'
        },
        {
          id: 'emprunt_libre_cfp',
          label: "Montant total de l'emprunt",
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pre_accord_statut',
          label: 'Le pré accord de financement a-t-il déjà été obtenu ?',
          type: 'SELECT-BINARY'
        },
        {
          id: 'pre_accord_date',
          label: "Date d'obtention du pré accord de financement",
          type: 'DATE'
        },
        {
          description:
            'La condition est réputée accomplie lorsque c’est le débiteur, obligé sous cette condition, qui en a empêché l’accomplissement. Si le bénéficiaire veut renoncer à la condition suspensive d’obtention de prêt, il devra notifier au promettant, dans les formes et délais applicables à la notification d’obtention de prêts, qu’il dispose des sommes nécessaires pour s’acquitter du prix sans l’aide d’un prêt. Cette notification devra contenir la mention suivante, écrite de la main du bénéficiaire : « Je reconnais avoir été informé de ce que, si je recourais néanmoins à un prêt, je ne pourrais me prévaloir du statut protecteur institué par les articles L 312-1 et suivants du Code de la consommation ». À défaut de réalisation de l’ensemble de ces conditions, la présente promesse sera caduque..',
          id: 'date_obtention_pret_libre',
          label: "Date extrême d'obtention du financement",
          type: 'DATE'
        },
        {
          id: 'organisme_bancaire_libre',
          label: 'Organisme bancaire',
          type: 'TEXT'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'taux_fixe',
                  label: 'Prêt à taux fixe'
                },
                {
                  id: 'taux_variable',
                  label: 'Prêt à taux variable'
                },
                {
                  id: 'in_fine',
                  label: 'Prêt in fine'
                },
                {
                  id: 'pel',
                  label: 'Prêt Plan épargne logement'
                },
                {
                  id: 'employeur',
                  label: 'Prêt 1% employeur'
                },
                {
                  id: 'taux_zero',
                  label: 'Prêt à taux zéro'
                },
                {
                  id: 'taux_zero_plus',
                  label: 'Prêt à taux zéro plus ou PTZ+'
                },
                {
                  id: 'accession_sociale',
                  label: "Prêt à l'accession sociale"
                },
                {
                  id: 'conventionne',
                  label: 'Prêt conventionné'
                },
                {
                  id: 'relais',
                  label: 'Prêt relais'
                },
                {
                  id: 'paris_logement_0',
                  label: 'Prêt Paris logement 0%'
                },
                {
                  id: 'travaux',
                  label: 'Prêt travaux'
                },
                {
                  id: 'prime_action_logement',
                  label: 'Prime Action Logement'
                },
                {
                  id: 'pret_action_logement',
                  label: 'Prêt Action Logement'
                },
                {
                  id: 'autre',
                  label: 'Autre'
                }
              ],
              id: 'emprunt_list_emprunt_type',
              label: "Nature de l'emprunt",
              type: 'SELECT'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'emprunt_list_emprunt_type_relais',
              label: 'Prêt relais',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'emprunt_list_emprunt_type_relais',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'emprunt_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'pp_emprunt_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    id: 'emprunt_list_emprunt_type_relais',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'emprunt_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'emprunt_list_emprunt_type_relais',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'pp_emprunt_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'emprunt_list_emprunt_type_relais_adresse',
              label: 'Adresse du bien à vendre',
              type: 'ADDRESS'
            },
            {
              conditions: [
                [
                  {
                    id: 'emprunt_list_emprunt_type',
                    type: 'EQUALS',
                    value: 'autre'
                  },
                  {
                    id: 'emprunt_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'pp_emprunt_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    id: 'emprunt_list_emprunt_type',
                    type: 'EQUALS',
                    value: 'autre'
                  },
                  {
                    id: 'emprunt_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'emprunt_list_emprunt_type',
                    type: 'EQUALS',
                    value: 'autre'
                  },
                  {
                    id: 'pp_emprunt_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'emprunt_list_emprunt_type_autre',
              label: "Type d'emprunt :",
              type: 'TEXT'
            },
            {
              id: 'emprunt_list_emprunt_montant_maximum',
              label: 'Montant maximum à emprunter :',
              type: 'PRICE'
            },
            {
              id: 'emprunt_list_emprunt_montant_maximum_cfp',
              label: 'Montant maximum à emprunter :',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              id: 'emprunt_list_emprunt_duree_maximum',
              label: "Durée maximum de l'emprunt :",
              suffix: 'années',
              type: 'NUMBER'
            },
            {
              id: 'emprunt_list_emprunt_duree_minimum',
              label: "Durée minimum de l'emprunt :",
              suffix: 'années',
              type: 'NUMBER'
            },
            {
              id: 'emprunt_list_emprunt_duree_maximum_mois',
              label: "Durée maximum de l'emprunt :",
              suffix: 'mois',
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  id: 'fixe',
                  label: 'Fixe'
                },
                {
                  id: 'variable',
                  label: 'Variable'
                }
              ],
              id: 'emprunt_list_emprunt_type_taux',
              label: 'Type de taux :',
              type: 'SELECT-BINARY'
            },
            {
              id: 'emprunt_list_emprunt_taux_maximum',
              label: "Taux maximum de l'emprunt :",
              suffix: '%',
              type: 'NUMBER'
            },
            {
              id: 'emprunt_list_emprunt_banque',
              label: 'Etablissement bancaire :',
              type: 'TEXT'
            },
            {
              id: 'emprunt_list_emprunt_charges_mensuelles_maximum',
              label: 'Charges mensuelles maximales :',
              type: 'PRICE'
            },
            {
              id: 'emprunt_list_emprunt_charges_mensuelles_maximum_cfp',
              label: 'Charges mensuelles maximales :',
              suffix: 'CFP',
              type: 'PRICE'
            }
          ],
          conditions: [
            [
              {
                id: 'emprunt_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'pp_emprunt_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'emprunt_list',
          label: 'Emprunts souhaités',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Emprunt '
              },
              {
                type: 'VARIABLE',
                value: 'emprunt_list_emprunt_montant_maximum'
              },
              {
                type: 'TEXT',
                value: '€'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'ue',
              label: 'UE'
            },
            {
              id: 'hors_ue',
              label: 'Hors UE'
            },
            {
              id: 'fiscalite_privilegiee',
              label: 'Pays à fiscalité privilégiée'
            },
            {
              id: 'pays_risque',
              label: 'Pays à risque (GAFI)'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'tracfin_banque_situation',
          label: 'Situation de la Banque',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'tracfin_banque_situation',
                type: 'EQUALS',
                value: 'autre'
              }
            ]
          ],
          id: 'tracfin_banque_situation_autre',
          label: 'Autre situation de la banque',
          type: 'TEXT'
        },
        {
          after: {
            value: 15,
            type: 'N_DAYS_FROM_NOW'
          },
          description:
            'Minimum 15j à compter de la signature ; indiquer au moins 25j si toutes les parties ne peuvent signer le contrat rapidement',
          id: 'date_obtention_attestation_depot',
          label: "Date limite d'obtention de l'attestation de dépôt de prêt",
          type: 'DATE'
        },
        {
          id: 'emprunt_credit_en_cours',
          label: 'Montant des crédits en cours',
          type: 'PRICE'
        },
        {
          id: 'emprunt_ressources_mensuelles',
          label: 'Ressources mensuelles',
          type: 'PRICE'
        },
        {
          id: 'emprunt_credit_en_cours_cfp',
          label: 'Montant des crédits en cours',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'emprunt_ressources_mensuelles_cfp',
          label: 'Ressources mensuelles',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'emprunt_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'pp_emprunt_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'emprunt_refus',
          label: "Nombre de refus de prêt à produire par l'Acquéreur",
          type: 'NUMBER'
        },
        {
          after: {
            type: 'N_DAYS_FROM_NOW',
            value: '1'
          },
          id: 'emprunt_cs_date_precise',
          label: "Date d'expiration de la condition suspensive",
          type: 'DATE'
        },
        {
          id: 'emprunt_delai_depot',
          label: 'Délai maximum de dépôt de la demande de prêt',
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'emprunt_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'pp_emprunt_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          description: 'Cette durée ne peut être inférieure à 31 jours.',
          id: 'emprunt_delai',
          label: 'Délai de réalisation de la condition suspensive',
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          id: 'emprunt_delai_mois',
          label: 'Délai de réalisation de la condition suspensive',
          suffix: 'mois',
          type: 'NUMBER'
        },
        {
          conditions: [
            [
              {
                id: 'emprunt_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'pp_emprunt_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'commercial_emprunt_delai',
          label: 'Délai de réalisation de la condition suspensive',
          suffix: 'jours',
          type: 'NUMBER'
        },
        {
          id: 'emprunt_delai_semaines',
          label: "Délai d'obtention du financement (semaines)",
          type: 'NUMBER'
        },
        {
          children: [
            {
              id: 'emprunt_simulation',
              label: "Simulations d'emprunts",
              type: 'UPLOAD'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'emprunt_travaux',
              label: 'Travaux à inclure dans le prêt',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'emprunt_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'emprunt_travaux',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'pp_emprunt_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    id: 'emprunt_statut',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'emprunt_travaux',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ],
                [
                  {
                    id: 'emprunt_travaux',
                    value: 'oui',
                    type: 'EQUALS'
                  },
                  {
                    id: 'pp_emprunt_statut',
                    value: 'oui',
                    type: 'EQUALS'
                  }
                ]
              ],
              id: 'emprunt_travaux_total',
              label: 'Montant total des travaux',
              suffix: '€',
              type: 'NUMBER'
            }
          ],
          conditions: [
            [
              {
                id: 'emprunt_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'pp_emprunt_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: '64c28309_6342_4289_8014_c8c0c7966d98',
          label: 'CONDITION_BLOCK_Informations complémentaires',
          type: 'CONDITION_BLOCK'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'ressource_acquereur',
          label: "Ressources de l'Acquéreur indiquées au contrat",
          type: 'SELECT-BINARY'
        },
        {
          id: 'ressource_acquereur_montant',
          label: "Montant des ressources mensuelles nettes de l'Acquéreur",
          type: 'PRICE'
        },
        {
          id: 'ressource_acquereur_montant_cfp',
          label: "Montant des ressources mensuelles nettes de l'Acquéreur",
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'ressource_acquereur_pret_en_cours',
          label: "Prêts en cours de l'Acquéreur",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'ressource_pret_en_cours_ressource_pret_en_cours_montant',
              label: 'Charge mensuelle du prêt',
              type: 'PRICE'
            },
            {
              id: 'ressource_pret_en_cours_ressource_pret_en_cours_montant_cfp',
              label: 'Charge mensuelle du prêt',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              id: 'ressource_pret_en_cours_ressource_pret_en_cours_duree',
              label: 'Durée restante du prêt',
              type: 'TEXT'
            }
          ],
          id: 'ressource_pret_en_cours',
          label: 'Prêt en cours',
          repetition: {
            label: [
              {
                type: 'TEXT',
                value: 'Emprunt '
              },
              {
                type: 'VARIABLE',
                value: 'ressource_pret_en_cours_ressource_pret_en_cours_montant'
              },
              {
                type: 'TEXT',
                value: '€'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'ressource_acquereur_pension',
          label: "Pension alimentaire due par l'Acquéreur",
          type: 'SELECT-BINARY'
        },
        {
          id: 'ressource_acquereur_pension_montant',
          label: "Montant de la pension payée par l'Acquéreur",
          type: 'PRICE'
        },
        {
          id: 'ressource_acquereur_pension_montant_cfp',
          label: "Montant de la pension payée par l'Acquéreur",
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'pret_relais_adresse',
          label: 'Adresse du bien Immobilier objet du prêt relais',
          type: 'ADDRESS'
        },
        {
          id: 'pret_relais_bien_montant',
          label: 'Valeur Vénale du bien Immobilier',
          type: 'PRICE'
        },
        {
          id: 'pret_relais_bien_montant_restant',
          label: 'Somme restante à rembourser sur le prêt initial',
          type: 'PRICE'
        },
        {
          id: 'pret_relais_bien_montant_cfp',
          label: 'Valeur Vénale du bien Immobilier',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          id: 'pret_relais_bien_montant_restant_cfp',
          label: 'Somme restante à rembourser sur le prêt initial',
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'annulation',
              label: 'La vente est inexistante'
            },
            {
              id: 'poursuite',
              label: 'La vente se poursuit'
            }
          ],
          id: 'belgique_condition_suspensive_tacite',
          label: "En cas d'absence d'information de l'acquéreur",
          type: 'SELECT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          conditions: [
            [
              {
                id: 'emprunt_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'pp_emprunt_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'frais_supplementaire_ajout',
          label: 'Ajouter des frais supplémentaires',
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              id: 'frais_supplementaire_liste_frais_supplementaire_type',
              label: 'Type de frais',
              type: 'TEXT'
            },
            {
              id: 'frais_supplementaire_liste_frais_supplementaire_montant',
              label: 'Montant',
              type: 'PRICE'
            }
          ],
          conditions: [
            [
              {
                id: 'emprunt_statut',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'frais_supplementaire_ajout',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'frais_supplementaire_ajout',
                type: 'EQUALS',
                value: 'oui'
              },
              {
                id: 'pp_emprunt_statut',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'frais_supplementaire_liste',
          label: 'Frais supplémentaires',
          repetition: {
            label: [
              {
                type: 'VARIABLE',
                value: 'frais_supplementaire_liste_frais_supplementaire_type'
              },
              {
                type: 'TEXT',
                value: ' '
              },
              {
                type: 'VARIABLE',
                value: 'frais_supplementaire_liste_frais_supplementaire_montant'
              },
              {
                type: 'TEXT',
                value: '€'
              }
            ],
            max: 1000,
            min: 1
          },
          type: 'REPEAT'
        }
      ],
      id: '737dbefe_f2c5_4a7d_9146_f824760267e1',
      label: 'Emprunt',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'apport',
          label: 'Recours à un apport personnel',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'epargne',
              label: 'Epargne'
            },
            {
              id: 'epargne_salariale',
              label: 'Epargne Salariale'
            },
            {
              id: 'donation',
              label: 'Donation'
            },
            {
              id: 'reinvestissement',
              label: 'Réinvestissement'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'apport_tracfin_list',
          label: "Provenance de l'apport",
          multiple: true,
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'apport_tracfin_list',
                type: 'CONTAINS',
                value: 'epargne'
              }
            ]
          ],
          id: 'apport_tracfin_epargne',
          label: 'Montant épargne',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'apport_tracfin_list',
                type: 'CONTAINS',
                value: 'epargne_salariale'
              }
            ]
          ],
          id: 'apport_tracfin_epargne_salariale',
          label: 'Montant épargne salariale',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'apport_tracfin_list',
                type: 'CONTAINS',
                value: 'donation'
              }
            ]
          ],
          id: 'apport_tracfin_donation',
          label: 'Montant Donation',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'apport_tracfin_list',
                type: 'CONTAINS',
                value: 'donation'
              }
            ]
          ],
          id: 'apport_tracfin_donation_lien',
          label: 'Lien de parenté',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'apport_tracfin_list',
                type: 'CONTAINS',
                value: 'reinvestissement'
              }
            ]
          ],
          id: 'apport_tracfin_reinvestissement',
          label: 'Montant Réinvestissement',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'apport_tracfin_list',
                type: 'CONTAINS',
                value: 'autre'
              }
            ]
          ],
          id: 'apport_tracfin_autre',
          label: 'Apport autre motif',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'apport_tracfin_list',
                type: 'CONTAINS',
                value: 'autre'
              }
            ]
          ],
          id: 'apport_tracfin_autre_montant',
          label: 'Apport autre montant',
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'apport',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'mode_financement_liste',
                type: 'CONTAINS',
                value: 'apport'
              }
            ]
          ],
          id: 'apport_montant_simple',
          label: "Montant de l'apport",
          type: 'PRICE'
        },
        {
          conditions: [
            [
              {
                id: 'apport',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'apport_montant_simple_cfp',
          label: "Montant de l'apport",
          suffix: 'CFP',
          type: 'PRICE'
        },
        {
          children: [
            {
              children: [
                {
                  id: 'apport_list_apport_personne',
                  label: "Personne concernée par l'apport",
                  type: 'TEXT'
                },
                {
                  id: 'apport_list_apport_montant',
                  label: "Montant de l'apport personnel",
                  type: 'PRICE'
                },
                {
                  id: 'apport_list_apport_montant_cfp',
                  label: "Montant de l'apport personnel",
                  suffix: 'CFP',
                  type: 'PRICE'
                },
                {
                  id: 'apport_list_apport_provenance',
                  label: "Provenance de l'apport",
                  type: 'TEXT'
                }
              ],
              id: 'apport_list',
              label: 'Apport personnel',
              repetition: {
                label: [
                  {
                    type: 'TEXT',
                    value: 'Apport '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'apport_list_apport_personne'
                  },
                  {
                    type: 'TEXT',
                    value: ' '
                  },
                  {
                    type: 'VARIABLE',
                    value: 'apport_list_apport_montant'
                  },
                  {
                    type: 'TEXT',
                    value: '€'
                  }
                ],
                max: 1000,
                min: 0
              },
              type: 'REPEAT'
            }
          ],
          conditions: [
            [
              {
                conditions: [
                  {
                    id: 'apport',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                type: 'EQUALS_TEMPLATES',
                templates: [
                  'OPERATION__IMMOBILIER__VENTE_ANCIEN',
                  'OPERATION__ICM__DOSSIER_DE_VENTE_ICM',
                  'OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN',
                  'OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE',
                  'OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN',
                  'OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF',
                  'OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO',
                  'OPERATION__KEREDES__IMMOBILIER_BRS',
                  'OPERATION__BEAUX_VILLAGES__IMMOBILIER',
                  'OPERATION__CANNISIMMO__IMMOBILIER',
                  'OPERATION__SANTONI__IMMOBILIER',
                  'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                  'OPERATION__ORPI__IMMOBILIER__VENTE',
                  'OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION',
                  'OPERATION__JOHN_TAYLOR_ALPILLES__VENTE_JOHN_TAYLOR_ALPILLES',
                  'OPERATION__JOHN_TAYLOR_CORSE__VENTE_JOHN_TAYLOR_CORSE',
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                  'OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE',
                  'OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE',
                  'OPERATION__ERA__IMMOBILIER__VENTE',
                  'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                  'OPERATION__UNIS__IMMOBILIER__HABITATION',
                  'OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE',
                  'OPERATION__BENEDIC__IMMOBILIER',
                  'OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION',
                  'OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE',
                  'OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE',
                  'OPERATION__COLDWELL_BANKER__IMMOBILIER',
                  'OPERATION__FOLLIOT__IMMOBILIER',
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                  'OPERATION__ALLOWA__IMMOBILIER',
                  'OPERATION__COLDWELL_BANKER__IMMOBILIER',
                  'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE'
                ]
              }
            ],
            [
              {
                type: 'DIFFERENT_TEMPLATES',
                templates: [
                  'OPERATION__IMMOBILIER__VENTE_ANCIEN',
                  'OPERATION__ICM__DOSSIER_DE_VENTE_ICM',
                  'OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_VENTE_ANCIEN',
                  'OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_PRESTIGE',
                  'OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_TERRAIN',
                  'OPERATION__TROIS_G_IMMO__TROIS_G_IMMO_NEUF',
                  'OPERATION__THIERRY_IMMOBILIER__DOSSIER_DE_VENTE_THIERRY_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_HABITAT_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_IMMOBILIER_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_SELECTION_OCEAN_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_AGENCE_HAMILTON_PRO',
                  'OPERATION__SELECTION_HABITAT__DOSSIER_DE_VENTE_ARIEG_IMMO_PRO',
                  'OPERATION__KEREDES__IMMOBILIER_BRS',
                  'OPERATION__BEAUX_VILLAGES__IMMOBILIER',
                  'OPERATION__CANNISIMMO__IMMOBILIER',
                  'OPERATION__SANTONI__IMMOBILIER',
                  'OPERATION__AJP__DOSSIER_DE_VENTE_AJP',
                  'OPERATION__ORPI__IMMOBILIER__VENTE',
                  'OPERATION__JOHN_TAYLOR__IMMOBILIER__VENTE_HABITATION',
                  'OPERATION__JOHN_TAYLOR_ALPILLES__VENTE_JOHN_TAYLOR_ALPILLES',
                  'OPERATION__JOHN_TAYLOR_CORSE__VENTE_JOHN_TAYLOR_CORSE',
                  'OPERATION__AXO_ACTIF__IMMOBILIER__HABITATION',
                  'OPERATION__MY_CHEZ_MOI__IMMOBILIER__VENTE',
                  'OPERATION__COTE_PARTICULIERS__IMMOBILIER__VENTE',
                  'OPERATION__ERA__IMMOBILIER__VENTE',
                  'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE',
                  'OPERATION__UNIS__IMMOBILIER__HABITATION',
                  'OPERATION__PVCI_SENS__IMMOBILIER__VENTE_SEULE',
                  'OPERATION__BENEDIC__IMMOBILIER',
                  'OPERATION__I_PARTICULIERS__IMMOBILIER__HABITATION',
                  'OPERATION__I_PARTICULIERS__IMMOBILIER__LUXE',
                  'OPERATION__I_PARTICULIERS__IMMOBILIER__SUISSE',
                  'OPERATION__COLDWELL_BANKER__IMMOBILIER',
                  'OPERATION__FOLLIOT__IMMOBILIER',
                  'OPERATION__KELLER_WILLIAMS__IMMOBILIER',
                  'OPERATION__ALLOWA__IMMOBILIER',
                  'OPERATION__COLDWELL_BANKER__IMMOBILIER',
                  'OPERATION__IMOCONSEIL__IMMOBILIER__VENTE'
                ]
              }
            ]
          ],
          id: 'e74acfdf_d592_496b_9141_660c523ae526',
          label: 'CONDITION_BLOCK_Apport(s)',
          type: 'CONDITION_BLOCK'
        },
        {
          id: 'apport_provenance_tracfin',
          label: "Provenance de l'apport",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'epargne',
              label: 'Epargne'
            },
            {
              id: 'succession',
              label: 'Succession / Donation'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'apport_provenance_liste',
          label: "L'apport provient de",
          multiple: true,
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'apport_provenance_liste',
                value: 'autre',
                type: 'CONTAINS'
              }
            ]
          ],
          id: 'apport_provenance_autre',
          label: "Autre source de l'apport",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mention_manuscrite_procuration_giboire_30',
          label: "L'apport dépasse 30 % du prix de vente. Ajouter la mention manuscrite",
          type: 'SELECT-BINARY'
        },
        {
          id: 'apport_compte_numero',
          label: "Numéro de compte de l'apport",
          type: 'TEXT'
        },
        {
          id: 'apport_compte_nom',
          label: "Nom du compte de l'apport",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'pret_partiel',
          label: 'Le prêt ne couvre pas la totalité du prix. Ajouter la mention manuscrite de renonciation',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'pret_partiel',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          filters: {
            mustBeExcludedInOperationConfig: true
          },
          id: 'apport_financement_partiel_mention_document',
          label: 'Mention manuscrite - financement partiel',
          type: 'UPLOAD'
        }
      ],
      id: '3bf537be_f06b_49d7_a397_2fd9900191d8',
      label: 'Apport',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'cession',
          label: "Cession d'un bien immobilier appartenant à l'Acquéreur",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: 'Accord de la DIR + Promesse purgée à enregistrer dans Stockage fichier',
          id: 'cession_cdc',
          label: "Cession d'un bien immobilier appartenant au Bénéficiaire",
          type: 'SELECT-BINARY'
        },
        {
          children: [
            {
              filters: {
                mustBeIncludedInOperationConfig: true
              },
              id: 'cession_compromis_signe_doc_libre',
              label: 'Compromis de vente signé',
              type: 'UPLOAD'
            },
            {
              id: 'cession_type',
              label: 'Type de bien vendu',
              type: 'TEXT'
            },
            {
              id: 'cession_adresse',
              label: 'Adresse du Bien Vendu',
              type: 'ADDRESS'
            },
            {
              id: 'cession_prix_vente_total',
              label: 'Montant du prix de vente',
              type: 'PRICE'
            },
            {
              id: 'cession_prix_vente',
              label: 'Montant du prix de vente réutilisé',
              type: 'PRICE'
            },
            {
              id: 'cession_prix_vente_cfp',
              label: 'Montant du prix de vente réutilisé',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cession_compromis_signe',
              label: 'Compromis de vente de cession déjà signé',
              type: 'SELECT-BINARY'
            },
            {
              id: 'cession_compromis_signe_date_realisation',
              label: 'Délai maximal de réalisation de la vente',
              suffix: 'Mois',
              type: 'NUMBER'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              description:
                'à intégrer si conditions suspensives particulières autres que urbanisme/hypothèque/droit de préemption urbain',
              id: 'cession_compromis_annexe',
              label: 'Le compromis de vente est annexé',
              type: 'SELECT-BINARY'
            },
            {
              conditions: [
                [
                  {
                    id: 'cession',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cession_cdc',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'cession',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'cession_cdc',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente'
                  }
                ],
                [
                  {
                    id: 'cession_cdc',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente_etranger'
                  }
                ],
                [
                  {
                    id: 'cession_cdc',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente'
                  }
                ],
                [
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente_etranger'
                  }
                ]
              ],
              filters: {
                mustBeExcludedInOperationConfig: true
              },
              id: 'cession_compromis_document',
              label: "Compromis de vente - Vente du Bien de l'Acquéreur",
              type: 'UPLOAD'
            },
            {
              children: [
                {
                  id: 'cession_compromis_acquereur',
                  label: 'Nom des Acquéreurs du Bien',
                  type: 'TEXT'
                },
                {
                  before: {
                    type: 'N_DAYS_FROM_NOW',
                    value: '1'
                  },
                  id: 'cession_date_compromis',
                  label: 'Date de signature du compromis',
                  type: 'DATE'
                },
                {
                  after: {
                    type: 'N_DAYS_FROM_NOW',
                    value: '1'
                  },
                  id: 'cession_date_signature',
                  label: "Date envisagée pour la signature de l'acte de vente",
                  type: 'DATE'
                },
                {
                  id: 'cession_nom_notaire',
                  label: "Nom du notaire en charge de l'acte de vente",
                  type: 'TEXT'
                },
                {
                  id: 'cession_ville_notaire',
                  label: "Ville du notaire en charge de l'acte de vente",
                  type: 'TEXT'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      }
                    ],
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ],
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      }
                    ],
                    [
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ],
                    [
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ],
                    [
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      }
                    ],
                    [
                      {
                        id: 'cession_compromis_signe',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ]
                  ],
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'cession_compromis_signe_doc',
                  label: 'Compromis de vente signé',
                  type: 'UPLOAD'
                }
              ],
              id: '4239eaec_1e3a_4064_93e7_26afc739127c',
              label: 'CONDITION_BLOCK_Compromis signé',
              type: 'CONDITION_BLOCK'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'cession_deux',
              label: "Cession d'un second bien appartenant à l'Acquéreur",
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  filters: {
                    mustBeExcludedInOperationConfig: true
                  },
                  id: 'cession_compromis_signe_doc_libre_deux',
                  label: 'Compromis de vente signé',
                  type: 'UPLOAD'
                },
                {
                  id: 'cession_type_deux',
                  label: 'Type de bien vendu',
                  type: 'TEXT'
                },
                {
                  id: 'cession_adresse_deux',
                  label: 'Adresse du bien',
                  type: 'ADDRESS'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      }
                    ],
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ],
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      }
                    ],
                    [
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ],
                    [
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ],
                    [
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      }
                    ],
                    [
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ]
                  ],
                  id: 'cession_prix_vente_deux',
                  label: 'Montant du prix de vente réutilisé',
                  type: 'PRICE'
                },
                {
                  conditions: [
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      }
                    ],
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ],
                    [
                      {
                        id: 'cession',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      }
                    ],
                    [
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ],
                    [
                      {
                        id: 'cession_cdc',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      }
                    ],
                    [
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ],
                    [
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente'
                      }
                    ],
                    [
                      {
                        id: 'cession_deux',
                        type: 'EQUALS',
                        value: 'oui'
                      },
                      {
                        id: 'mode_financement_liste',
                        type: 'CONTAINS',
                        value: 'vente_etranger'
                      }
                    ]
                  ],
                  id: 'cession_prix_vente_deux_cfp',
                  label: 'Montant du prix de vente réutilisé',
                  suffix: 'CFP',
                  type: 'PRICE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'cession_compromis_signe_deux',
                  label: 'Compromis de vente de cession déjà signé',
                  type: 'SELECT-BINARY'
                },
                {
                  children: [
                    {
                      before: {
                        type: 'N_DAYS_FROM_NOW',
                        value: '1'
                      },
                      id: 'cession_date_compromis_deux',
                      label: 'Date de signature du compromis',
                      type: 'DATE'
                    },
                    {
                      after: {
                        type: 'N_DAYS_FROM_NOW',
                        value: '1'
                      },
                      id: 'cession_date_signature_deux',
                      label: "Date envisagée pour la signature de l'acte de vente",
                      type: 'DATE'
                    },
                    {
                      id: 'cession_nom_notaire_deux',
                      label: "Nom du notaire en charge de l'acte de vente",
                      type: 'TEXT'
                    },
                    {
                      id: 'cession_ville_notaire_deux',
                      label: "Ville du notaire en charge de l'acte de vente",
                      type: 'TEXT'
                    },
                    {
                      conditions: [
                        [
                          {
                            id: 'cession',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'cession_cdc',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'cession',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'mode_financement_liste',
                            type: 'CONTAINS',
                            value: 'vente'
                          }
                        ],
                        [
                          {
                            id: 'cession',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'mode_financement_liste',
                            type: 'CONTAINS',
                            value: 'vente_etranger'
                          }
                        ],
                        [
                          {
                            id: 'cession',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'cession_cdc',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'mode_financement_liste',
                            type: 'CONTAINS',
                            value: 'vente'
                          }
                        ],
                        [
                          {
                            id: 'cession_cdc',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'mode_financement_liste',
                            type: 'CONTAINS',
                            value: 'vente_etranger'
                          }
                        ],
                        [
                          {
                            id: 'cession_cdc',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          }
                        ],
                        [
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'mode_financement_liste',
                            type: 'CONTAINS',
                            value: 'vente'
                          },
                          {
                            id: 'mode_financement_liste',
                            type: 'CONTAINS',
                            value: 'vente_etranger'
                          }
                        ],
                        [
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'mode_financement_liste',
                            type: 'CONTAINS',
                            value: 'vente'
                          }
                        ],
                        [
                          {
                            id: 'cession_deux',
                            type: 'EQUALS',
                            value: 'oui'
                          },
                          {
                            id: 'mode_financement_liste',
                            type: 'CONTAINS',
                            value: 'vente_etranger'
                          }
                        ]
                      ],
                      filters: {
                        mustBeExcludedInOperationConfig: true
                      },
                      id: 'cession_compromis_signe_doc_deux',
                      label: 'Compromis de vente signé',
                      type: 'UPLOAD'
                    }
                  ],
                  id: '4239eaec_1e3a_4064_93e7_26afc739127c',
                  label: 'CONDITION_BLOCK_Compromis signé',
                  type: 'CONDITION_BLOCK'
                }
              ],
              conditions: [
                [
                  {
                    id: 'cession',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cession_cdc',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'cession',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente'
                  }
                ],
                [
                  {
                    id: 'cession',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente_etranger'
                  }
                ],
                [
                  {
                    id: 'cession',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'cession_cdc',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente'
                  }
                ],
                [
                  {
                    id: 'cession_cdc',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente_etranger'
                  }
                ],
                [
                  {
                    id: 'cession_cdc',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  }
                ],
                [
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente_etranger'
                  }
                ],
                [
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente'
                  }
                ],
                [
                  {
                    id: 'cession_deux',
                    type: 'EQUALS',
                    value: 'oui'
                  },
                  {
                    id: 'mode_financement_liste',
                    type: 'CONTAINS',
                    value: 'vente_etranger'
                  }
                ]
              ],
              id: '7d588233_68dc_44de_8597_adf5c140504e',
              label: 'Deuxième cession',
              type: 'CATEGORY'
            }
          ],
          conditions: [
            [
              {
                id: 'cession',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'cession_cdc',
                type: 'EQUALS',
                value: 'oui'
              }
            ],
            [
              {
                id: 'mode_financement_liste',
                type: 'CONTAINS',
                value: 'vente'
              }
            ],
            [
              {
                id: 'mode_financement_liste',
                type: 'CONTAINS',
                value: 'vente_etranger'
              }
            ]
          ],
          id: '52e8ec48_bd39_4648_9545_600be7dfb884',
          label: 'Cession',
          type: 'CATEGORY'
        }
      ],
      id: '0e1306c7_ccae_4d96_927f_5262668cdcf5',
      label: "Cession d'un bien immobilier",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'autre_ligne_statut',
          label: 'Ajouter une ligne au tableau de financement',
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'autre_ligne_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'autre_ligne_titre',
          label: 'Titre',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'autre_ligne_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'autre_ligne_montant',
          label: 'Montant',
          type: 'PRICE'
        }
      ],
      id: '627b270e_e6f1_40d8_a95b_9309d47c282c',
      label: 'Autres',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'depot_garantie_statut',
          label: 'Dépôt de garantie',
          type: 'SELECT-BINARY'
        },
        {
          id: 'autre_depot_garantie_montant',
          label: 'Montant du dépôt de garantie',
          type: 'PRICE'
        },
        {
          id: 'autre_depot_garantie_pourcentage',
          label: 'Pourcentage du dépôt de garantie',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'dix',
                  label: '10 % du prix de vente'
                },
                {
                  id: 'cinq',
                  label: '5 % du prix de vente'
                },
                {
                  id: 'depot_autre',
                  label: 'Autre montant'
                }
              ],
              id: 'depot_garantie_montant',
              label: 'Montant du dépôt de garantie',
              type: 'SELECT'
            },
            {
              id: 'depot_garantie_autre',
              label: 'Autre montant du dépôt de garantie',
              type: 'PRICE'
            },
            {
              conditions: [
                [
                  {
                    id: 'depot_garantie_montant',
                    type: 'EQUALS',
                    value: 'depot_autre'
                  }
                ]
              ],
              id: 'depot_garantie_autre_cfp',
              label: 'Autre montant',
              suffix: 'CFP',
              type: 'PRICE'
            },
            {
              choices: [
                {
                  id: 'depot_garantie_agence',
                  label: "A l'Agence"
                },
                {
                  id: 'depot_garantie_notaire',
                  label: 'Au Notaire'
                }
              ],
              id: 'depot_garantie_versement',
              label: 'Le montant du dépôt de garantie est versé',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'depot_garantie_compromis',
                  label: 'Le jour du compromis de vente'
                },
                {
                  id: 'depot_garantie_quinze',
                  label: 'Dans les quinze jours de la signature du compromis'
                },
                {
                  id: 'depot_garantie_retractation',
                  label: 'A la fin du délai de rétractation'
                },
                {
                  id: 'depot_garantie_huit',
                  label: 'Dans les huit jours à compter de la fin du délai de rétractation'
                },
                {
                  id: 'depot_garantie_autre_date',
                  label: 'Un autre délai'
                }
              ],
              id: 'depot_garantie_date',
              label: 'Délai de versement du dépôt de garantie',
              type: 'SELECT'
            }
          ],
          id: 'be7ddeff_6586_42d6_a729_f33656be5fc0',
          label: 'CONDITION_BLOCK_Dépôt de garantie',
          type: 'CONDITION_BLOCK'
        }
      ],
      id: 'bce46901_2f59_4802_911e_d6e6c9315893',
      label: 'Dépôt de garantie',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__OPERATION__IMMOBILIER__FINANCEMENT',
  label: 'Financement',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__OPERATION__IMMOBILIER__FINANCEMENT',
  specificTypes: ['OPERATION', 'IMMOBILIER', 'FINANCEMENT'],
  type: 'RECORD'
};
