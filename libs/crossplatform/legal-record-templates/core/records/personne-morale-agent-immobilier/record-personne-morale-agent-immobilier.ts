// @ts-nocheck
/* eslint-disable sort-keys-fix/sort-keys-fix */
import { LegalRecordTemplate } from '@mynotary/crossplatform/legal-templates/api';

export const RecordPersonneMoraleAgentImmobilier: LegalRecordTemplate = {
  config: {
    tags: {
      siren: {
        questionId: 'siren',
        format: 'SIREN',
        order: 0
      }
    },
    recordLinks: [
      {
        id: 'REPRESENTATION',
        specificTypes: ['REPRESENTATION', 'PERSONNE_MORALE'],
        label: 'Représentant de la société',
        creation: {
          autoCreate: false
        },
        constraints: {
          min: 1,
          max: 1
        },
        creationQuestion: {
          choices: [
            {
              id: 'other'
            },
            {
              id: 'empty'
            }
          ]
        }
      },
      {
        id: 'PROCURATION',
        label: 'Procuration',
        specificTypes: ['PROCURATION'],
        contractSpecific: true,
        creationQuestion: {
          choices: [
            {
              id: 'other',
              label: 'Oui'
            },
            {
              id: 'empty',
              label: 'Non'
            }
          ],
          label: 'La partie donne t-elle procuration pour ce contrat :'
        },
        constraints: {
          min: 0,
          max: 1
        }
      }
    ],
    search: [
      'siren',
      'personne_morale_denomination',
      'personne_morale_denomination_commerciale',
      'personne_morale_forme_sociale',
      'personne_morale_forme_sociale_autre'
    ],
    duplicate: ['siren']
  },
  form: [
    {
      children: [
        {
          choices: [
            {
              id: 'liege',
              label: 'Liège'
            },
            {
              id: 'rasquain',
              label: 'Rasquain'
            }
          ],
          id: 'agence_trevi_liege',
          label: 'Agence Trévi',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'societe_sci',
              label: 'Société Civile Immobilière'
            },
            {
              id: 'societe_sarl',
              label: 'Société à Responsabilité Limitée'
            },
            {
              id: 'societe_sa',
              label: 'Société par Actions'
            },
            {
              id: 'societe_sas',
              label: 'Société par Actions Simplifiée'
            },
            {
              id: 'societe_sasu',
              label: 'Société par Actions Simplifiée Unipersonnelle'
            },
            {
              id: 'association',
              label: 'Association'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'personne_morale_forme_sociale',
          label: 'Forme sociale',
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                id: 'personne_morale_forme_sociale',
                type: 'EQUALS',
                value: 'autre'
              }
            ]
          ],
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'personne_morale_forme_sociale_autre',
          label: 'Précisez la forme sociale',
          type: 'TEXT'
        },
        {
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'personne_morale_denomination',
          label: 'Dénomination sociale',
          type: 'TEXT'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'personne_morale_denomination_commerciale',
          label: "Nom commercial / de l'enseigne",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'capital_variable',
          label: 'Capital variable',
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'capital',
          label: 'Capital social',
          suffix: 'Euros',
          type: 'NUMBER'
        },
        {
          id: 'capital_cfp',
          label: 'Capital social',
          suffix: 'CFP',
          type: 'NUMBER'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'adresse',
          label: 'Siège Social',
          type: 'ADDRESS'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'immatriculation_statut',
          label: 'La société est déjà immatriculée au RCS',
          type: 'SELECT-BINARY'
        },
        {
          description: "Ou tout autre numéro d'identification si société basée à l'étranger",
          filters: {
            recordOnly: true,
            qualificationQuestions: true
          },
          id: 'siren',
          label: 'Numéro SIREN',
          type: 'TEXT'
        },
        {
          id: 'numero_snpi',
          label: 'Numéro SNPI',
          type: 'TEXT'
        },
        {
          id: 'immatriculation_numero',
          label: "Numéro d'immatriculation professionnelle",
          type: 'TEXT'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'ville_immatriculation',
          label: "Ville d'immatriculation",
          type: 'TEXT'
        },
        {
          id: 'zone_non_concurrence',
          label: 'Liste des communes pour zone de non-concurrence',
          type: 'TEXTAREA'
        },
        {
          id: 'code_ape',
          label: 'Code A.P.E.',
          type: 'TEXT'
        },
        {
          conditions: [
            [
              {
                id: 'mandat_delegation_amepi',
                recordPath: ['OPERATION__IMMOBILIER__VENTE_ANCIEN__FICHES', 'MANDAT', '0'],
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'liste_agence_amepi',
          label: 'Liste des agences - AMEPI',
          type: 'UPLOAD'
        },
        {
          id: 'rib_agence',
          label: 'RIB Agence - Versement Honoraires',
          type: 'UPLOAD'
        },
        {
          id: 'numero_tva',
          label: "Numéro d'identification TVA",
          type: 'TEXT'
        },
        {
          id: 'numero_compte_tiers',
          label: 'Numéro du compte tiers',
          type: 'TEXT'
        },
        {
          id: 'numero_entreprise',
          label: "Numéro d'entreprise",
          type: 'TEXT'
        },
        {
          id: 'nom_assureur',
          label: "Nom de l'assureur",
          type: 'TEXT'
        },
        {
          id: 'numero_police_rcp',
          label: 'Numéro de police collective RC professionnelle',
          type: 'TEXT'
        }
      ],
      id: 'da102edc_4cbd_438d_9d5b_ebf2614f2840',
      label: 'Informations générales',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'representant_personne_morale_statut',
          label: 'Représentation par une personne morale',
          type: 'SELECT-BINARY'
        },
        {
          id: 'representant_societe_denomination',
          label: 'Dénomination de la société',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'societe_sci',
              label: 'Société Civile Immobilière'
            },
            {
              id: 'societe_sarl',
              label: 'Société à Responsabilité Limitée'
            },
            {
              id: 'societe_sa',
              label: 'Société par Actions'
            },
            {
              id: 'societe_sas',
              label: 'Société par Actions Simplifiée'
            },
            {
              id: 'societe_sasu',
              label: 'Société par Actions Simplifiée Unipersonnelle'
            },
            {
              id: 'association',
              label: 'Association'
            },
            {
              id: 'autre',
              label: 'Autre'
            }
          ],
          id: 'representant_societe_forme',
          label: 'Forme sociale',
          type: 'SELECT'
        },
        {
          id: 'representant_societe_capital',
          label: 'Capital social',
          type: 'PRICE'
        },
        {
          id: 'representant_societe_siege',
          label: 'Siège de la société',
          type: 'ADDRESS'
        },
        {
          id: 'representant_societe_rcs_ville',
          label: "Ville d'immatriculation",
          type: 'TEXT'
        },
        {
          id: 'representant_societe_rcs_numero',
          label: 'Numéro RCS',
          type: 'TEXT'
        },
        {
          id: 'representant_societe_qualite',
          label: 'Qualité',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'homme',
              label: 'Monsieur'
            },
            {
              id: 'femme',
              label: 'Madame'
            }
          ],
          id: 'representant_civilite',
          label: 'Civilité du représentant légal',
          type: 'SELECT'
        },
        {
          id: 'representant_prenom',
          label: 'Prénom du représentant légal',
          type: 'TEXT'
        },
        {
          id: 'representant_nom',
          label: 'Nom du représentant légal',
          type: 'TEXT'
        },
        {
          id: 'representant_telephone',
          label: 'Téléphone du représentant légal',
          type: 'PHONE'
        },
        {
          id: 'representant_email',
          label: 'Email du représentant légal',
          type: 'EMAIL'
        },
        {
          choices: [
            {
              id: 'representant_qualite_gerant',
              label: 'Gérant'
            },
            {
              id: 'representant_qualite_pdg',
              label: 'PDG'
            },
            {
              id: 'representant_qualite_president',
              label: 'Président'
            },
            {
              id: 'representant_qualite_directeur',
              label: 'Directeur Général'
            },
            {
              id: 'representant_qualite_autre',
              label: 'Autre'
            }
          ],
          id: 'representant_qualite',
          label: 'Qualité du représentant légal',
          type: 'SELECT'
        },
        {
          id: 'representant_qualite_autre',
          label: 'Préciser le statut du représentant',
          type: 'TEXT'
        },
        {
          id: 'representant_designation_generique',
          label: 'Désignation du représentant',
          type: 'TEXT'
        }
      ],
      id: 'ae728161_c38e_40e2_aafc_5f8bd0393c4e',
      label: 'Représentant légal',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          filters: {
            qualificationQuestions: true
          },
          id: 'agence_nom',
          label: "Nom de l'agence",
          type: 'TEXT'
        },
        {
          id: 'agence_horaire',
          label: 'Horaires',
          type: 'TEXT'
        },
        {
          id: 'agence_adresse',
          label: 'Adresse',
          type: 'ADDRESS'
        },
        {
          id: 'agence_telephone',
          label: 'Numéro de téléphone de contact',
          type: 'PHONE'
        },
        {
          id: 'agence_email',
          label: 'Adresse email de contact',
          type: 'TEXT'
        },
        {
          id: 'agence_internet',
          label: 'Site internet',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'agence_etablissement_secondaire',
          label: "L'agence possède-t-elle un établissement secondaire ?",
          type: 'SELECT-BINARY'
        },
        {
          id: 'agence_etablissement_secondaire_denomiation',
          label: "Dénomination de l'établissement secondaire",
          type: 'TEXT'
        },
        {
          id: 'agence_etablissement_secondaire_adresse',
          label: "Adresse de l'établissement secondaire",
          type: 'ADDRESS'
        },
        {
          id: 'responasble_vrp',
          label: 'Nom / Prénom du responsable des négociateurs',
          type: 'TEXT'
        },
        {
          description:
            'La jurisprudence sur les clauses de mobilité (Cass. Soc. 7/06/06) oblige à définir de façon précise sa zone géographique d’application',
          id: 'zone_geographique_mobilite',
          label: 'Zone géographique de mobilité du négociateur',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'delegue_rgpd_statut',
          label: "Présence d'un délégué RGPD",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'homme',
              label: 'Monsieur'
            },
            {
              id: 'femme',
              label: 'Madame'
            }
          ],
          id: 'delegue_rgpd_civilite',
          label: 'Civilité du délégué',
          type: 'SELECT'
        },
        {
          id: 'delegue_rgpd_prenom',
          label: 'Prénom du délégué RGPD',
          type: 'TEXT'
        },
        {
          id: 'delegue_rgpd_nom',
          label: 'Nom du délégué RGPD',
          type: 'TEXT'
        },
        {
          id: 'delegue_rgpd_telephone',
          label: 'Téléphone du délégué',
          type: 'PHONE'
        },
        {
          id: 'delegue_rgpd_mail',
          label: 'Email du délégué RGPD',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'paie_externe',
          label: 'La paie des employés est confiée à un sous-traitant',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'rgpd_intranet',
          label: "La politique générale de la RGPD est sur l'intranet de l'entreprise",
          type: 'SELECT-BINARY'
        },
        {
          description:
            'Par exemple Service gestion/comptabilité/location de l’agence; locataire; Organisme financier teneur du compte du propriétaire; Auxiliaires de justice et officiers ministériels dans le cadre de leur mission de recouvrement de créances',
          id: 'delegue_rgpd_organisme_liste',
          label: 'Liste des organismes destinataires des données à caractère personnel',
          type: 'TEXTAREA'
        },
        {
          id: 'modalites_paiement',
          label: "Modalités de paiement acceptées par l'agence",
          type: 'TEXT'
        }
      ],
      id: '4230d1b8_3e53_437f_b364_aec3ef240bb5',
      label: "Informations sur l'Agence / le Réseau",
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'carte_transaction',
              label: 'Carte de transaction'
            },
            {
              id: 'carte_delegation',
              label: 'Délégation de carte de transaction'
            },
            {
              id: 'carte_gestion',
              label: 'Carte de Gestion'
            },
            {
              id: 'aucune_carte',
              label: 'Aucune carte'
            }
          ],
          id: 'agence_carte_partenariat',
          label: 'Titulaire de carte professionnelle',
          multiple: true,
          type: 'SELECT'
        },
        {
          children: [
            {
              filters: {
                recordOnly: true
              },
              id: 'agence_carte_date',
              label: 'Date de délivrance de la carte pro',
              type: 'DATE'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              conditions: [
                [
                  {
                    type: 'DIFFERENT_TEMPLATES',
                    templates: ['OPERATION__AJP__DOSSIER_DE_LOCATION_AJP']
                  }
                ]
              ],
              filters: {
                recordOnly: true
              },
              id: 'agence_carte_gestion',
              label: "Agence titulaire d'une carte de gestion",
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'agence_carte_cci_statut',
              label: 'Carte pro délivrée par une CCI',
              type: 'SELECT-BINARY'
            }
          ],
          id: 'e987b021_fb1e_47b7_bb7c_be271c31098a',
          label: 'Informations générales',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              filters: {
                recordOnly: true
              },
              id: 'agence_carte_cci',
              label: 'Ville de la CCI délivrant la carte',
              type: 'TEXT'
            },
            {
              id: 'agence_carte_cci_departement',
              label: 'Département de la CCI délivrant la carte pro',
              type: 'TEXT'
            }
          ],
          id: '56a1d759_98cc_4c4a_80c5_e335a57188dd',
          label: 'CONDITION_BLOCK_CCI',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              id: 'agence_carte_prefecture',
              label: 'Ville de la Préfecture',
              type: 'TEXT'
            },
            {
              id: 'agence_carte_departement',
              label: 'Département de délivrance de la carte pro',
              type: 'TEXT'
            }
          ],
          id: 'ee70ddb3_dfb0_44ed_ae95_58282aba85f3',
          label: 'CONDITION_BLOCK_Préfecture',
          type: 'CONDITION_BLOCK'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'agence_garantie_financiere_presence',
              label: 'Garantie financière',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              filters: {
                recordOnly: true
              },
              id: 'agence_fonds_clients_statut',
              label: 'Possibilité de détention de fonds clients',
              type: 'SELECT-BINARY'
            },
            {
              filters: {
                recordOnly: true
              },
              id: 'agence_carte_numero',
              label: 'Numéro de carte pro',
              type: 'TEXT'
            },
            {
              children: [
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_assurance_rcp_nom',
                  label: "Nom de l'établissement d'assurance RCP",
                  type: 'TEXT'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_assurance_rcp_adresse',
                  label: "Adresse de l'établissement d'assurance RCP",
                  type: 'ADDRESS'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_assurance_rcp_police',
                  label: "Numéro de la police d'assurance RCP",
                  type: 'TEXT'
                },
                {
                  id: 'agence_assurance_rcp_montant',
                  label: 'Montant de la garantie RCP',
                  type: 'PRICE'
                },
                {
                  id: 'agence_assurance_rcp_montant_cfp',
                  label: 'Montant de la garantie RCP',
                  suffix: 'CFP',
                  type: 'PRICE'
                },
                {
                  id: 'agence_assurance_rcp_date',
                  label: 'Date de souscription de la RCP',
                  type: 'DATE'
                }
              ],
              id: 'ef2abc97_4107_4c77_97f8_4df84b489505',
              label: 'Responsabilité civile professionnelle - Carte Transaction',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_fonds_garantie_nom',
                  label: "Nom de l'établissement garant",
                  type: 'TEXT'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_fonds_garantie_adresse',
                  label: "Adresse de l'établissement garant",
                  type: 'ADDRESS'
                },
                {
                  id: 'agence_fonds_garantie_numero',
                  label: 'Numéro de la garantie financière',
                  type: 'TEXT'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_fonds_garantie_montant',
                  label: 'Montant de la garantie',
                  type: 'PRICE'
                },
                {
                  choices: [
                    {
                      id: 'non',
                      label: 'NON'
                    },
                    {
                      id: 'oui',
                      label: 'OUI'
                    }
                  ],
                  id: 'agence_etablissement_bancaire',
                  label: 'Informations du compte séquestre indiquées au contrat',
                  type: 'SELECT-BINARY'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_etablissement_bancaire_nom',
                  label: 'Banque titulaire du compte séquestre',
                  type: 'TEXT'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_etablissement_bancaire_adresse',
                  label: 'Adresse la Banque',
                  type: 'ADDRESS'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_etablissement_bancaire_numero',
                  label: 'Numéro de compte ouvert auprès de la banque',
                  type: 'TEXT'
                }
              ],
              conditions: [
                [
                  {
                    conditions: [
                      {
                        id: 'agence_fonds_clients_statut',
                        type: 'DIFFERENT',
                        value: 'non'
                      }
                    ],
                    type: 'DIFFERENT_TEMPLATES',
                    templates: ['OPERATION__C2I__CONTRAT_RECRUTEMENT']
                  }
                ],
                [
                  {
                    type: 'EQUALS_CONTRACT_MODELS',
                    model: ['COTE_PARTICULIERS_IMMOBILIER_VENTE_MANDAT_SERENITE']
                  }
                ],
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__C2I__CONTRAT_RECRUTEMENT']
                  }
                ]
              ],
              id: '0120f018_e27d_451f_946d_1ebefc72daa2',
              label: 'Gestion de fonds clients - Transaction',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'agence_garantie_financiere_nom',
                  label: "Nom de l'établissement de garantie financière",
                  type: 'TEXT'
                },
                {
                  id: 'agence_garantie_financiere_adresse',
                  label: "Adresse de l'établissement de garantie financière",
                  type: 'ADDRESS'
                },
                {
                  id: 'agence_garantie_financiere_montant',
                  label: 'Montant de la garantie financière',
                  type: 'PRICE'
                },
                {
                  id: 'agence_garantie_financiere_numero',
                  label: 'Numéro du contrat de la garantie',
                  type: 'TEXT'
                }
              ],
              id: '1bab8b82_6002_4a4b_a208_48808d17c410',
              label: 'Garantie Financière',
              type: 'CATEGORY'
            }
          ],
          id: '664595fa_8845_4259_92c1_3e110763657c',
          label: 'Informations - Carte de Transaction',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              filters: {
                recordOnly: true,
                qualificationQuestions: true
              },
              id: 'agence_fonds_clients_statut_gestion',
              label: 'Possibilité de détention de fonds clients (carte de gestion)',
              type: 'SELECT-BINARY'
            },
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'agence_carte_location_cci_statut',
              label: 'Carte pro délivrée par une CCI',
              type: 'SELECT-BINARY'
            },
            {
              id: 'agence_carte_numero_gestion',
              label: 'Numéro de carte pro (gestion)',
              type: 'TEXT'
            },
            {
              id: 'agence_carte_date_gestion',
              label: 'Date de délivrance de la carte pro (gestion)',
              type: 'DATE'
            },
            {
              children: [
                {
                  id: 'agence_carte_location_cci',
                  label: 'Ville de la CCI délivrant la carte',
                  type: 'TEXT'
                },
                {
                  id: 'agence_carte_location_cci_departement',
                  label: 'Département de la CCI délivrant la carte pro',
                  type: 'TEXT'
                }
              ],
              id: 'cd839228_6ebe_437e_8208_46cd109d08ea',
              label: 'CONDITION_BLOCK_CCI',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  id: 'agence_carte_location_prefecture',
                  label: 'Ville de la Préfecture',
                  type: 'TEXT'
                },
                {
                  id: 'agence_carte_locaiton_departement',
                  label: 'Département de délivrance de la carte pro',
                  type: 'TEXT'
                }
              ],
              id: 'f71e2a20_1a46_4fb9_a527_320b4ddf7de4',
              label: 'CONDITION_BLOCK_Préfecture',
              type: 'CONDITION_BLOCK'
            },
            {
              filters: {
                recordOnly: true
              },
              id: 'agence_carte_location_numero',
              label: 'Numéro de carte pro',
              type: 'TEXT'
            },
            {
              filters: {
                recordOnly: true
              },
              id: 'agence_carte_location_date',
              label: 'Date de délivrance de la carte',
              type: 'DATE'
            },
            {
              children: [
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_assurance_location_rcp_nom',
                  label: "Nom de l'établissement d'assurance RCP",
                  type: 'TEXT'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_assurance_location_rcp_adresse',
                  label: "Adresse de l'établissement d'assurance RCP",
                  type: 'ADDRESS'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_assurance_location_rcp_police',
                  label: "Numéro de la police d'assurance RCP",
                  type: 'TEXT'
                },
                {
                  id: 'agence_assurance_location_rcp_date',
                  label: 'Date de souscription de la RCP',
                  type: 'DATE'
                }
              ],
              id: 'fff7c106_041f_454e_8de0_2f4f76733fcf',
              label: 'Responsabilité civile professionnelle - Gestion',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_fonds_garantie_location_nom',
                  label: "Nom de l'établissement garant",
                  type: 'TEXT'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_fonds_garantie_location_adresse',
                  label: "Adresse de l'établissement garant",
                  type: 'ADDRESS'
                },
                {
                  id: 'agence_fonds_garantie_location_numero',
                  label: 'Numéro de la garantie financière',
                  type: 'TEXT'
                },
                {
                  filters: {
                    recordOnly: true
                  },
                  id: 'agence_fonds_garantie_location_montant',
                  label: 'Montant de la garantie',
                  type: 'PRICE'
                },
                {
                  id: 'agence_location_etablissement_bancaire_numero',
                  label: 'Numéro de compte ouvert auprès de la banque',
                  type: 'TEXT'
                },
                {
                  id: 'agence_location_etablissement_bancaire_nom',
                  label: 'Banque titulaire du compte séquestre',
                  type: 'TEXT'
                }
              ],
              conditions: [
                [
                  {
                    conditions: [
                      {
                        id: 'agence_fonds_clients_statut_gestion',
                        type: 'DIFFERENT',
                        value: 'non'
                      }
                    ],
                    type: 'DIFFERENT_TEMPLATES',
                    templates: ['OPERATION__C2I__CONTRAT_RECRUTEMENT']
                  }
                ],
                [
                  {
                    type: 'EQUALS_TEMPLATES',
                    templates: ['OPERATION__C2I__CONTRAT_RECRUTEMENT']
                  }
                ]
              ],
              id: '2e2310be_23c1_4b55_8c0e_4a957bcaf144',
              label: 'Gestion de fonds clients - Gestion',
              type: 'CATEGORY'
            }
          ],
          id: '3881619f_f049_4131_9786_088c9175f043',
          label: 'Informations - Carte de Gestion',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              id: 'agence_delegation_carte_numero',
              label: 'Numéro de la carte en délégation',
              type: 'TEXT'
            },
            {
              id: 'agence_delegation_carte_titulaire',
              label: 'Titulaire de la carte initiale',
              type: 'TEXT'
            },
            {
              id: 'agence_delegation_carte_couvert',
              label: 'Sous couvert de',
              type: 'TEXT'
            }
          ],
          id: '607b34e9_76c1_4f0e_afb5_3f5bacf4de0c',
          label: 'Information - Délégation',
          type: 'CATEGORY'
        },
        {
          children: [
            {
              choices: [
                {
                  id: 'non',
                  label: 'NON'
                },
                {
                  id: 'oui',
                  label: 'OUI'
                }
              ],
              id: 'agence_carte_syndic_cci_statut',
              label: 'Carte pro délivrée par une CCI',
              type: 'SELECT-BINARY'
            },
            {
              children: [
                {
                  id: 'agence_carte_syndic_cci',
                  label: 'Ville de la CCI délivrant la carte',
                  type: 'TEXT'
                },
                {
                  id: 'agence_carte_syndic_cci_departement',
                  label: 'Département de la CCI délivrant la carte pro',
                  type: 'TEXT'
                }
              ],
              id: 'cc7af337_9c11_4e1f_baf8_25712b63ffe3',
              label: 'CONDITION_BLOCK_CCI',
              type: 'CONDITION_BLOCK'
            },
            {
              children: [
                {
                  id: 'agence_carte_syndic_prefecture',
                  label: 'Ville de la Préfecture',
                  type: 'TEXT'
                },
                {
                  id: 'agence_carte_locaiton_departement',
                  label: 'Département de délivrance de la carte pro',
                  type: 'TEXT'
                }
              ],
              id: '5ad9cd23_aead_4590_9d42_a4c79a9dc8df',
              label: 'CONDITION_BLOCK_Préfecture',
              type: 'CONDITION_BLOCK'
            },
            {
              id: 'agence_carte_syndic_numero',
              label: 'Numéro de carte pro',
              type: 'TEXT'
            },
            {
              id: 'agence_carte_syndic_date',
              label: 'Date de délivrance de la carte',
              type: 'DATE'
            },
            {
              children: [
                {
                  id: 'agence_assurance_syndic_rcp_nom',
                  label: "Nom de l'établissement d'assurance RCP",
                  type: 'TEXT'
                },
                {
                  id: 'agence_assurance_syndic_rcp_adresse',
                  label: "Adresse de l'établissement d'assurance RCP",
                  type: 'ADDRESS'
                },
                {
                  id: 'agence_assurance_syndic_rcp_telephone',
                  label: "Téléphone de l'établissement",
                  type: 'PHONE'
                },
                {
                  id: 'agence_assurance_syndic_rcp_email',
                  label: "Email de l'établissement",
                  type: 'EMAIL'
                },
                {
                  id: 'agence_assurance_syndic_rcp_police',
                  label: "Numéro de la police d'assurance RCP",
                  type: 'TEXT'
                },
                {
                  id: 'agence_assurance_syndic_rcp_date',
                  label: 'Date de souscription de la RCP',
                  type: 'DATE'
                }
              ],
              id: 'f02039a6_de3c_4573_9da5_f9a90abc2956',
              label: 'Responsabilité civile professionnelle - Syndic',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'agence_fonds_garantie_syndic_nom',
                  label: "Nom de l'établissement titulaire du compte séparé",
                  type: 'TEXT'
                },
                {
                  id: 'agence_fonds_garantie_syndic_adresse',
                  label: "Adresse de l'établissement",
                  type: 'ADDRESS'
                },
                {
                  id: 'agence_fonds_garantie_syndic_numero',
                  label: 'Numéro du compte séparé',
                  type: 'TEXT'
                },
                {
                  id: 'agence_fonds_garantie_syndic_montant',
                  label: 'Montant de la garantie',
                  type: 'PRICE'
                },
                {
                  id: 'agence_assurance_syndic_garantie_fonds_date',
                  label: 'Date de souscription de la Garantie',
                  type: 'DATE'
                }
              ],
              id: '8acb6884_df8c_4be9_8b3d_62633939b152',
              label: 'Gestion de fonds clients - Syndic',
              type: 'CATEGORY'
            },
            {
              children: [
                {
                  id: 'agence_garantie_syndic_financiere_nom',
                  label: "Nom de l'établissement de garantie financière",
                  type: 'TEXT'
                },
                {
                  id: 'agence_garantie_syndic_financiere_adresse',
                  label: "Adresse de l'établissement de garantie financière",
                  type: 'ADDRESS'
                },
                {
                  id: 'agence_garantie_syndic_financiere_telephone',
                  label: "Téléphone de l'établissement",
                  type: 'PHONE'
                },
                {
                  id: 'agence_garantie_syndic_financiere_email',
                  label: "Email de l'établissement",
                  type: 'EMAIL'
                },
                {
                  id: 'agence_garantie_financiere_syndic_montant',
                  label: 'Montant de la garantie financière',
                  type: 'PRICE'
                },
                {
                  id: 'agence_garantie_financiere_syndic_numero',
                  label: 'Numéro du contrat de la garantie',
                  type: 'TEXT'
                },
                {
                  id: 'agence_garantie_financiere_syndic_montant_date',
                  label: 'Date de souscription de la garantie',
                  type: 'DATE'
                }
              ],
              id: 'c3559b9a_b423_4c7a_a44c_6bcb9320c58c',
              label: 'Garantie Financière',
              type: 'CATEGORY'
            }
          ],
          id: '915811b5_7a72_46d7_a9a8_7f19b4a0103e',
          label: 'Informations - Carte de Syndic',
          type: 'CATEGORY'
        },
        {
          conditions: [
            [
              {
                id: 'agence_fonds_clients_statut',
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          id: 'rib_agence_depot_garantie',
          label: 'RIB Agence - Dépôt de garantie',
          type: 'UPLOAD'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'agence_liens_capitalistiques',
          label: "Liens capitalistiques de l'Agence avec un établissement financier",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'agence_liens_capitalistiques',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'agence_liens_capitalistiques_liste',
          label: 'Liste des établissements liés',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'agence_orias',
          label: "L'agence est-elle immatriculée à l'Orias ?",
          type: 'SELECT-BINARY'
        },
        {
          conditions: [
            [
              {
                id: 'agence_orias',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'agence_orias_numero',
          label: "Numéro d'immatriculation à l'Orias",
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'orias_activite_assurance',
              label: 'Assurance'
            },
            {
              id: 'orias_activite_banque',
              label: 'Banque'
            }
          ],
          conditions: [
            [
              {
                id: 'agence_orias',
                value: 'oui',
                type: 'EQUALS'
              }
            ]
          ],
          id: 'agence_orias_activite',
          label: 'Liste des activités Orias',
          multiple: true,
          type: 'SELECT'
        },
        {
          conditions: [
            [
              {
                type: 'EQUALS_TEMPLATES',
                templates: ['OPERATION__KELLER_WILLIAMS__IMMOBILIER_COMMERCIAL']
              }
            ]
          ],
          filters: {
            mustBeIncludedInOperationConfig: true
          },
          id: 'bareme_honoraires',
          label: 'Barème des honoraires',
          type: 'UPLOAD'
        },
        {
          id: 'bareme_honoraires_location_gestion',
          label: 'Barème des honoraires Location / Gestion',
          type: 'UPLOAD'
        },
        {
          id: 'tribunal_competent',
          label: 'Tribunal compétent',
          type: 'TEXT'
        },
        {
          children: [
            {
              id: 'syndicat_caisse_retraite',
              label: 'Caisse de retraite',
              type: 'TEXT'
            },
            {
              id: 'syndicat_caisse_prevoyance',
              label: 'Organisme de prévoyance',
              type: 'TEXT'
            },
            {
              id: 'syndicat_complementaire_sante',
              label: 'Complémentaire santé',
              type: 'TEXT'
            }
          ],
          id: '05085668_e94c_41e9_ba84_42de614b8856',
          label: 'Régimes',
          type: 'CATEGORY'
        }
      ],
      id: '5acf032d_6b56_4ffe_8c29_18bee4599f98',
      label: 'Informations professionnelles',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mediation',
          label: "Désignation d'un médiateur par l'agence",
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'mediateur_nom',
          label: 'Nom du médiateur',
          type: 'TEXT'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'mediateur_adresse',
          label: 'Adresse du médiateur',
          type: 'ADDRESS'
        },
        {
          id: 'mediateur_coordonnees',
          label: 'Coordonnées du médiateur',
          type: 'TEXT'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          id: 'mediateur_site_statut',
          label: 'Le médiateur possède un site internet',
          type: 'SELECT-BINARY'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'mediateur_site',
          label: 'Site internet du médiateur',
          type: 'TEXT'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'mediateur_email',
          label: 'Email du médiateur',
          type: 'TEXT'
        },
        {
          filters: {
            recordOnly: true
          },
          id: 'mediateur_telephone',
          label: 'Téléphone du mediateur',
          type: 'PHONE'
        },
        {
          id: 'mediateur_interne_nom',
          label: 'Personne en interne en charge du traitement des réclamations',
          type: 'TEXT'
        },
        {
          description: 'Maximum 8 jours',
          id: 'mediation_temps_reponse',
          label: 'Temps de réponse en cas de réclamation',
          max: 8,
          suffix: 'jours',
          type: 'NUMBER'
        }
      ],
      id: '2c5ca937_1edf_46e9_863a_81c06f4fa9b6',
      label: 'Médiation',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'tranche_0_50000',
          label: 'Honoraires pour la tranche 0 à 50.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_50001_75000',
          label: 'Honoraires pour la tranche 50.001 à 75.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_75001_100000',
          label: 'Honoraires pour la tranche 75.001 à 100.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_100001_125000',
          label: 'Honoraires pour la tranche 100.001 à 125.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_125001_150000',
          label: 'Honoraires pour la tranche 125.001 à 150.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_150001_175000',
          label: 'Honoraires pour la tranche 150.001 à 175.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_175001_200000',
          label: 'Honoraires pour la tranche 175.001 à 200.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_200001_225000',
          label: 'Honoraires pour la tranche 200.001 à 225.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_225001_250000',
          label: 'Honoraires pour la tranche 225.001 à 250.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_250001_275000',
          label: 'Honoraires pour la tranche 250.001 à 275.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_275001_300000',
          label: 'Honoraires pour la tranche 275.001 à 300.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_300001_350000',
          label: 'Honoraires pour la tranche 300.001 à 350.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_350001_400000',
          label: 'Honoraires pour la tranche 350.001 à 400.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_400001_450000',
          label: 'Honoraires pour la tranche 400.001 à 450.000 €',
          type: 'PRICE'
        },
        {
          id: 'tranche_450001',
          label: 'Pourcentage des honoraires pour la tranche supérieure à 450.001 €',
          suffix: '%',
          type: 'NUMBER'
        },
        {
          id: 'tranche_terrain',
          label: 'Pourcentage des honoraires pour les terrains',
          suffix: '%',
          type: 'NUMBER'
        }
      ],
      id: '1421f8a5_d662_406a_85f7_ca1c73651842',
      label: 'Barème',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'donnees_personnelles_personne_responsable',
          label: "Personne responsable du traitement informatique de l'agence",
          type: 'TEXT'
        },
        {
          id: 'donnees_personnelles_duree_conservation',
          label: 'Durée de conservation des informations',
          type: 'TEXT'
        },
        {
          id: 'donnees_personnelles_rectification',
          label: "Adresse de contact pour droit d'accès aux données personnelles",
          type: 'TEXT'
        }
      ],
      id: 'a23ab4b3_d595_4bcc_8613_0373dbad1427',
      label: 'Protection des données personnelles',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          choices: [
            {
              id: 'charge_vendeur',
              label: 'du Vendeur'
            },
            {
              id: 'charge_acquereur',
              label: "de l'Acquéreur"
            },
            {
              id: 'charge_double',
              label: "du Vendeur et de l'Acquéreur"
            }
          ],
          id: 'charge_honoraires',
          label: "Les honoraires de l'agence sont en principe à la charge",
          type: 'SELECT'
        }
      ],
      id: 'aee26b0e_cdf9_4b3e_a56a_0e9c7e36a07d',
      label: 'Information précontractuelle',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'numero_ics',
          label: 'Identifiant du Créancier (ICS)',
          type: 'TEXT'
        }
      ],
      id: '67677eec_e566_4cbd_8604_e969adb085ff',
      label: 'Prélèvement Mandat SEPA',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'viager_point_valeur',
          label: "Valeur du point de l'indice des prix à la consommation",
          type: 'TEXT'
        },
        {
          id: 'viager_point_annee',
          label: "Mois et année de publication de l'IPC",
          type: 'TEXT'
        }
      ],
      id: 'c95baf14_60d2_4506_a2f7_ce41913dc571',
      label: 'Informations Viager',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          id: 'tracfin_referent',
          label: "Référent TRACFIN à l'agence",
          type: 'TEXT'
        },
        {
          description: 'Montant au délà duquel la vigilance doit être renforcée',
          id: 'tracfin_moyenne_prix',
          label: 'Montant moyen des prix de vente',
          type: 'PRICE'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: "La vigilance sera renforcée en cas d'absence d'emprunt si la réponse est non",
          id: 'tracfin_acquereur_emprunt',
          label: 'Il est fréquent que les acquéreurs ne fassent pas recours à un prêt',
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: "La vigilance sera renforcée si l'Acquéreur est une SCI",
          id: 'tracfin_acquereur_morale',
          label: "Il est fréquent que l'acquéreur soit une personne morale",
          type: 'SELECT-BINARY'
        },
        {
          choices: [
            {
              id: 'non',
              label: 'NON'
            },
            {
              id: 'oui',
              label: 'OUI'
            }
          ],
          description: "La vigilance sera renforcée si un client n'est pas Français",
          id: 'tracfin_client_nationalite',
          label: 'Il est fréquent que les clients soient de nationalité étrangère',
          type: 'SELECT-BINARY'
        },
        {
          description:
            "Par exemple : couple français d'une trentaine d'années ; acquisition pour résidence principale ; primo accédant ; apport d'environ 10% du prix de vente",
          id: 'tracfin_profil_acquereur',
          label: "Profil type de l'acquéreur",
          type: 'TEXT'
        },
        {
          description:
            "Par exemple : couple français d'une cinquantaine d'années ; vente pour un Bien plus grand ou plus petit...",
          id: 'tracfin_profil_vendeur',
          label: 'Profil type du vendeur',
          type: 'TEXT'
        },
        {
          description:
            "la rapidité des opérations immobilières sur un même bien; annulations subites d’achats immobiliers; immobilier commercial qui n'est pas courant à l'agence; immobilier de prestige qui n'est pas courant etc.",
          id: 'tracfin_risque_lcb',
          label: "Risques identifiés à l'agence",
          type: 'TEXT'
        }
      ],
      id: 'a2f9ea4c_dd8f_4b2e_a75e_6ff2ade28de7',
      label: 'Information Tracfin',
      type: 'CATEGORY'
    },
    {
      children: [
        {
          conditions: [
            [
              {
                id: 'mandat_gestion_assurance_gli',
                recordPath: ['OPERATION__IMMOBILIER__LOCATION__FICHES', 'MANDAT', '0'],
                type: 'EQUALS',
                value: 'oui'
              }
            ]
          ],
          filters: {
            recordOnly: true
          },
          id: 'agence_gestion_gli_doc',
          label: 'Notice - Assurance Loyers Impayés',
          type: 'UPLOAD'
        }
      ],
      id: 'a2f9ea7u_dd3d_4b6y_asde_6ff2fde2fde7',
      label: 'Gestion',
      type: 'CATEGORY'
    },
    {
      id: 'divers',
      label: 'Divers',
      type: 'CATEGORY'
    }
  ],
  id: 'RECORD__PERSONNE__MORALE__AGENT_IMMOBILIER',
  label: 'Agent immobilier - Personne Morale',
  mynotaryTemplate: false,
  originTemplate: 'RECORD__PERSONNE__MORALE__AGENT_IMMOBILIER',
  specificTypes: ['PERSONNE', 'MORALE', 'AGENT_IMMOBILIER'],
  type: 'RECORD'
};
