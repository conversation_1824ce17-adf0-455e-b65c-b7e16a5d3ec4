import { FormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { isUploadQuestion } from './legal-record-form-typeguards';
import { LegalRecordTemplateId } from '@mynotary/crossplatform/legal-templates/api';
import { getLegalRecordTemplate } from './legal-record-template-map';
import { forEach, uniqBy } from 'lodash';

/**
 * Interface representing a simplified version of a legal record form.
 * This structure is used to expose our forms to partners who are developing
 * interconnections with our tools.
 */
export interface RecordFormDescription {
  documents: FormQuestion[];
  questions: FormQuestion[];
}

/**
 * Returns a simplified version of a legal record form that can be exposed to partners
 * who are developing interconnections with our tools.
 *
 * This function provides a lightweight version of our forms by removing complex elements
 * such as conditions and repeat that would be too complicated for external integration purposes.
 *
 * @param legalRecordTemplateId - The ID of the legal record template to retrieve
 * @returns A simplified form description containing only the essential questions and documents
 */
export function getLegalRecordFormDescription(legalRecordTemplateId: LegalRecordTemplateId): RecordFormDescription {
  const legalRecordTemplate = getLegalRecordTemplate(legalRecordTemplateId);
  const recordFormDescription: RecordFormDescription = {
    documents: [],
    questions: []
  };
  forEach(legalRecordTemplate.form, (formNode) => {
    fillForm(recordFormDescription, formNode);
  });
  return {
    documents: uniqBy(recordFormDescription.documents, (document) => document.id),
    questions: uniqBy(recordFormDescription.questions, (question) => question.id)
  };
}

/**
 * Helper function that recursively processes form nodes to extract questions and documents
 * while removing complex elements like conditions and repeating sections.
 *
 * @param formDescription - The form description object being populated
 * @param formNode - The current form node being processed
 */
export function fillForm(formDescription: RecordFormDescription, formNode: FormQuestion): void {
  if (!formNode.children) {
    return;
  }

  for (const element of formNode.children) {
    if (isUploadQuestion(element)) {
      formDescription.documents.push({
        ...element,
        children: undefined,
        conditionalTitles: undefined,
        conditions: undefined,
        filters: undefined
      });
    } else if (element.type !== null && element.type !== 'CATEGORY') {
      formDescription.questions.push({
        ...element,
        children: undefined,
        conditionalTitles: undefined,
        conditions: undefined,
        filters: undefined,
        // @ts-ignore
        register: undefined
      });
    }

    if (element.children !== null && element.type !== 'REPEAT') {
      fillForm(formDescription, element);
    }
  }
}
