import { Element<PERSON><PERSON>, Text<PERSON><PERSON> } from '@udecode/plate';

export { withProps } from '@udecode/cn';

export const isElement = ElementApi.isElement;
export const isText = TextApi.isText;

export {
  useFocused,
  useSelected,
  PlaceholderProps,
  PlateContent,
  PlateElementProps,
  createPlatePlugin,
  useEditorReadOnly,
  useEditorState,
  useMarkToolbarButton,
  useMarkToolbarButtonState,
  usePlaceholderState,
  useRemoveNodeButton,
  createNodeHOC,
  createNodesHOC,
  createPlateEditor,
  Plate,
  PlateEditor,
  PlateElement,
  ParagraphPlugin,
  PlateLeaf,
  useElement,
  PlatePluginContext,
  useReadOnly
} from '@udecode/plate/react';

export { NodeIdPlugin } from '@udecode/plate-node-id';

export {
  PathApi,
  Hotkeys,
  Descendant,
  TElement,
  NodeEntry,
  Value,
  deserializeHtml,
  DebugPlugin,
  NodeComponent,
  At,
  EditorAfterOptions,
  EditorBeforeOptions,
  <PERSON>,
  <PERSON><PERSON><PERSON>,
  TextUnit,
  AnyPluginConfig
} from '@udecode/plate';

export { HeadingPlugin } from '@udecode/plate-heading/react';
export { HEADING_KEYS } from '@udecode/plate-heading';
export {
  LinkPlugin,
  useLink,
  FloatingLinkUrlInput,
  LinkFloatingToolbarState,
  LinkOpenButton,
  submitFloatingLink,
  useFloatingLinkEdit,
  useFloatingLinkEditState,
  useFloatingLinkInsert,
  useFloatingLinkInsertState
} from '@udecode/plate-link/react';

export { TLinkElement } from '@udecode/plate-link';

export { IndentPlugin } from '@udecode/plate-indent/react';

export {
  ListItemPlugin,
  BulletedListPlugin,
  NumberedListPlugin,
  useListToolbarButton,
  useListToolbarButtonState,
  ListPlugin
} from '@udecode/plate-list/react';

export { ImagePlugin, useMediaState, Image } from '@udecode/plate-media/react';
export { insertImage, TImageElement } from '@udecode/plate-media';

export {
  useTableElement,
  useTableCellElement,
  TablePlugin,
  TableCellPlugin,
  TableRowPlugin,
  TableCellHeaderPlugin,
  useTableCellElementResizable
} from '@udecode/plate-table/react';
export {
  deleteRow,
  deleteColumn,
  insertTable,
  insertTableRow,
  TTableCellElement,
  deleteTable,
  insertTableColumn
} from '@udecode/plate-table';
export { ResizeHandle, Resizable, ResizableProvider, useResizableStore } from '@udecode/plate-resizable';
export { BoldPlugin, ItalicPlugin, UnderlinePlugin, StrikethroughPlugin } from '@udecode/plate-basic-marks/react';

// Colors
export { useColorDropdownMenuState, useColorDropdownMenu } from '@udecode/plate-font/react';
export { FontColorPlugin, FontBackgroundColorPlugin } from '@udecode/plate-font/react';

// Alignment
export { useAlignDropdownMenu, useAlignDropdownMenuState } from '@udecode/plate-alignment/react';
export { AlignPlugin } from '@udecode/plate-alignment/react';

export { ResetNodePlugin } from '@udecode/plate-reset-node/react';
export { SoftBreakPlugin } from '@udecode/plate-break/react';
export { DocxPlugin } from '@udecode/plate-docx';
export {
  useFloatingToolbar,
  useFloatingToolbarState,
  UseVirtualFloatingOptions,
  offset,
  flip
} from '@udecode/plate-floating';
export { useComposedRef, withRef } from '@udecode/react-utils';
