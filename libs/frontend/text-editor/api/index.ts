export {
  TextEditorTagPlugin,
  plateFixedToolbarDomId,
  BalloonToolbar,
  FixedToolbar,
  HeadingElement,
  ImageElement,
  LinkElement,
  LinkFloatingToolbar,
  ListElement,
  ParagraphElement,
  TableCellElement,
  TableCellHeaderElement,
  TableElement,
  TableRowElement,
  TextEditorReadOnlyContext
} from '@mynotary/frontend/text-editor/feature';

export {
  useFocused,
  useSelected,
  isElement,
  TElement,
  FormattedText,
  convertLabelPatternToText,
  ParagraphPlugin,
  TextEditorAnswer,
  createPlateEditor,
  Plate,
  PlateContent,
  PlateEditor,
  resetEditor,
  createNodeHOC,
  createNodesHOC,
  PlaceholderProps,
  NodeComponent,
  usePlaceholderState,
  AlignPlugin,
  BoldPlugin,
  BulletedListPlugin,
  DebugPlugin,
  DocxPlugin,
  FontBackgroundColorPlugin,
  FontColorPlugin,
  HEADING_KEYS,
  HeadingPlugin,
  ImagePlugin,
  IndentPlugin,
  ItalicPlugin,
  LinkPlugin,
  ListItemPlugin,
  ListPlugin,
  NumberedListPlugin,
  PlateElement,
  PlateLeaf,
  ResetNodePlugin,
  SoftBreakPlugin,
  StrikethroughPlugin,
  TableCellHeaderPlugin,
  TableCellPlugin,
  TablePlugin,
  NodeEntry,
  TableRowPlugin,
  UnderlinePlugin,
  withProps,
  createPlatePlugin,
  Value,
  deserializeHtml,
  isText,
  At,
  EditorAfterOptions,
  EditorBeforeOptions,
  Point,
  PointApi,
  TextUnit,
  NodeIdPlugin,
  useReadOnly,
  PathApi,
  Descendant,
  deleteColumn,
  deleteRow,
  deleteTable,
  insertEmptySpace,
  insertTable,
  insertTableColumn,
  insertTableRow,
  isElementSelected,
  useEditorReadOnly,
  useEditorState,
  PlateElementProps,
  AnyPluginConfig
} from '@mynotary/frontend/text-editor/core';
