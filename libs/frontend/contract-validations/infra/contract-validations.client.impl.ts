import { createAxiosInstance } from '@mynotary/frontend/shared/axios-util';
import {
  ContractValidationNew,
  ContractValidationRequestCreated,
  ContractValidationRequestDeleted,
  ContractValidationRequestNew,
  ContractValidationsClient
} from '@mynotary/frontend/contract-validations/core';
import { ContractValidationNewDto, TaskDeletedDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { environment } from '@mynotary/frontend/shared/environments-util';
import { ContractValidationRequestNewDto, TaskDto } from '@mynotary/crossplatform/api-mynotary/openapi';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { TaskType } from '@mynotary/crossplatform/legals/api';

export class ContractValidationsClientImpl implements ContractValidationsClient {
  apiClient = createAxiosInstance({ baseURL: environment.apiMyNotaryUrl });

  async createValidationRequest(args: ContractValidationRequestNew): Promise<ContractValidationRequestCreated> {
    const task = await this.apiClient.post<TaskDto>(`/contract-validation-requests`, {
      contractId: args.contractId,
      description: args.description,
      emailAssignees: args.emailAssignees,
      emailContent: args.emailContent,
      emailSubject: args.emailSubject,
      title: args.title,
      userId: args.userId
    } satisfies ContractValidationRequestNewDto);

    const commonField = {
      assignees: task.data.assignees.map((assignee) => ({
        taskId: parseInt(task.data.id),
        user: {
          email: assignee.email,
          firstname: assignee.firstname,
          id: assignee.id ? parseInt(assignee.id) : undefined,
          lastname: assignee.lastname
        }
      })),
      creationTime: new Date(task.data.creationTime).getTime(),
      creatorUser: {
        email: task.data.creatorEmail,
        firstname: task.data.creatorFirstname,
        lastname: task.data.creatorLastname,
        phone: task.data.creatorPhone,
        profilePictureFileId: task.data.creatorProfilePictureFileId
      },
      creatorUserId: parseInt(task.data.creatorUserId),
      description: task.data.description,
      emails: task.data.assignees.map((assignee) => assignee.email),
      id: parseInt(task.data.id),
      legalComponentId: parseInt(task.data.legalOperationId),
      organization: {
        address: task.data.organizationAddress,
        id: parseInt(task.data.organizationId),
        name: task.data.organizationName
      },
      seen: task.data.seen,
      title: task.data.title
    };

    assertNotNull(task.data.contractId, 'contractId');
    return {
      task: {
        ...commonField,
        reference: {
          contractId: parseInt(task.data.contractId)
        },
        type: TaskType.VALIDATE_CONTRACT
      }
    };
  }

  async deleteValidationRequest(contractId: string): Promise<ContractValidationRequestDeleted> {
    const { data } = await this.apiClient.delete<TaskDeletedDto>(`/contract-validation-requests/${contractId}`);

    return {
      deletedTaskIds: data.deletedTaskIds
    };
  }

  async createValidation(dto: ContractValidationNew): Promise<void> {
    await this.apiClient.post<void>(`/contract-validations`, {
      contractId: dto.contractId,
      fileId: dto.fileId,
      userId: dto.userId
    } satisfies ContractValidationNewDto);
  }

  async deleteValidation(contractId: string): Promise<void> {
    await this.apiClient.delete<TaskDeletedDto>(`/contract-validation/${contractId}`);
  }
}
