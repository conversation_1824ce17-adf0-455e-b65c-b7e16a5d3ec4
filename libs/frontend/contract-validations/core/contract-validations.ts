import { Task } from '@mynotary/frontend/legals/api';

export interface ContractValidation {
  id: string;
}

export type ContractValidationRequestNew = {
  contractId: string;
  description: string;
  emailAssignees: string[];
  emailContent: string;
  emailSubject: string;
  title: string;
  userId: string;
};

export type ContractValidationNew = {
  contractId: string;
  fileId: string;
  userId: string;
};

export type ContractValidationRequestCreated = {
  task: Task;
};

export type ContractValidationRequestDeleted = {
  deletedTaskIds: string[];
};
