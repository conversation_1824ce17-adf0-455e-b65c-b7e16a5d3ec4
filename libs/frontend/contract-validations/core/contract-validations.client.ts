import {
  ContractValidationNew,
  ContractValidationRequestDeleted,
  ContractValidationRequestNew,
  ContractValidationRequestCreated
} from './contract-validations';

export abstract class ContractValidationsClient {
  abstract createValidationRequest(dto: ContractValidationRequestNew): Promise<ContractValidationRequestCreated>;

  abstract deleteValidationRequest(contractId: string): Promise<ContractValidationRequestDeleted>;

  abstract createValidation(dto: ContractValidationNew): Promise<void>;

  abstract deleteValidation(contractId: string): Promise<void>;
}
