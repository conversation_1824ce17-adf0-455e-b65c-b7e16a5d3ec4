import './validation-workflow-popin.scss';
import { MnValidationPopin } from '@mynotary/frontend/shared/ui';
import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import { useGlobalLoaderClass } from '@mynotary/frontend/shared/util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import {
  generateContractPdf,
  RedactionTab,
  RedactionView,
  selectIsValidating,
  selectValidationIsReady,
  setRedactionTab,
  updateContract,
  validateContract,
  ValidationWorkflow
} from '@mynotary/frontend/legals/api';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { ContractValidationsClient } from '@mynotary/frontend/contract-validations/core';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { changeRedactionView, setIsValidating } from '@mynotary/frontend/legals/api';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { useWhitelist } from '@mynotary/frontend/whitelist/api';
import { FeatureWhiteListed } from '@mynotary/crossplatform/feature-flags/api';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';

interface ValidationWorkflowPopinProps {
  contractId: number;
  isOpen: boolean;
  onClose: () => void;
  operationId: number;
}

export const ValidationWorkflowPopin = ({ contractId, isOpen, onClose, operationId }: ValidationWorkflowPopinProps) => {
  const dispatch = useAsyncDispatch();

  const contractValidationClient = useService(ContractValidationsClient);
  const isWhitelisted = useWhitelist(FeatureWhiteListed.CONTRACT_VALIDATION);

  const userId = useSelector(selectCurrentUser)?.id;
  const validationIsReady = useSelector(selectValidationIsReady(operationId, contractId));
  const isValidating = useSelector(selectIsValidating);
  const { displayGlobalLoader, hideGlobalLoader } = useGlobalLoaderClass();

  useEffect(() => {
    if (isValidating) {
      displayGlobalLoader();
    } else {
      hideGlobalLoader();
    }
  }, [isValidating, displayGlobalLoader, hideGlobalLoader]);

  const handleValidateContract = async (): Promise<void> => {
    assertNotNull(userId, 'userId is null');

    if (isWhitelisted) {
      try {
        dispatch(setIsValidating(true));
        const contractFile = await dispatch(generateContractPdf(contractId, false));
        await contractValidationClient.createValidation({
          contractId: contractId.toString(),
          fileId: contractFile.id,
          userId: userId.toString()
        });
        dispatch(updateContract({ contractFileId: contractFile.id, id: contractId, status: ContractStatus.VALIDATED }));
        dispatch(setRedactionTab(RedactionTab.INFORMATION));
        /** Force to switch display mode to preview and form since we can't display validation + other mode in other contract view   */
        dispatch(changeRedactionView(RedactionView.CONTRACT_VALIDATED));
        dispatch(setIsValidating(false));
        onClose();
      } catch (e) {
        console.error('err', e);
        dispatch(setErrorMessage('Une erreur est survenue lors de la validation du contrat'));
      }
    } else {
      try {
        await dispatch(validateContract(operationId, contractId));
        onClose();
      } catch (e) {
        console.error('err', e);
        dispatch(setErrorMessage('Une erreur est survenue lors de la validation du contrat'));
      }
    }
  };

  return (
    <MnValidationPopin
      onCancel={onClose}
      onValidate={handleValidateContract}
      opened={isOpen}
      validate={'Valider'}
      validateDisabled={!validationIsReady}
    >
      <ValidationWorkflow contractId={contractId} onClosePopin={onClose} operationId={operationId} />
    </MnValidationPopin>
  );
};
