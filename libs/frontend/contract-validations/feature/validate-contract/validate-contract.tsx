import { ReactElement, useState } from 'react';
import { ValidationWorkflowPopin } from './validation-workflow-popin';
import { MnProps } from '@mynotary/frontend/shared/util';
import { resetFormNavigation, selectHasValidationAction } from '@mynotary/frontend/legals/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useSelector } from 'react-redux';

interface TriggerElementProps {
  disabled?: boolean;
  onClick: () => void;
}

interface ValidateContractButtonProps extends MnProps {
  contractId: number;
  onFinish?: () => void;
  operationId: number;
  triggerElement: React.FC<TriggerElementProps>;
}

export const ValidateContractButton = ({
  contractId,
  onFinish,
  operationId,
  triggerElement: TriggerElement
}: ValidateContractButtonProps): ReactElement | null => {
  const [openPopin, setOpenPopin] = useState(false);
  const dispatch = useAsyncDispatch();

  const hasValidationAction = useSelector(selectHasValidationAction(contractId));

  const handleValidate = () => {
    setOpenPopin(true);
    dispatch(resetFormNavigation());
  };

  const handleClose = () => {
    setOpenPopin(false);
    onFinish?.();
  };

  if (!hasValidationAction) {
    return null;
  }

  return (
    <>
      <TriggerElement onClick={handleValidate} />
      <ValidationWorkflowPopin
        contractId={contractId}
        isOpen={openPopin}
        onClose={handleClose}
        operationId={operationId}
      />
    </>
  );
};
