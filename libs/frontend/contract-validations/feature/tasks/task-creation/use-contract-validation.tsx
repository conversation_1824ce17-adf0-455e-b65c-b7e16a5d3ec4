import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { Email } from '@mynotary/frontend/email-editor/api';
import { Task, TaskTypeAndReference, TaskNew } from '@mynotary/frontend/legals/api';
import { postTasks } from '@mynotary/frontend/legals/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';
import { useState } from 'react';
import { convertAnswerToTaskNew } from '@mynotary/frontend/legals/api';
import { useWhitelist } from '@mynotary/frontend/whitelist/api';
import { FeatureWhiteListed } from '@mynotary/crossplatform/feature-flags/api';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { TaskType } from '@mynotary/crossplatform/legals/api';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { useSelector } from 'react-redux';
import { assertNotNull, Exception } from '@mynotary/crossplatform/shared/util';
import { ContractValidationsClient } from '@mynotary/frontend/contract-validations/core';
import { setTasks } from '@mynotary/frontend/legals/api';

interface UseContractValidationProps {
  answer: AnswerDict;
  email: Email | null;
  operationId: number;
  reference: TaskTypeAndReference;
}

export const useContractValidation = ({ answer, email, operationId, reference }: UseContractValidationProps) => {
  const dispatch = useAsyncDispatch();
  const currentUser = useSelector(selectCurrentUser);
  const [isLoading, setIsLoading] = useState(false);
  const contractValidationWhitelist = useWhitelist(FeatureWhiteListed.CONTRACT_VALIDATION);
  const contractValidationClient = useService(ContractValidationsClient);

  const handleValidation = async ({ onFinish }: { onFinish?: (task: Task) => void }): Promise<void> => {
    setIsLoading(true);

    const newTask: TaskNew = convertAnswerToTaskNew({ answer, email, operationId, reference });

    let taskCreated: Task;

    try {
      if (contractValidationWhitelist) {
        if (newTask.type !== TaskType.VALIDATE_CONTRACT) {
          throw new Exception('Task type is not VALIDATE_CONTRACT');
        }

        assertNotNull(currentUser);
        assertNotNull(email, 'email');

        const taskValidation = await contractValidationClient.createValidationRequest({
          contractId: newTask.reference.contractId.toString(),
          description: newTask.description ?? '',
          emailAssignees: newTask.assignees,
          emailContent: email.content,
          emailSubject: email.subject,
          title: newTask.title,
          userId: currentUser.id.toString()
        });
        dispatch(setTasks([taskValidation.task]));

        taskCreated = taskValidation.task;
      } else {
        taskCreated = await dispatch(postTasks(operationId, newTask));
      }
      onFinish?.(taskCreated);
    } catch (error) {
      console.error(error);
      dispatch(setErrorMessage("Une erreur inconnue empêche l'envoi de l'e-mail"));
    } finally {
      setIsLoading(false);
    }
  };

  return { handleValidation, isLoading };
};
