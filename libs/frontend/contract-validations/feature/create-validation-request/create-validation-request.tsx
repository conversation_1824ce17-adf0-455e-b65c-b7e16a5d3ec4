import React, { ReactElement, useState } from 'react';

import { useSelector } from 'react-redux';
import { selectHasCreateValidationRequestAction } from '@mynotary/frontend/legals/api';
import { MnValidationPopin } from '@mynotary/frontend/shared/ui';

import { openSidebarAction, selectCanAskValidation, SidebarActionType } from '@mynotary/frontend/legals/api';
import { TaskType } from '@mynotary/crossplatform/legals/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { ValidationWorkflow } from '@mynotary/frontend/legals/api';

interface TriggerElementProps {
  disabled?: boolean;
  onClick: () => void;
}

type CreateValidationRequestProps = {
  TriggerElement: React.FC<TriggerElementProps>;
  contractId: number;
  onFinish?: () => void;
  operationId: number;
};

export const CreateValidationRequest = ({
  TriggerElement,
  contractId,
  onFinish,
  operationId
}: CreateValidationRequestProps): ReactElement | null => {
  const hasCreateValidationRequestAction = useSelector(selectHasCreateValidationRequestAction(contractId));

  const [isOpen, setOpen] = useState(false);
  const dispatch = useAsyncDispatch();
  const canAskValidation = useSelector(selectCanAskValidation(operationId, contractId));

  const handleValidate = async (): Promise<void> => {
    dispatch(
      openSidebarAction({
        legalComponentId: operationId,
        type: SidebarActionType.TASK_CREATION,
        typeAndReference: {
          reference: { contractId },
          type: TaskType.VALIDATE_CONTRACT
        }
      })
    );
    setOpen(false);
  };

  if (!hasCreateValidationRequestAction) {
    return null;
  }

  const handleOpen = () => {
    setOpen(true);
  };

  return (
    <>
      <TriggerElement onClick={handleOpen} />
      <MnValidationPopin
        onCancel={() => setOpen(false)}
        onValidate={handleValidate}
        opened={isOpen}
        validate={'Demander la validation'}
        validateDisabled={!canAskValidation}
      >
        <ValidationWorkflow contractId={contractId} onClosePopin={() => onFinish} operationId={operationId} />
      </MnValidationPopin>
    </>
  );
};
