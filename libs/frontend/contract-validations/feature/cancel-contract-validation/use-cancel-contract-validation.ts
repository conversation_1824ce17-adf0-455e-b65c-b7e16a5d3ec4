import { useSelector } from 'react-redux';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { selectContractPermission, updateContract } from '@mynotary/frontend/legals/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { useGlobalLoader } from '@mynotary/frontend/shared/util';
import { selectContract } from '@mynotary/frontend/legals/api';
import { updateContractValidationFiles } from '@mynotary/frontend/legals/api';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { ContractValidationsClient } from '@mynotary/frontend/contract-validations/core';
import { useWhitelist } from '@mynotary/frontend/whitelist/api';
import { FeatureWhiteListed } from '@mynotary/crossplatform/feature-flags/api';

interface UseCancelValidation {
  canCancelContractValidation: boolean;
  cancelContractValidation: () => Promise<void>;
}

interface UseCancelContractValidationProps {
  contractId: number;
  operationId: number;
}

export const useCancelContractValidation = ({
  contractId,
  operationId
}: UseCancelContractValidationProps): UseCancelValidation => {
  const contractValidationClient = useService(ContractValidationsClient);

  const dispatch = useAsyncDispatch();
  const [setIsLoading] = useGlobalLoader(false);
  const isWhitelisted = useWhitelist(FeatureWhiteListed.CONTRACT_VALIDATION);

  const contract = useSelector(selectContract(contractId));

  const hasValidateContractPermission = useSelector(
    selectContractPermission(PermissionType.VALIDATE_CONTRACT, contractId)
  );
  const hasForceValidateContractPermission = useSelector(
    selectContractPermission(PermissionType.FORCE_VALIDATE_CONTRACT, contractId)
  );

  const handleCancelValidation = async (): Promise<void> => {
    if (isWhitelisted) {
      setIsLoading(true);
      await contractValidationClient.deleteValidation(contractId.toString());
      dispatch(updateContract({ contractFileId: undefined, id: contractId, status: ContractStatus.REDACTION }));
      setIsLoading(false);
    } else {
      setIsLoading(true);
      try {
        await dispatch(updateContractValidationFiles(operationId, contractId));
      } finally {
        setIsLoading(false);
      }
    }
  };

  const canCancelContractValidation =
    contract.status === ContractStatus.VALIDATED &&
    (hasValidateContractPermission || hasForceValidateContractPermission) &&
    contract?.contractFileId != null;

  return {
    canCancelContractValidation,
    cancelContractValidation: handleCancelValidation
  };
};
