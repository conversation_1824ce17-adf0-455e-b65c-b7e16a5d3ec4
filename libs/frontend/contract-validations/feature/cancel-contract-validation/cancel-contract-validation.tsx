import { useCancelContractValidation } from './use-cancel-contract-validation';

interface TriggerElementProps {
  disabled?: boolean;
  onClick: () => void;
}

interface CancelContractValidationProps {
  contractId: number;
  onFinish?: () => void;
  operationId: number;
  triggerElement: React.FC<TriggerElementProps>;
}

export const CancelContractValidation = ({
  contractId,
  onFinish,
  operationId,
  triggerElement: TriggerElement
}: CancelContractValidationProps) => {
  const { canCancelContractValidation, cancelContractValidation } = useCancelContractValidation({
    contractId,
    operationId
  });

  if (!canCancelContractValidation) {
    return null;
  }

  const handleCancellation = async () => {
    await cancelContractValidation();
    onFinish?.();
  };

  return <TriggerElement onClick={handleCancellation} />;
};
