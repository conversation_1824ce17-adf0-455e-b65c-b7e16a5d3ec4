import React, { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import {
  deleteTask,
  selectCanDeleteTask,
  selectContract,
  selectHasCancelAskValidationAction,
  selectValidateContractTask
} from '@mynotary/frontend/legals/api';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { ContractValidationsClient } from '@mynotary/frontend/contract-validations/core';
import { removeTask } from '@mynotary/frontend/legals/api';
import { useWhitelist } from '@mynotary/frontend/whitelist/api';
import { FeatureWhiteListed } from '@mynotary/crossplatform/feature-flags/api';
import { ContractStatus } from '@mynotary/crossplatform/legals/core';

interface TriggerElementProps {
  disabled?: boolean;
  onClick: () => void;
}

type DeleteValidationRequestProps = {
  contractId: number;
  onFinish?: () => void;
  operationId: number;
  triggerElement: React.FC<TriggerElementProps>;
};

export const DeleteValidationRequest = ({
  contractId,
  onFinish,
  operationId,
  triggerElement: TriggerElement
}: DeleteValidationRequestProps): ReactElement | null => {
  const dispatch = useAsyncDispatch();
  const [status, setStatus] = useState<'idle' | 'deleting' | 'success' | 'error'>('idle');
  const contractValidationClient = useService(ContractValidationsClient);
  const isWhitelisted = useWhitelist(FeatureWhiteListed.CONTRACT_VALIDATION);

  const validateContractTask = useSelector(selectValidateContractTask(contractId));
  const contract = useSelector(selectContract(contractId));
  const hasCancelAskValidationAction = useSelector(selectHasCancelAskValidationAction(contractId));
  const canDeleteTask = useSelector(selectCanDeleteTask(validateContractTask?.id));

  const canDeleteValidationRequest =
    canDeleteTask && contract.status === ContractStatus.VALIDATION_PENDING && hasCancelAskValidationAction;

  async function handleDeleteValidationRequest() {
    if (isWhitelisted) {
      try {
        setStatus('deleting');
        const deleted = await contractValidationClient.deleteValidationRequest(contractId.toString());
        for (const deletedTaskId of deleted.deletedTaskIds) {
          dispatch(removeTask(parseInt(deletedTaskId)));
        }
        setStatus('success');
      } catch {
        setStatus('error');
      }
    } else {
      if (validateContractTask) {
        dispatch(deleteTask(operationId, validateContractTask.id));
      }
    }
    onFinish?.();
  }

  if (!canDeleteValidationRequest) {
    return null;
  }

  return <TriggerElement disabled={status === 'deleting'} onClick={handleDeleteValidationRequest} />;
};
