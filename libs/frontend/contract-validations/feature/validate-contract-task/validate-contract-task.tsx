import React, { ReactElement, useState } from 'react';

import { MnProps } from '@mynotary/frontend/shared/util';

import {
  Form,
  TaskCreationEmail,
  ValidateContractTaskReference,
  TaskCreationContainer,
  TaskCreationEmailValidation,
  TaskCreationForm,
  TaskCreationPreviousStep,
  TaskCreationTitle,
  useTaskCreation
} from '@mynotary/frontend/legals/api';
import { useValidateContractForm } from './use-validate-contract-form';
import { Email } from '@mynotary/frontend/email-editor/api';
import { isEmpty } from 'lodash';
import { MnLoader } from '@mynotary/frontend/shared/ui';
import { useSelector } from 'react-redux';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { assertNotNull } from '@mynotary/crossplatform/shared/util';
import { useWhitelist } from '@mynotary/frontend/whitelist/api';
import { FeatureWhiteListed } from '@mynotary/crossplatform/feature-flags/api';
import { useContractValidation } from '../tasks/task-creation/use-contract-validation';

interface TaskCreationValidateContractProps extends MnProps {
  onContractValidationRequested: () => void;
  operationId: number;
  reference: ValidateContractTaskReference;
}

export const TaskCreationValidateContract = ({
  onContractValidationRequested,
  operationId,
  reference
}: TaskCreationValidateContractProps): ReactElement => {
  const [currentStep, setCurrentStep] = useState<'FORM' | 'EMAIL'>('FORM');
  const [email, setEmail] = useState<Email | null>(null);

  const isWhitelisted = useWhitelist(FeatureWhiteListed.CONTRACT_VALIDATION);

  const user = useSelector(selectCurrentUser);
  const { answer, form, handleAnswerChange, isCompleted } = useValidateContractForm({
    contractId: reference.reference.contractId,
    operationId
  });

  const { handleValidation, isLoading } = useTaskCreation({
    answer,
    email,
    operationId,
    reference
  });

  const { handleValidation: handleValidationWhiteListed, isLoading: isLoadingWhitelisted } = useContractValidation({
    answer,
    email,
    operationId,
    reference
  });

  const handleCreateValidationTask = async () => {
    assertNotNull(user, 'User must be defined');

    if (isWhitelisted) {
      await handleValidationWhiteListed({
        onFinish: onContractValidationRequested
      });
    } else {
      await handleValidation({ onFinish: onContractValidationRequested });
    }
  };

  if (isEmpty(form)) {
    return <MnLoader variant='large' />;
  }

  return (
    <TaskCreationContainer>
      {currentStep === 'EMAIL' && <TaskCreationPreviousStep onClick={() => setCurrentStep('FORM')} />}
      <TaskCreationTitle step={currentStep} />

      {currentStep === 'FORM' && (
        <TaskCreationForm
          hasMail={true}
          isCompleted={isCompleted}
          isLoading={isLoading || isLoadingWhitelisted}
          onClick={() => setCurrentStep('EMAIL')}
        >
          <Form answer={answer} editable={!isLoading} forms={form} onChange={handleAnswerChange} />
        </TaskCreationForm>
      )}
      {currentStep === 'EMAIL' && (
        <>
          <TaskCreationEmail onEmailChange={setEmail} operationId={operationId} typeAndReference={reference} />
          <TaskCreationEmailValidation
            disabled={isLoading || !email || isLoadingWhitelisted}
            isLoading={isLoading || isLoadingWhitelisted}
            onClick={handleCreateValidationTask}
          />
        </>
      )}
    </TaskCreationContainer>
  );
};
