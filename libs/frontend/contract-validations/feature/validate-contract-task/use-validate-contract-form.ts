import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { ContractValidators, selectCurrentContractValidators } from '@mynotary/frontend/contract-validators/api';
import {
  computeVisibleProgression,
  filterForm,
  mergeAndCopyAnswer,
  getTaskDefaultAnswer,
  taskAssignees,
  taskDescription,
  taskTitle,
  useTaskAssignees
} from '@mynotary/frontend/legals/api';
import { TaskType } from '@mynotary/crossplatform/legals/api';
import { isEmpty, map } from 'lodash';
import { useState } from 'react';
import { useSelector } from 'react-redux';
import { User } from '@mynotary/frontend/shared/util';
import {
  DateFormQuestion,
  EmailFormQuestion,
  FormQuestion,
  SelectFormQuestion
} from '@mynotary/crossplatform/shared/forms-util';

interface UseValidateContractFormProps {
  contractId: number;
  operationId: number;
}

export const useValidateContractForm = ({ contractId, operationId }: UseValidateContractFormProps) => {
  const contractValidators = useSelector(selectCurrentContractValidators);
  const { assignees, isAssigneesLoading } = useTaskAssignees({
    contractId,
    hasDefaultAssignees: !isEmpty(contractValidators?.users),
    operationId,
    taskType: TaskType.VALIDATE_CONTRACT
  });

  const [answer, setAnswer] = useState<AnswerDict>(
    getTaskDefaultAnswer({ assignees: contractValidators?.users, type: TaskType.VALIDATE_CONTRACT })
  );

  const form = getValidateContractTaskForm({ answer, assignees, isAssigneesLoading, validators: contractValidators });

  const handleAnswerChange = (update: AnswerDict): void => {
    const fullAnswer = mergeAndCopyAnswer(answer, update);
    setAnswer(fullAnswer);
  };

  const { mandatoryFilled, mandatoryTotal } = computeVisibleProgression(form, answer);
  const isCompleted = mandatoryFilled === mandatoryTotal;

  if (isAssigneesLoading) {
    return {
      answer,
      form: [],
      handleAnswerChange,
      isCompleted
    };
  }

  return {
    answer,
    form,
    handleAnswerChange,
    isCompleted
  };
};

const getValidateContractTaskForm = (args: GetValidateContractTaskFormArgs) => {
  if (args.isAssigneesLoading && isEmpty(args.validators?.users)) {
    return [];
  }

  const options = convertToOptions({
    assignees: args.assignees,
    contractValidators: args.validators
  });

  const assigneeQuestion: EmailFormQuestion = {
    ...taskAssignees({ multiple: true }),
    conditions: undefined,
    disabled: args.validators?.locked,
    fetchContacts: true,
    options
  };

  const form: Array<FormQuestion | SelectFormQuestion | DateFormQuestion | EmailFormQuestion> = [
    taskTitle,
    taskDescription,
    assigneeQuestion
  ];
  return filterForm(form, args.answer, { CONDITION: {} });
};

const convertToOptions = (args: {
  assignees: User[];
  contractValidators?: ContractValidators;
}): { email: string; id: number; nom: string; prenoms: string }[] => {
  if (args.contractValidators != null && !isEmpty(args.contractValidators?.users)) {
    return map(args.contractValidators.users, (assignee, key) => ({
      email: assignee.email,
      id: key,
      nom: '',
      prenoms: assignee.fullName
    }));
  }

  return map(args.assignees, (assignee, key) => ({
    email: assignee.email,
    id: key,
    nom: assignee.lastname,
    prenoms: assignee.firstname
  }));
};

interface GetValidateContractTaskFormArgs {
  answer: AnswerDict;
  assignees: User[];
  isAssigneesLoading: boolean;
  validators?: ContractValidators;
}
