import { map } from 'lodash';
import { inject } from '@mynotary/frontend/shared/injector-util';
import { PlacesClient } from './places.client';

type PredictionResponse = { placePrediction: google.maps.places.PlacePrediction | null };
type ValidPredictionResponse = { placePrediction: google.maps.places.PlacePrediction };
type AdressPrediction = { label: string; value: google.maps.places.PlacePrediction };

/**
 * Type guard to check if a prediction has a non-null placePrediction.
 */
function hasPlacePrediction(prediction: PredictionResponse): prediction is ValidPredictionResponse {
  return prediction.placePrediction !== null;
}

/**
 * Get address predictions using google places api.
 * We use boundaries to restrict the search to France.
 * ne = 51.0891, 8.2306 are the coordinates of the most northern and eastern point of France
 * sw = 42.3327, -4.79556 are the coordinates of the most southern and western point of France
 * @param value partial address entered by the user
 */
export const getAdressPredictions = async (value: string): Promise<AdressPrediction[]> => {
  if (value.length < 2) {
    return [];
  }
  const placesClient = inject(PlacesClient);
  const response = await placesClient.getPlacePredictions(value);

  const filteredResponse = response.filter(hasPlacePrediction);

  return map(filteredResponse, (prediction) => ({
    label: prediction.placePrediction.text.text,
    value: prediction.placePrediction
  }));
};
