import { formatAddress, FormattedLocation, MnAddress } from '@mynotary/crossplatform/shared/util';
import { inject } from '@mynotary/frontend/shared/injector-util';
import { BanDto, PlacesClient } from './places.client';
import { deburr } from 'lodash';

export const getPlaceDetails = async (
  prediction: google.maps.places.PlacePrediction,
  callback: (address: MnAddress | null) => void
) => {
  const placesClient = inject(PlacesClient);

  const place = prediction.toPlace();

  const geocodeResponse = await placesClient.geocode({
    placeId: place.id
  });

  const firstResult = geocodeResponse.results[0];

  if (!firstResult) {
    return callback({ address: prediction.text.text });
  }

  if (isAddressInFrance(firstResult)) {
    await handleFrenchAddress({ callback: callback, geocodeResult: firstResult, prediction: prediction });
  } else {
    callback(convertToAddress(firstResult, prediction));
  }
};

const isAddressInFrance = (result: google.maps.GeocoderResult): boolean => {
  const countryComponent = result.address_components.find((component) => component.types.includes('country'));
  return countryComponent?.short_name === 'FR';
};

const handleFrenchAddress = async ({
  callback,
  geocodeResult,
  prediction
}: {
  callback: (address: MnAddress) => void;
  geocodeResult: google.maps.GeocoderResult;
  prediction: google.maps.places.PlacePrediction;
}): Promise<void> => {
  const placesClient = inject(PlacesClient);
  const address = convertToAddress(geocodeResult, prediction);

  try {
    const banResult = await placesClient.banSearch(prediction.text.text);

    /**
     * The api might return addresses that are thousand kilometers away from the original city
     * so we use the city as a filter to get the closest address
     */

    const filteredBanResult = banResult.features.filter(
      (feature) => deburr(feature.properties.city).toLowerCase() === deburr(address?.city).toLowerCase()
    );

    if (!filteredBanResult.length) {
      callback(address);
    } else {
      const firstFeature = filteredBanResult[0];
      callback(convertBANtoAddress(firstFeature, address));
    }
  } catch (error) {
    console.error('Error handling French address:', error);
    callback(address);
  }
};

const convertToAddress = (
  result: google.maps.GeocoderResult,
  prediction: google.maps.places.PlacePrediction
): MnAddress => {
  const zip = extractStringData(result, 'postal_code');
  const city = extractStringData(result, 'locality') ?? extractStringData(result, 'postal_town');
  const country = extractStringData(result, 'country');
  const street = extractStringData(result, 'route');
  const streetNumber = extractStringData(result, 'street_number');
  const place = prediction.toPlace();
  return {
    address: prediction.text.text,
    city,
    country,
    formattedAddress: formatAddress({ city, country, street, streetNumber, zip }),
    location: extractGeometryData(result),
    placeId: place.id,
    street,
    streetNumber,
    zip
  };
};

const convertBANtoAddress = (feature: BanDto, address: MnAddress) => {
  const { departmentCode, departmentName, regionName } = extractContextData(feature.properties.context);

  return {
    ...address,
    cityCode: feature.properties.citycode,
    departmentCode,
    departmentName,
    regionName
  };
};

const extractStringData = (result: google.maps.GeocoderResult, path: string): string | undefined => {
  if (result?.address_components) {
    const component = result.address_components.find((comp: google.maps.GeocoderAddressComponent) => {
      return comp.types.includes(path);
    });
    return component ? component.long_name : undefined;
  }
  return undefined;
};

const extractGeometryData = (result: google.maps.GeocoderResult | null): FormattedLocation | undefined => {
  if (result?.geometry) {
    return {
      lat: result.geometry.location?.lat() as number,
      lng: result.geometry.location?.lng() as number
    };
  }
  return undefined;
};

const extractContextData = (
  context?: string
): { departmentCode?: string; departmentName?: string; regionName?: string } => {
  // context should be like "departmentCode, departmentName, regionName"
  if (!context || /^[0-9]{2-3}, .*(, .*)?$/.test(context)) {
    return {};
  }
  const [departmentCode, departmentName, regionName] = context.split(', ');
  return { departmentCode, departmentName, regionName };
};
