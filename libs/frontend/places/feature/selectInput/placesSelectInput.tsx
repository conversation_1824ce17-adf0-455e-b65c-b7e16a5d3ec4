import './placesSelectInput.scss';
import React, { ReactElement, useCallback, useEffect, useMemo, useState } from 'react';
import AsyncSelect from 'react-select/async';
import { InputActionMeta, ValueType } from 'react-select/src/types';
import { PlaceNoOption } from './placeNoOption/placeNoOption';
import { PlaceOption } from './placeOption/placeOption';
import { PlaceMenuList } from './placeMenuList/placeMenuList';
import { MnProps, classNames, isIOS } from '@mynotary/frontend/shared/util';
import { MnAddress } from '@mynotary/crossplatform/shared/util';
import { debounce } from 'lodash';
import { MnButtonIcon, MnLoadingBar } from '@mynotary/frontend/shared/ui';
import { getAdressPredictions, getPlaceDetails } from '@mynotary/frontend/places/core';

interface PlacesSelectInputProps extends MnProps {
  disabled: boolean;
  displayCustomForm: (isVisble: boolean) => void;
  id?: string;
  onChange: (address: MnAddress | null) => void;
  placeholder: string;
  required: boolean;
  value: MnAddress | null;
}

export interface SelectInputOption {
  country?: string;
  district?: string;
  label: string;
  value: google.maps.places.PlacePrediction;
  zip?: string;
}

const PlacesSelectInput = ({
  disabled,
  displayCustomForm,
  id,
  onChange,
  placeholder,
  required,
  value
}: PlacesSelectInputProps): ReactElement => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(false);
  const [inputValue, setInputValue] = useState(value?.formattedAddress ?? '');
  const [selectValue, setSelectValue] = useState<ValueType<SelectInputOption>>();
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  useEffect(() => {
    if (value?.formattedAddress) {
      setInputValue(value.formattedAddress);
      console.log('value', value);
      setSelectValue({ label: value.formattedAddress } as SelectInputOption);
    } else {
      setInputValue('');
      setSelectValue(null);
    }
  }, [value]);

  const handleLoadOptions = useMemo(
    () =>
      debounce((inputValue: string, setOptions: (options: SelectInputOption[]) => void): void => {
        if (inputValue) {
          getAdressPredictions(inputValue).then((res: SelectInputOption[]) => {
            setLoading(false);
            setOptions(res);
          });
        }
      }, 1500),
    []
  );

  const handleInputChange = useCallback(
    (inputValue: string, { action }: InputActionMeta): void => {
      if (action === 'input-change') {
        if (inputValue) {
          setLoading(true);
        } else {
          onChange(null);
        }

        setSelectValue(null);
        setError(false);
        setInputValue(inputValue);
      }
    },
    [onChange]
  );

  const handleChange = useCallback(
    async (selectedOption: ValueType<SelectInputOption>) => {
      if (selectedOption != null) {
        setInputValue((selectedOption as SelectInputOption).label);
        const option = selectedOption as SelectInputOption;
        await getPlaceDetails(option.value, onChange);
      } else {
        setInputValue('');
        onChange(null);
      }
      console.log('selectedOption', selectedOption);
      setSelectValue(selectedOption);
    },
    [onChange]
  );

  const handleBlur = useCallback((): void => {
    setLoading(false);

    if (!selectValue) {
      setError(true);
    }
  }, [selectValue]);

  const handleCustomFormDisplay = useCallback((): void => {
    setInputValue('');
    displayCustomForm(true);
    setIsMenuOpen(false);
  }, [displayCustomForm]);

  const components = useMemo(
    () => ({
      ClearIndicator: () => (
        <MnButtonIcon
          className='button-close-select'
          onClick={() => handleChange(null)}
          path='/assets/images/pictos/icon/cross-light.svg'
        />
      ),
      DropdownIndicator: () => null,
      IndicatorSeparator: () => null,
      LoadingIndicator: () => null,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      MenuList: (props: any) => <PlaceMenuList {...props} onClickManual={handleCustomFormDisplay} />,
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      NoOptionsMessage: (props: any) => <PlaceNoOption {...props} />,
      Option: PlaceOption
    }),
    [handleChange, handleCustomFormDisplay]
  );

  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const styles = useMemo(() => ({ menuPortal: (base: any) => ({ ...base, zIndex: 10 }) }), []);

  return (
    <>
      <AsyncSelect
        aria-label={placeholder}
        cacheOptions={false}
        className={classNames('mn-select-places-input', {
          disabled,
          filled: !required || selectValue,
          inputEmpty: !inputValue,
          loading,
          required
        })}
        classNamePrefix='mn-select-places-input'
        closeMenuOnSelect={true}
        components={components}
        escapeClearsValue={false}
        getOptionLabel={(option) => option.label}
        hideSelectedOptions={false}
        inputId={id}
        inputValue={inputValue}
        isClearable={true}
        isDisabled={disabled}
        isSearchable={true}
        loadOptions={handleLoadOptions}
        loadingMessage={() => null}
        maxMenuHeight={190}
        menuIsOpen={isMenuOpen}
        menuPlacement='auto'
        menuPortalTarget={document.body}
        menuPosition={!isIOS() ? 'fixed' : undefined}
        minMenuHeight={148}
        onBlur={handleBlur}
        onChange={handleChange}
        onInputChange={handleInputChange}
        onMenuClose={() => setIsMenuOpen(false)}
        onMenuOpen={() => setIsMenuOpen(true)}
        openMenuOnClick={false}
        openMenuOnFocus={false}
        pageSize={1}
        placeholder={`${placeholder}${required ? '*' : ''}`}
        styles={styles}
        value={selectValue}
      />
      {loading && <MnLoadingBar className='mn-input-places-progress-bar' />}
      {inputValue && !selectValue && error && (
        <div className='mn-input-places-error'>Veuillez sélectionner une adresse dans le menu déroulant.</div>
      )}
    </>
  );
};

export { PlacesSelectInput };
