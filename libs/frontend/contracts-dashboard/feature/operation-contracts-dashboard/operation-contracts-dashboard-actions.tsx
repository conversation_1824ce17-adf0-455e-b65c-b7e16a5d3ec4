import './operation-contracts-dashboard-actions.scss';
import { useState, MouseE<PERSON> } from 'react';
import {
  <PERSON><PERSON><PERSON>,
  MnButtonController,
  MnSvg,
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON>Trigger,
  She<PERSON><PERSON>op<PERSON>
} from '@mynotary/frontend/shared/ui';
import { ContractHistoryAction } from '../contracts-actions/contract-history-action';
import { useResponsive } from '@mynotary/frontend/shared/util';
import { CreateReceiverShipAction } from '../contracts-actions/create-receivership-action';
import { ShareOperationFilesAction } from '@mynotary/frontend/operation-files/api';
import { useSelector } from 'react-redux';
import { selectContract } from '@mynotary/frontend/legals/api';
import { DownloadImportedContract } from '../contracts-actions/download-imported-contract';
import {
  DownloadRegisteredLettersAction,
  selectRegisteredLetterByContractId
} from '@mynotary/frontend/registered-letters/api';
import { ContractDisplayAction } from '../contracts-actions/contract-display-action';
import { RenameContractAction } from '../contracts-actions/rename-contract-action';
import { DuplicateContractAction } from '../contracts-actions/duplicate-contract-action';
import { AccessFormAction } from '../contracts-actions/access-form-action';
import { CancelRegisteredLettersAction } from '@mynotary/frontend/registered-letters/api';
import { CancelSignatureAction, selectAllSignaturesByContractId } from '@mynotary/frontend/signatures/api';
import { getCurrentSignatureByContractStatus } from '@mynotary/frontend/signatures/api';
import { DeleteContractAction } from '../contracts-actions/delete-contract-action';
import { ArchiveContractActions } from '../contracts-actions/archive-contract-actions';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import { CancelContractValidation } from '@mynotary/frontend/contract-validations/api';

interface OperationContractsDashboardActionsProps {
  contractId: number;
  operationId: number;
}

export const OperationContractsDashboardActions = ({
  contractId,
  operationId
}: OperationContractsDashboardActionsProps) => {
  const isResponsive = useResponsive('SMALL_DESKTOP');
  const [openPopover, setOpenPopover] = useState(false);
  const contract = useSelector(selectContract(contractId));
  const registeredLetter = useSelector(selectRegisteredLetterByContractId(contractId));

  const handleClick = (e: MouseEvent) => {
    e.stopPropagation();
    e.preventDefault();
    setOpenPopover(!openPopover);
  };

  const signatures = useSelector(selectAllSignaturesByContractId(contractId));
  const signature = getCurrentSignatureByContractStatus(contract, signatures);

  return (
    <>
      {!isResponsive && (
        <Popover onOpenChange={setOpenPopover} open={openPopover}>
          <PopoverTrigger asChild={true}>
            <MnButtonController
              bolded={true}
              className='operation-contracts-dashboard-actions'
              label='Actions'
              onClick={handleClick}
              underlined={false}
            />
          </PopoverTrigger>
          <PopoverContent>
            <PopoverActionList>
              <DownloadImportedContract
                contract={contract}
                fetchOperationData={false}
                onFinish={() => setOpenPopover(false)}
              />
              {registeredLetter && !registeredLetter.draft && (
                <DownloadRegisteredLettersAction batch={registeredLetter} onFinish={() => setOpenPopover(false)} />
              )}
              <ContractDisplayAction contract={contract} />
              {contract.status !== ContractStatus.DRAFT && contract.status !== ContractStatus.REDACTION && (
                <ShareOperationFilesAction onFinish={() => setOpenPopover(false)} operationId={operationId} />
              )}
              <RenameContractAction contract={contract} onFinish={() => setOpenPopover(false)} />
              <DuplicateContractAction contract={contract} onFinish={() => setOpenPopover(false)} />
              <ContractHistoryAction contractId={contractId} onFinish={() => setOpenPopover(false)} />
              <AccessFormAction contract={contract} />
              {(contract.status === ContractStatus.NOTIFICATION_DRAFT ||
                contract.status === ContractStatus.NOTIFICATION_ERROR ||
                contract.status === ContractStatus.NOTIFICATION_PENDING) && (
                <CancelRegisteredLettersAction
                  batchId={registeredLetter?.id}
                  contractId={contract.id}
                  onFinish={() => setOpenPopover(false)}
                  renderItem={(onClick) => (
                    <ActionIcon
                      icon='/assets/images/pictos/icon/x-circle-light.svg'
                      label='Annuler le recommandé'
                      onClick={onClick}
                    />
                  )}
                />
              )}
              {(contract.status === ContractStatus.SIGNATURE_DRAFT ||
                contract.status === ContractStatus.SIGNATURE_ERROR ||
                contract.status === ContractStatus.SIGNATURE_PENDING ||
                contract.status === ContractStatus.SIGNATURE_EXPIRED) &&
                signature && (
                  <CancelSignatureAction
                    contractId={contractId}
                    onFinish={() => setOpenPopover(false)}
                    signature={signature}
                  />
                )}
              <CancelContractValidation
                contractId={contractId}
                onFinish={() => setOpenPopover(false)}
                operationId={operationId}
                triggerElement={({ onClick }) => (
                  <ActionIcon
                    icon='/assets/images/pictos/icon/x-circle-light.svg'
                    label='Retour à l’édition'
                    onClick={onClick}
                  />
                )}
              />
              <DeleteContractAction
                contract={contract}
                hasSignaturesOrLetters={
                  (registeredLetter != null && !registeredLetter.draft) || (signature != null && !signature.draft)
                }
                onFinish={() => setOpenPopover(false)}
              />
              <CreateReceiverShipAction
                contractId={contractId}
                onFinish={() => setOpenPopover(false)}
                operationId={operationId}
                renderItem={(onClick) => (
                  <ActionIcon
                    icon='/assets/images/pictos/icon/clipboard-filled.svg'
                    label='Accéder au registre séquestre'
                    onClick={onClick}
                  />
                )}
              />
              <ArchiveContractActions contractId={contract.id} onFinish={() => setOpenPopover(false)} />
            </PopoverActionList>
          </PopoverContent>
        </Popover>
      )}
      {isResponsive && (
        <Popover onOpenChange={setOpenPopover} open={openPopover}>
          <PopoverTrigger asChild={true}>
            <MnSvg
              onClick={(e) => handleClick(e)}
              path='/assets/images/pictos/icon/more-vertical-light.svg'
              variant='black'
            />
          </PopoverTrigger>
          <PopoverContent>
            <SheetPopin direction='bottom' isOpened={openPopover} onClose={() => setOpenPopover(false)}>
              <PopoverActionList>
                <DownloadImportedContract
                  contract={contract}
                  fetchOperationData={false}
                  onFinish={() => setOpenPopover(false)}
                />
                {registeredLetter && !registeredLetter.draft && (
                  <DownloadRegisteredLettersAction batch={registeredLetter} onFinish={() => setOpenPopover(false)} />
                )}
                <ContractDisplayAction contract={contract} />
                {contract.status !== ContractStatus.DRAFT && contract.status !== ContractStatus.REDACTION && (
                  <ShareOperationFilesAction onFinish={() => setOpenPopover(false)} operationId={operationId} />
                )}
                <RenameContractAction contract={contract} onFinish={() => setOpenPopover(false)} />
                <DuplicateContractAction contract={contract} onFinish={() => setOpenPopover(false)} />
                <AccessFormAction contract={contract} />
                {(contract.status === ContractStatus.NOTIFICATION_DRAFT ||
                  contract.status === ContractStatus.NOTIFICATION_ERROR ||
                  contract.status === ContractStatus.NOTIFICATION_PENDING) && (
                  <CancelRegisteredLettersAction
                    batchId={registeredLetter?.id}
                    contractId={contract.id}
                    onFinish={() => setOpenPopover(false)}
                    renderItem={(onClick) => (
                      <ActionIcon
                        icon='/assets/images/pictos/icon/x-circle-light.svg'
                        label='Annuler le recommandé'
                        onClick={onClick}
                      />
                    )}
                  />
                )}
                {(contract.status === ContractStatus.SIGNATURE_DRAFT ||
                  contract.status === ContractStatus.SIGNATURE_ERROR ||
                  contract.status === ContractStatus.SIGNATURE_PENDING ||
                  contract.status === ContractStatus.SIGNATURE_EXPIRED) &&
                  signature && (
                    <CancelSignatureAction
                      contractId={contractId}
                      onFinish={() => setOpenPopover(false)}
                      signature={signature}
                    />
                  )}
                <ContractHistoryAction contractId={contractId} onFinish={() => setOpenPopover(false)} />
                <CancelContractValidation
                  contractId={contractId}
                  onFinish={() => setOpenPopover(false)}
                  operationId={operationId}
                  triggerElement={({ onClick }) => (
                    <ActionIcon
                      icon='/assets/images/pictos/icon/x-circle-light.svg'
                      label='Retour à l’édition'
                      onClick={onClick}
                    />
                  )}
                />
                <DeleteContractAction
                  contract={contract}
                  hasSignaturesOrLetters={
                    (registeredLetter != null && !registeredLetter.draft) || (signature != null && !signature.draft)
                  }
                  onFinish={() => setOpenPopover(false)}
                />
                <CreateReceiverShipAction
                  contractId={contractId}
                  onFinish={() => setOpenPopover(false)}
                  operationId={operationId}
                  renderItem={(onClick) => (
                    <ActionIcon
                      icon='/assets/images/pictos/icon/clipboard-filled.svg'
                      label='Accéder au registre séquestre'
                      onClick={onClick}
                    />
                  )}
                />
                <ArchiveContractActions contractId={contract.id} onFinish={() => setOpenPopover(false)} />
              </PopoverActionList>
            </SheetPopin>
          </PopoverContent>
        </Popover>
      )}
    </>
  );
};
