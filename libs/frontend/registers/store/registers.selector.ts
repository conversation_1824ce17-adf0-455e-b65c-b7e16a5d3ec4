import { createSelector } from '@reduxjs/toolkit';
import { selectTransactionRegisterFeature } from './transaction-registers.slice';
import { selectManagementRegisterFeature } from './management-registers.slice';
import { selectReceivershipRegisterFeature } from './receivership-registers.slice';
import { find, mapValues, memoize } from 'lodash';
import { selectCurrentFeatureByType } from '@mynotary/frontend/features/api';
import { selectRegistersFeature } from './registers.slice';
import {
  selectContract,
  selectFormsFeature,
  selectOperation,
  selectOperationRecordsSource
} from '@mynotary/frontend/legals/api';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { AnswerDict } from '@mynotary/crossplatform/records/api';
import { getRegisterQuestionsValues } from '@mynotary/frontend/registers/core';
import { selectCurrentOrganization } from '@mynotary/frontend/organizations/api';
import { RegisterType } from '@mynotary/crossplatform/shared/forms-util';
import { getLegalContractTemplate } from '@mynotary/crossplatform/legal-contract-templates/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';

export const selectRegisterEntry;
export const selectCurrentManagementRegister = createSelector(
  selectManagementRegisterFeature,
  selectCurrentFeatureByType(FeatureType.MANAGEMENT_REGISTER_ACCESS),
  (managementRegisters, feature) => {
    return find(managementRegisters, (managementRegister) => managementRegister.id === feature?.id) ?? null;
  }
);

export const selectCurrentTransactionRegister = createSelector(
  selectTransactionRegisterFeature,
  selectCurrentFeatureByType(FeatureType.TRANSACTION_REGISTER_ACCESS),
  (managementRegisters, feature) => {
    return find(managementRegisters, (managementRegister) => managementRegister.id === feature?.id) ?? null;
  }
);

export const selectTransactionRegister = (featureId?: number) =>
  createSelector(
    selectTransactionRegisterFeature,
    (transactionRegisters) =>
      find(transactionRegisters, (transactionRegisters) => transactionRegisters.id === featureId) ?? null
  );

export const selectManagementRegister = (featureId?: number) =>
  createSelector(
    selectManagementRegisterFeature,
    (managementRegisters) =>
      find(managementRegisters, (managementRegister) => managementRegister.id === featureId) ?? null
  );

export const selectCurrentReceivershipRegister = createSelector(
  selectReceivershipRegisterFeature,
  selectCurrentFeatureByType(FeatureType.RECEIVERSHIP_REGISTER_ACCESS),
  (registers, feature) => {
    return find(registers, (register) => register.id === feature?.id) ?? null;
  }
);
export const selectReceivershipRegister = (featureId?: number) =>
  createSelector(
    selectReceivershipRegisterFeature,
    (receivershipRegisters) => find(receivershipRegisters, (register) => register.id === featureId) ?? null
  );

export const selectReceivership = () =>
  createSelector(selectRegistersFeature, (receivershipRegisters) => receivershipRegisters['RECEIVERSHIP']);

export const selectContractRegisterDefaultAnswer = memoize(
  (type: RegisterType, operationId: number, contractId: number) =>
    createSelector(
      selectOperation(operationId),
      selectContract(contractId),
      selectOperationRecordsSource(operationId, contractId),
      selectFormsFeature,
      selectCurrentUser,
      selectCurrentOrganization,
      (operation, contract, source, formsFeature, connectedUser, organization): AnswerDict | undefined => {
        const answersById = formsFeature.answerById;
        const legalContractTemplate = getLegalContractTemplate(contract?.legalContractTemplateId);

        if (legalContractTemplate?.config.register?.[type]) {
          return {
            ...mapValues(legalContractTemplate.config.register[type], (questions) => {
              return {
                value: getRegisterQuestionsValues(questions, source, answersById)
              };
            }),
            contrat_registre: {
              value: `${operation.id}#${contract?.id}`
            },
            ...(connectedUser
              ? {
                  createur_registre: {
                    value: `${connectedUser.lastname} ${connectedUser.firstname} - ${connectedUser.email}`
                  }
                }
              : {}),
            ...(organization
              ? {
                  organization_registre: {
                    value: `${organization.name}, ${organization.address?.formattedAddress ?? 'Adresse non-renseignée'}`
                  }
                }
              : {})
          };
        }
        return undefined;
      }
    ),
  (type: RegisterType, operationId: number, contractId: number) => `${type}_${operationId}_${contractId}`
);
export const selectRegisterEntry = memoize((entryId?: number, type?: RegisterType) =>
  createSelector(selectRegistersFeature, (registers) => {
    return registers?.[type ?? '']?.[entryId ?? -1];
  })
);
