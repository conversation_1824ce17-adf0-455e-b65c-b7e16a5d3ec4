import {
  RegisterPage,
  NewRegisterEntry,
  RegisterEntry,
  UpdateReceivershipRegisterArgs,
  UpdateManagementRegisterArgs,
  RegisterExportOptions,
  RegistersClient,
  UpdateTransactionRegisterArgs
} from '@mynotary/frontend/registers/core';
import { ApiHelpers } from '@mynotary/frontend/shared/axios-util';
import { FileLegacyDto, convertLegacyDtoToMnFile } from '@mynotary/frontend/files/api';
import { FileInfo } from '@mynotary/crossplatform/files-client/api';
import { addRegisterEntries, addManagementRegister, addReceivershipRegister, addTransactionRegister } from './index';
import { AppAsyncThunk } from '@mynotary/frontend/shared/redux-util';
import { inject } from '@mynotary/frontend/shared/injector-util';
import { RegisterType } from '@mynotary/crossplatform/shared/forms-util';
import { selectActiveTokenInfo } from '@mynotary/frontend/auth/api';
import { isWhitelisted, FeatureWhiteListed } from '@mynotary/crossplatform/feature-flags/api';

export const getRegisterEntries =
  (
    registerType: string,
    filter: string,
    page: number,
    pageSize: number,
    organizationId: number
  ): AppAsyncThunk<RegisterPage> =>
  async (dispatch, getState) => {
    // Get current user's email from the state
    const state = getState();
    const tokenInfo = selectActiveTokenInfo(state);
    const userEmail = tokenInfo?.email;

    const registerClient = inject(RegistersClient);

    const useNewApiMyNotary = isWhitelisted(FeatureWhiteListed.NEW_REGISTER_ENTRIES_ROUTE, userEmail);

    let data: RegisterPage;

    if (useNewApiMyNotary) {
      const response = await registerClient.getRegisterEntries({
        organizationId,
        pageSize,
        registerType: registerType as RegisterType,
        search: filter
      });
      data = { entries: response, total: response.length };
    } else {
      const response = await ApiHelpers.get<RegisterPage>('/register/entries', {
        params: { filter, organizationId, page, pageSize, registerType }
      });
      data = response.data;
    }

    dispatch(addRegisterEntries(data.entries));

    return data;
  };

export const postRegisterEntry =
  (newEntry: NewRegisterEntry): AppAsyncThunk<RegisterEntry> =>
  async (dispatch) => {
    const { data } = await ApiHelpers.post<RegisterEntry>(`/register/entries`, newEntry);

    dispatch(addRegisterEntries([data]));

    return data;
  };
export const getRegisterEntry =
  (id: number): AppAsyncThunk<RegisterEntry> =>
  async (dispatch) => {
    const { data } = await ApiHelpers.get<RegisterEntry>(`/register/entries/${id}`);

    dispatch(addRegisterEntries([data]));

    return data;
  };
export const updateRegisterEntry =
  (entry: Pick<RegisterEntry, 'id' | 'answer'>): AppAsyncThunk<RegisterEntry> =>
  async (dispatch) => {
    const { data } = await ApiHelpers.post<RegisterEntry>(`/register/entries/${entry.id}`, entry);

    dispatch(addRegisterEntries([data]));

    return data;
  };
export const updateRegisterEntryStatus =
  (entry: Pick<RegisterEntry, 'id' | 'status'>): AppAsyncThunk<RegisterEntry> =>
  async (dispatch) => {
    const { data } = await ApiHelpers.put<RegisterEntry>(`/register/entries/${entry.id}`, entry);

    dispatch(addRegisterEntries([data]));

    return data;
  };
export const exportRegister =
  (registerType: RegisterType, options?: RegisterExportOptions): AppAsyncThunk<FileInfo> =>
  async () => {
    const { data } = await ApiHelpers.get<FileLegacyDto>(`/register/entries/export`, {
      params: { registerType, ...options }
    });

    return convertLegacyDtoToMnFile(data);
  };
export const getReceivershipRegisterBalance = (): AppAsyncThunk<number> => async () => {
  const { data } = await ApiHelpers.get<{ balance: number }>(`/register/receivership/balance/`);

  return data.balance;
};

export const getManagementRegister =
  (organizationId: number): AppAsyncThunk<void> =>
  async (dispatch) => {
    try {
      const registersClient = inject(RegistersClient);
      const config = await registersClient.getManagementRegister(organizationId);
      dispatch(addManagementRegister(config));
    } catch (error) {
      console.error(error);
    }
  };

export const getReceivershipRegister =
  (organizationId: number): AppAsyncThunk<void> =>
  async (dispatch) => {
    try {
      const registersClient = inject(RegistersClient);
      const config = await registersClient.getReceivershipRegister(organizationId);
      dispatch(addReceivershipRegister(config));
    } catch (error) {
      console.error(error);
    }
  };

export const updateReceivershipRegister =
  (update: UpdateReceivershipRegisterArgs): AppAsyncThunk<void> =>
  async (dispatch) => {
    const registersClient = inject(RegistersClient);
    await registersClient.updateReceivershipRegister(update);
    dispatch(addReceivershipRegister(update));
  };

export const updateManagementRegister =
  (update: UpdateManagementRegisterArgs): AppAsyncThunk<void> =>
  async (dispatch) => {
    const registersClient = inject(RegistersClient);
    await registersClient.updateManagementRegister(update);
    dispatch(addManagementRegister(update));
  };

export const getTransactionRegister =
  (organizationId: number): AppAsyncThunk<void> =>
  async (dispatch) => {
    try {
      const registersClient = inject(RegistersClient);
      const config = await registersClient.getTransactionRegister(organizationId);
      dispatch(addTransactionRegister(config));
    } catch (error) {
      console.error(error);
    }
  };

export const updateTransactionRegister =
  (update: UpdateTransactionRegisterArgs): AppAsyncThunk<void> =>
  async (dispatch) => {
    const registersClient = inject(RegistersClient);
    await registersClient.updateTransactionRegister(update);
    dispatch(addTransactionRegister(update));
  };

export const getContractRegisterEntry =
  (operationId: number, contractId: number, type: RegisterType): AppAsyncThunk<RegisterEntry> =>
  async () => {
    const { data } = await ApiHelpers.get<RegisterEntry>(
      `/legal-components/${operationId}/operation/contracts/${contractId}/register`,
      {
        params: { type }
      }
    );

    return data;
  };
