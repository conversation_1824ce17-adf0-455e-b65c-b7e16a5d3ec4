import './register-number-picker.scss';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { RegisterEntry, RegistersClient } from '@mynotary/frontend/registers/core';
import {
  PopoverSearchItem,
  PopoverSearchAction,
  PopoverSearchInput,
  PopoverSearchList,
  PopoverSearchEmptyList,
  SelectUi
} from '@mynotary/frontend/registers/ui';
import React, { useEffect, useState } from 'react';
import {
  MnButton,
  MnButtonInline,
  MnLoader,
  MnSvg,
  MnTooltip,
  Popover,
  PopoverAnchor,
  PopoverContent,
  PopoverTrigger
} from '@mynotary/frontend/shared/ui';
import { RegisterNumberSelectProps } from '../register-number-select/register-number-select';
import { RegisterNumberInput } from '../register-number-input/register-number-input';
import { getLegalOperationPersonFromRegisterEntry } from '@mynotary/frontend/registers/core';

const PAGE_SIZE = 10;

export const RegisterNumberPicker = ({
  className,
  filterByNumber,
  onValidate,
  organizationId,
  registerNumber,
  registerType
}: RegisterNumberSelectProps) => {
  const [isPickerOpened, setIsPickerOpened] = useState<boolean>(false);
  const [isManualOpened, setIsManualOpened] = useState<boolean>(false);

  const [status, setStatus] = useState<'pending' | 'loading' | 'success' | 'error-permission'>('pending');
  const [search, setSearch] = useState<string>();
  const [registerEntries, setRegisterEntries] = useState<RegisterEntry[]>([]);

  const registerClient = useService(RegistersClient);

  const [manualRegister, setManualRegister] = useState<number | null | undefined>(registerNumber);

  useEffect(() => {
    const fetchRegisterNumbers = async () => {
      if (organizationId != null && registerType != null) {
        setStatus('loading');
        try {
          const registerEntries = await registerClient.getRegisterEntriesLegacy({
            filterNumber: filterByNumber ? search : undefined,
            organizationId,
            pageSize: PAGE_SIZE,
            registerType,
            search: filterByNumber ? undefined : search
          });
          setStatus('success');
          setRegisterEntries(registerEntries);
        } catch (error) {
          setStatus('error-permission');
          throw error;
        }
      }
    };

    const timeoutId = setTimeout(fetchRegisterNumbers, 500);

    return () => clearTimeout(timeoutId);
  }, [search, organizationId, registerClient, registerType, filterByNumber]);

  const handleSelectRegister = async (id: number) => {
    const register = registerEntries.find((entry) => entry.id === id);

    if (register != null) {
      const legalOperationPersons = await getLegalOperationPersonFromRegisterEntry(register);

      onValidate?.({
        creatorId: register.creator.id,
        email: register.creator.email,
        firstname: register.creator.firstname,
        id: register.id,
        lastName: register.creator.lastname,
        legalOperationPersons,
        operationLabel: register.operationLabel,
        registerNumber: register.answer?.['numero_registre']?.value
      });
      setIsPickerOpened(false);
    }
  };

  const handleChangeSearch = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearch(event.target.value);
  };

  const handleValidateManual = () => {
    if (manualRegister != null) {
      onValidate?.(manualRegister);
    }
    setIsPickerOpened(false);
    setIsManualOpened(false);
  };

  return (
    <Popover onOpenChange={setIsPickerOpened} open={isPickerOpened}>
      <PopoverTrigger asChild>
        <SelectUi placeholder='Saisir un numéro' value={registerNumber?.toString()} />
      </PopoverTrigger>
      <PopoverContent className={className}>
        <div className='register-number-picker-popover'>
          <PopoverSearchInput onChange={handleChangeSearch} placeholder={'Rechercher un N° de mandat'} value={search} />
          {status === 'success' && registerEntries.length === 0 && (
            <PopoverSearchEmptyList>Aucun résultat</PopoverSearchEmptyList>
          )}
          {status === 'success' && registerEntries.length > 0 && (
            <PopoverSearchList>
              {registerEntries.map((registerEntry) => (
                <PopoverSearchItem key={registerEntry.id} onClick={() => handleSelectRegister(registerEntry.id)}>
                  <MnTooltip
                    className='rnsp-tooltip'
                    content={registerEntry.operationLabel}
                    disabled={registerEntry.operationLabel == null}
                  >
                    <div className='rnsp-container'>
                      <div className='rnsp-number'>N°{registerEntry.answer?.['numero_registre']?.value}</div>
                      <div className='rnsp-ellipsis'>{registerEntry.operationLabel ?? '-'}</div>
                    </div>
                  </MnTooltip>
                </PopoverSearchItem>
              ))}
            </PopoverSearchList>
          )}
          {status === 'loading' && (
            <PopoverSearchEmptyList>
              <MnLoader />
            </PopoverSearchEmptyList>
          )}
          {status === 'error-permission' && (
            <PopoverSearchEmptyList>
              <div>
                Vous ne disposez pas des droits d'accès à la consultation des numéros de mandats du registre, vous ne
                pouvez pas utiliser la fonctionnalité. Merci de vous rapprocher d'un administrateur
              </div>
            </PopoverSearchEmptyList>
          )}
          <Popover onOpenChange={setIsManualOpened} open={isManualOpened}>
            <PopoverTrigger asChild>
              <PopoverAnchor>
                <PopoverSearchAction>
                  <MnButtonInline className='rnsp-button' onClick={() => setIsManualOpened(true)}>
                    Saisir manuellement
                    <MnSvg path={'/assets/images/pictos/icon/edit-2-light.svg'} size='small' />
                  </MnButtonInline>
                </PopoverSearchAction>
              </PopoverAnchor>
            </PopoverTrigger>
            <PopoverContent className={className}>
              <div className='manual-register-popover'>
                <RegisterNumberInput
                  onValidate={(value) => setManualRegister(value)}
                  registerNumber={manualRegister ?? undefined}
                />
                <MnButton
                  disabled={manualRegister === registerNumber || manualRegister == null}
                  label='Enregistrer'
                  onClick={handleValidateManual}
                  shape='squircle'
                />
              </div>
            </PopoverContent>
          </Popover>
        </div>
      </PopoverContent>
    </Popover>
  );
};
