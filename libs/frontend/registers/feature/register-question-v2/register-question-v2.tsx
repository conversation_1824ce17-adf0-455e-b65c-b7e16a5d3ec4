// MnRegisterQuestionV2.tsx
import React, { ReactElement } from 'react';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterTransactionQuestion } from './register-transaction-question';
import { RegisterManagementQuestion } from './register-management-question';

// Shared tooltip content function
export const getRegisterTooltipContent = (
  disabled: boolean,
  canAdd: boolean,
  isRegisterInitialized: boolean,
  isAllowedContract: boolean,
  isClosed: boolean
): string => {
  if (disabled) {
    return 'Vous ne pouvez pas prendre de numéro sur un contrat validé';
  } else if (!canAdd) {
    return `Vous n'avez pas le droit de prendre un numéro de mandat`;
  } else if (!isRegisterInitialized) {
    return 'Vous ne pouvez pas prendre de numéro sans avoir initialisé le registre au préalable. Rendez-vous dans votre espace Paramètres > Registre (transaction ou gestion)';
  } else if (canAdd && !isAllowedContract) {
    return 'Vous ne pouvez pas prendre de numéro dans ce contrat';
  } else if (canAdd && !isClosed) {
    return 'Vous ne pouvez pas reprendre un numéro sans clore le précédent';
  }
  return '';
};

interface MnRegisterQuestionV2Props extends MnProps {
  answer: Answer;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const MnRegisterQuestionV2 = (props: MnRegisterQuestionV2Props): ReactElement => {
  const { question } = props;

  if (question.register?.type === 'TRANSACTION') {
    return <RegisterTransactionQuestion {...props} />;
  } else {
    return <RegisterManagementQuestion {...props} />;
  }
};
