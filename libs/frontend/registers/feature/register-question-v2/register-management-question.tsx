import React, { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { hasPermission } from '@mynotary/frontend/roles/api';
import { selectConnectedUserRole } from '@mynotary/frontend/roles/api';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { useFeatureState } from '@mynotary/frontend/features/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';
import { selectCurrentManagementRegister } from '@mynotary/frontend/registers/store';
import { registerPermissionEntityByType } from '@mynotary/frontend/registers/core';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterQuestionCommon } from './register-question-common';

interface RegisterManagementQuestionProps extends MnProps {
  answer: Answer;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterManagementQuestion = (props: RegisterManagementQuestionProps): ReactElement => {
  const role = useSelector(selectConnectedUserRole);
  const managementRegister = useSelector(selectCurrentManagementRegister);
  const { isActive: hasManagementFeature } = useFeatureState(FeatureType.MANAGEMENT_REGISTER_ACCESS);

  const isRegisterInitialized = managementRegister?.config != null;

  const canAdd = hasPermission(
    PermissionType.CREATE_ORGANIZATION_REGISTER_ENTRY,
    role,
    registerPermissionEntityByType['MANAGEMENT']
  );

  const canRead = hasPermission(
    PermissionType.READ_ORGANIZATION_REGISTER,
    role,
    registerPermissionEntityByType['MANAGEMENT']
  );

  const canAddManually =
    !isRegisterInitialized ||
    hasPermission(
      PermissionType.CREATE_ORGANIZATION_REGISTER_MANUALLY_ENTRY,
      role,
      registerPermissionEntityByType['MANAGEMENT']
    );

  const getTooltipContent = (
    disabled: boolean,
    canAdd: boolean,
    isRegisterInitialized: boolean,
    isAllowedContract: boolean,
    isClosed: boolean
  ): string => {
    if (disabled) {
      return 'Vous ne pouvez pas prendre de numéro sur un contrat validé';
    } else if (!canAdd) {
      return `Vous n'avez pas le droit de prendre un numéro de mandat`;
    } else if (!isRegisterInitialized) {
      return 'Vous ne pouvez pas prendre de numéro sans avoir initialisé le registre au préalable. Rendez-vous dans votre espace Paramètres > Registre (transaction ou gestion)';
    } else if (canAdd && !isAllowedContract) {
      return 'Vous ne pouvez pas prendre de numéro dans ce contrat';
    } else if (canAdd && !isClosed) {
      return 'Vous ne pouvez pas reprendre un numéro sans clore le précédent';
    }
    return '';
  };

  return (
    <RegisterQuestionCommon
      {...props}
      canAdd={canAdd}
      canAddManually={canAddManually}
      canRead={canRead}
      getTooltipContent={getTooltipContent}
      hasFeature={hasManagementFeature}
      isRegisterInitialized={isRegisterInitialized}
    />
  );
};
