import React, { ReactElement } from 'react';
import { MnProps } from '@mynotary/frontend/shared/util';
import { Answer, RegisterFormQuestion } from '@mynotary/crossplatform/shared/forms-util';
import { RegisterQuestionCommon } from './register-question-common';
import { useRegisterQuestionLogic } from './register-question-v2';

interface RegisterManagementQuestionProps extends MnProps {
  answer: Answer;
  debounce: boolean;
  disabled: boolean;
  onChange?: (value: string | null) => void;
  question: RegisterFormQuestion;
}

export const RegisterManagementQuestion = (props: RegisterManagementQuestionProps): ReactElement => {
  const {
    canAdd,
    canRead,
    canAddManually,
    hasFeature,
    isRegisterInitialized,
    getTooltipContent
  } = useRegisterQuestionLogic('MANAGEMENT');

  return (
    <RegisterQuestionCommon
      {...props}
      canAdd={canAdd}
      canAddManually={canAddManually}
      canRead={canRead}
      getTooltipContent={getTooltipContent}
      hasFeature={hasFeature}
      isRegisterInitialized={isRegisterInitialized}
    />
  );
};
