@use 'style/mixins' as *;
@use 'style/variables/colors' as *;

.mn-register-question {
  display: flex;
  flex-direction: column;
  align-items: flex-end;

  width: 100%;
  max-width: 388px;

  .rd-add {
    cursor: pointer;

    display: flex;
    align-items: center;
    justify-content: center;

    margin-top: 6px;

    &:hover {
      .disabled {
        color: $gray500;

        * {
          stroke: $gray500 !important;
        }
      }
    }
  }

  .rq-add-register-entry-tooltip {
    width: 16px;
    height: 16px;
    margin-right: 3px;
  }

  .rq-add-register-entry {
    @include mn-link;

    cursor: pointer;
    display: inline-block;
    margin-left: auto;

    &.disabled {
      cursor: default;
      color: $gray500;
      opacity: .7;
    }

    &.disabled::after {
      content: none;
    }
  }
}

.register-question-popin {
  .popin {
    @include responsive($tablet-max) {
      width: 80%;
    }

    @include responsive($mobile-max) {
      width: 95%;
    }
  }
}
