import { setSnackbarMessage } from '@mynotary/frontend/snackbars/api';

import { inject } from '@mynotary/frontend/shared/injector-util';
import {
  ApiComponentsResult,
  Direction,
  isOperation,
  Operation,
  OperationNew,
  OperationsClient
} from '@mynotary/frontend/legals/core';
import { AppAsyncThunk } from '@mynotary/frontend/shared/redux-util';
import { convertLabelPatternToText } from '@mynotary/frontend/text-editor/api';
import { FiltersOperationApi } from '@mynotary/frontend/dashboard-filters/api';
import { Pageable } from '@mynotary/frontend/shared/util';
import { setLoader } from '@mynotary/frontend/loader/api';
import { ApiHelpers } from '@mynotary/frontend/shared/axios-util';
import { switchMemberByOrganizationId } from '@mynotary/frontend/user-session/api';
import { TElement } from '@mynotary/frontend/text-editor/api';
import { selectTagsToCreate } from './select-tags-to-create';
import { selectOperation } from './selectOperation';
import {
  addOperationStatus,
  removeLegalComponent,
  setApiComponentPayload,
  updateOperationArchiveStatus,
  updateOperationLabel,
  updateOperationTags
} from '../legals.slice';
import { setIsLoadingOperation, setOperation } from '@mynotary/frontend/current-operation/api';

export const updateTags =
  (operationId: number): AppAsyncThunk<void> =>
  async (dispatch, getState) => {
    const tags = selectTagsToCreate(operationId)(getState());
    const operation = selectOperation(operationId)(getState());
    const pattern = operation.labelPattern;
    const answer = convertLabelPatternToText(pattern, tags);
    const label = answer?.label ? answer?.label : operation.label;

    const operationsClient = inject(OperationsClient);
    await operationsClient.updateOperation({ id: operationId.toString(), label: label ?? '', tags });
    dispatch(updateOperationTags({ label, operationId, tags }));
  };
// Operation specific calls
export const putOperationStatus =
  (operation: Operation, statusId?: number, statusLabel?: string): AppAsyncThunk<void> =>
  async (dispatch) => {
    await ApiHelpers.put(`/operations/${operation.id}/status`, statusId);
    const operationStatus = statusId && statusLabel ? { id: statusId, label: statusLabel } : undefined;
    dispatch(addOperationStatus({ operationId: operation.id, operationStatus }));
  };
export const putOperationLabel =
  (operation: Operation, label?: string, labelPattern?: TElement[]): AppAsyncThunk<void> =>
  async (dispatch) => {
    await ApiHelpers.put(`/operations/${operation.id}/label`, {
      label,
      labelPattern: JSON.stringify(labelPattern ?? '[]')
    });
    dispatch(updateOperationLabel({ label, labelPattern, operationId: operation.id }));
  };
export const postOperation =
  (operation: OperationNew, isOperationReference?: boolean): AppAsyncThunk<Operation> =>
  async (dispatch) => {
    const { data } = await ApiHelpers.post<Operation>(
      `/operations?isOperationReference=${isOperationReference ?? false}`,
      {
        creatorUser: {
          id: operation.creatorUserId
        },
        label: operation.label,
        labelPattern: operation.labelPattern,
        organizationId: operation.organizationId,
        template: { id: operation.templateId },
        type: 'OPERATION'
      }
    );

    dispatch(setApiComponentPayload({ components: [data] }));
    return data;
  };
export const getOperations =
  (
    filtering: FiltersOperationApi,
    pageable?: Pageable,
    ordering?: Direction,
    organizationId?: number
  ): AppAsyncThunk<ApiComponentsResult> =>
  async (dispatch) => {
    const type = 'OPERATIONS';
    dispatch(setLoader({ isLoading: true, type }));
    try {
      const { data } = await ApiHelpers.get<ApiComponentsResult>(`/operations`, {
        params: {
          filtering: encodeURIComponent(JSON.stringify(filtering)),
          ordering: ordering,
          organizationId,
          page: pageable?.page,
          pageSize: pageable?.pageSize
        }
      });

      dispatch(setApiComponentPayload(data));
      return data;
    } finally {
      dispatch(setLoader({ isLoading: false, type }));
    }
  };
export const getOperation =
  (id: number, isCurrent?: boolean): AppAsyncThunk<void> =>
  async (dispatch) => {
    dispatch(setIsLoadingOperation(true));
    const operationsClient = inject(OperationsClient);

    try {
      const data = await operationsClient.getOperation(id);

      const targetOp = data.components.find((c) => c.id === id);
      if (isOperation(targetOp)) {
        dispatch(switchMemberByOrganizationId(targetOp.organizationId));
        if (isCurrent) {
          dispatch(setOperation(targetOp));
        }
      }

      dispatch(setApiComponentPayload(data));
    } finally {
      dispatch(setIsLoadingOperation(false));
    }
  };
export const updateOperationArchive =
  (
    id: number,
    isArchived: boolean,
    isArchiveFilterActive: boolean,
    isActiveFilterActive: boolean
  ): AppAsyncThunk<void> =>
  async (dispatch) => {
    await ApiHelpers.put<void>(`/operations/${id}`, { isArchived });

    dispatch(updateOperationArchiveStatus({ isArchived, operationId: id }));

    const shouldRemoveArchivedOperation = !isArchiveFilterActive && isArchived;
    const shouldRemoveActiveOperation = !isActiveFilterActive && !isArchived;

    if (shouldRemoveArchivedOperation || shouldRemoveActiveOperation) {
      dispatch(removeLegalComponent(id));
    }

    if (isArchived) {
      dispatch(setSnackbarMessage({ templateId: 'OPERATION_ARCHIVED' }));
    }
  };
export const getLinkedOperations =
  (
    operationId: number,
    filtering: FiltersOperationApi,
    pageable?: Pageable,
    ordering?: Direction
  ): AppAsyncThunk<ApiComponentsResult> =>
  async (dispatch) => {
    const { data } = await ApiHelpers.get<ApiComponentsResult>(`/operations/${operationId}/linked-operations`, {
      params: {
        filtering: encodeURIComponent(JSON.stringify(filtering)),
        ordering,
        page: pageable?.page,
        pageSize: pageable?.pageSize
      }
    });

    dispatch(setApiComponentPayload(data));
    return data;
  };
export const deleteOperation =
  (id: number): AppAsyncThunk<void> =>
  async (dispatch) => {
    await ApiHelpers.delete<void>(`/operations/${id}`, {});

    dispatch(removeLegalComponent(id));
  };
