import { createSelector } from '@reduxjs/toolkit';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { memoize } from 'lodash';
import { selectContractPermission } from '../index';
import { selectContract, selectIsSignatureReady, selectHasMissingRecords } from '../contracts';
import { selectValidateContractTask } from '../tasks';
import { selectRedactionFeature } from './redaction.slice';

import { isRedactionPending } from '@mynotary/crossplatform/legals/core';

export const selectHasValidationAction = (contractId: number) =>
  createSelector(
    selectContractPermission(PermissionType.VALIDATE_CONTRACT, contractId),
    selectContractPermission(PermissionType.FORCE_VALIDATE_CONTRACT, contractId),
    selectContractPermission(PermissionType.READ_CONTRACT, contractId),
    selectContract(contractId),
    (canValidate, canForceValidate, canReadContract, contract) => {
      const isRightStatus = isRedactionPending(contract.status);

      return isRightStatus && (canValidate || canForceValidate) && canReadContract;
    }
  );

export const selectHasCreateValidationRequestAction = (contractId: number) =>
  createSelector(
    selectContractPermission(PermissionType.VALIDATE_CONTRACT, contractId),
    selectContractPermission(PermissionType.FORCE_VALIDATE_CONTRACT, contractId),
    selectContractPermission(PermissionType.CREATE_CONTRACT_TASK_ASK_VALIDATION, contractId),
    selectContractPermission(PermissionType.FORCE_CREATE_CONTRACT_TASK_ASK_VALIDATION, contractId),
    selectContractPermission(PermissionType.READ_CONTRACT, contractId),
    selectContract(contractId),
    selectValidateContractTask(contractId),
    (canValidate, canForceValidate, canAsk, canForceAsk, canReadContract, contract, validateTask) => {
      const isRightStatus = isRedactionPending(contract.status);

      return (
        isRightStatus &&
        validateTask == null &&
        !(canValidate && canReadContract) &&
        !(canForceValidate && canReadContract) &&
        (canAsk || canForceAsk)
      );
    }
  );

export const selectHasCancelAskValidationAction = (contractId: number) =>
  createSelector(
    selectContractPermission(PermissionType.VALIDATE_CONTRACT, contractId),
    selectContractPermission(PermissionType.FORCE_VALIDATE_CONTRACT, contractId),
    selectContractPermission(PermissionType.CREATE_CONTRACT_TASK_ASK_VALIDATION, contractId),
    selectContractPermission(PermissionType.FORCE_CREATE_CONTRACT_TASK_ASK_VALIDATION, contractId),
    selectContract(contractId),
    selectValidateContractTask(contractId),
    (canValidate, canForceValidate, canAsk, canForceAsk, contract, validateTask) => {
      const isRightStatus = isRedactionPending(contract.status);
      return isRightStatus && validateTask != null && !canValidate && !canForceValidate && (canAsk || canForceAsk);
    }
  );
export const selectValidationIsReady = memoize(
  (operationId: number, contractId: number) =>
    createSelector(
      selectIsSignatureReady(operationId, contractId),
      selectContractPermission(PermissionType.VALIDATE_CONTRACT, contractId),
      selectContractPermission(PermissionType.FORCE_VALIDATE_CONTRACT, contractId),
      selectHasMissingRecords(operationId, contractId),
      (isSignatureReady, canValidate, canForceValidation, hasMissingRecords): boolean => {
        const readyToForceValidate = !isSignatureReady && canForceValidation;
        const readyToValidate = isSignatureReady && (canValidate || canForceValidation);
        return !hasMissingRecords && (readyToForceValidate || readyToValidate);
      }
    ),
  (operationId: number, contractId: number) => `${operationId}_${contractId}`
);

export const selectIsValidating = createSelector(selectRedactionFeature, (redaction) => redaction.isValidating);
