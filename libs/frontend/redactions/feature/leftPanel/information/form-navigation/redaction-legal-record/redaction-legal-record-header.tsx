import './redaction-legal-record-header.scss';
import {
  LegalLinkTemplate,
  LegalOperationTemplate,
  LegalRecordTemplate
} from '@mynotary/crossplatform/legal-templates/api';
import {
  getBranchTemplate,
  getRecordLabel,
  resetFormNavigation,
  selectLegalComponentTemplate,
  selectRecordAnswer,
  selectTemplatesByType
} from '@mynotary/frontend/legals/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { FadeComponent, SheetHeader } from '@mynotary/frontend/shared/ui';
import { useSelector } from 'react-redux';

interface RedactionLegalRecordHeaderProps {
  linkTemplateId: string;
  operationId: number;
  recordId: number;
}

export const RedactionLegalRecordHeader = ({
  linkTemplateId,
  operationId,
  recordId
}: RedactionLegalRecordHeaderProps) => {
  const dispatch = useAsyncDispatch();
  const operationTemplate = useSelector(selectLegalComponentTemplate(operationId)) as LegalOperationTemplate;
  const templates = useSelector(selectTemplatesByType('LINK')) as LegalLinkTemplate[];
  const linkTemplate = templates.find((template) => template.id === linkTemplateId);
  const targetBranch = getBranchTemplate(operationTemplate, linkTemplate);
  const labels = linkTemplate?.config.display.branches[targetBranch?.type ?? ''];
  const answer = useSelector(selectRecordAnswer(recordId));
  const recordTemplate = useSelector(selectLegalComponentTemplate<LegalRecordTemplate>(recordId));

  const label = labels?.label ?? linkTemplate?.config.display.label;

  const handleClose = () => {
    dispatch(resetFormNavigation());
  };

  if (recordTemplate == null) {
    return null;
  }

  return (
    <SheetHeader onClose={() => handleClose()} testId='redaction-legal-record-header'>
      <FadeComponent animationType={'fade'} id={recordId}>
        <h5 className='redaction-legal-record-header'>
          {label} :{' '}
          {getRecordLabel({
            answer,
            recordTemplate
          })}
        </h5>
      </FadeComponent>
    </SheetHeader>
  );
};
