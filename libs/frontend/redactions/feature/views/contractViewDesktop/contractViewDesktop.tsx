import { ReactElement } from 'react';
import { RedactionLeftPanel } from '../../leftPanel/redactionLeftPanel';
import { ContractPanel } from '../../panel/contractPanel';
import { FooterLayout, MnButton, MnTooltip } from '@mynotary/frontend/shared/ui';
import { RedactionValidation } from '../../actions/redactionValidation/redactionValidation';
import { ContractViewProps } from '../contractView';
import { ContractViewLayout } from '../contractViewLayout/contractViewLayout';
import { ValidationFollowUpDisplay } from '../../validation/validationFollowUpDisplay';
import { RedactionSummary } from '../../redactionSummary/redactionSummary';
import { CreateReceiverShipAction } from '@mynotary/frontend/contracts-dashboard/api';
import { useResponsive } from '@mynotary/frontend/shared/util';
import { ContractProjectShare } from '@mynotary/frontend/operation-files/api';

export const ContractViewDesktop = ({ contractId, operationId }: ContractViewProps): ReactElement | null => {
  const isResponsive = useResponsive('SMALL_DESKTOP');

  return (
    <>
      <ContractViewLayout
        contract={<ContractPanel contractId={contractId} operationId={operationId} />}
        form={<RedactionLeftPanel />}
        summary={<RedactionSummary />}
        validationFollowUp={<ValidationFollowUpDisplay contractId={contractId} operationId={operationId} />}
      />
      <FooterLayout.Container hasSidebar={true}>
        <FooterLayout.LeftSide>
          {!isResponsive && (
            <CreateReceiverShipAction
              contractId={contractId}
              operationId={operationId}
              renderItem={(onClick) => (
                <MnTooltip content='Compte séquestre'>
                  <MnButton
                    icon='/assets/images/pictos/icon/clipboard-filled.svg'
                    label='Accéder au registre séquestre'
                    labelPosition='right'
                    onClick={onClick}
                    variant='secondary'
                  />
                </MnTooltip>
              )}
            />
          )}
        </FooterLayout.LeftSide>
        <FooterLayout.RightSide>
          <ContractProjectShare
            contractId={contractId}
            operationId={operationId}
            triggerElement={(props) => (
              <MnButton
                icon='/assets/images/pictos/icon/send.svg'
                label='Partager le projet de contrat'
                shape='squircle'
                size='small'
                testId='contract-project-share'
                variant={'secondary'}
                {...props}
              />
            )}
          />
          <RedactionValidation contractId={contractId} operationId={operationId} />
        </FooterLayout.RightSide>
      </FooterLayout.Container>
    </>
  );
};
