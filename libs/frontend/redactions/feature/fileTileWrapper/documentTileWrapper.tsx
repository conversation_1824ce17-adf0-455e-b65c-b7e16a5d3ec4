import './documentTileWrapper.scss';
import { Dictionary } from '@mynotary/crossplatform/shared/util';
import { MnDropMask, MnFileUpload } from '@mynotary/frontend/files/api';
import { MouseEvent, ReactNode, useState } from 'react';
import { MnSvg } from '@mynotary/frontend/shared/ui';
import { isEmpty, set, sumBy, toArray } from 'lodash';
import { setSuccessMessage } from '@mynotary/frontend/snackbars/api';
import { Record, updateRecordAnswer, useUploadMandatoryDocument } from '@mynotary/frontend/legals/api';
import { MnImportIcon } from '../importIcon';
import { FileInfo } from '@mynotary/crossplatform/files-client/api';
import { MultipleDocumentImportPopin } from '@mynotary/frontend/drives/api';
import { generateDocumentAnswerUpdate } from '@mynotary/frontend/shared/forms-util';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { extractUploadAnswerFileIds, UploadFormAnswer } from '@mynotary/crossplatform/shared/forms-util';
import { classNames } from '@mynotary/frontend/shared/util';

interface MnDocumentTileWrapperProps {
  actions?: ReactNode;
  children: ReactNode;
  documentCount?: number;
  documentId?: string;
  editable: boolean;
  isLocked: boolean;
  onTileClick?: () => void;
  record?: Record;
  uploadFormAnswer?: UploadFormAnswer;
}

const MnDocumentTileWrapper = ({
  actions,
  children,
  documentCount,
  documentId,
  editable,
  isLocked,
  onTileClick,
  record,
  uploadFormAnswer
}: MnDocumentTileWrapperProps) => {
  const dispatch = useAsyncDispatch();

  const { uploadMandatoryDocument } = useUploadMandatoryDocument();
  const [isImportPopinOpen, setisImportPopinOpen] = useState(false);

  const handleUploadCompleted = async (files: FileInfo[]): Promise<void> => {
    const newAnswer: Dictionary<number> = generateDocumentAnswerUpdate(files, uploadFormAnswer ?? { value: {} });

    if (!isEmpty(newAnswer) && record && documentId) {
      await dispatch(updateRecordAnswer(record.id, set({}, documentId, { value: newAnswer })));
      dispatch(setSuccessMessage('Importation réussie !'));
    }

    if (record?.id && documentId) {
      await uploadMandatoryDocument({
        documentId,
        recordId: record.id
      });
    }
  };

  const handleTileClick = (e: MouseEvent): void => {
    if (isLocked) {
      return;
    }
    if (editable) {
      e.stopPropagation();
      if (documentCount) {
        onTileClick?.();
      } else {
        setisImportPopinOpen(true);
      }
    } else {
      onTileClick?.();
    }
  };

  return (
    <>
      <MnFileUpload multiple={true} onUploadCompleted={handleUploadCompleted} shouldConvertImg={true}>
        {(__openFileDialog, uploadState, drop) => (
          <div className='mn-document-tile-wrapper' {...(editable ? drop.parentProps : {})} data-testid='document-tile'>
            {editable && !isImportPopinOpen && (
              <>
                <MnDropMask
                  maskProps={drop.maskProps}
                  progression={
                    uploadState.uploading
                      ? sumBy(toArray(uploadState.pendingUploads), (upload) => upload.progress) || 0
                      : undefined
                  }
                  state={drop.state}
                />
                <MnSvg className='mn-dtw-icon' path='/assets/images/pictos/icon/upload-cloud.svg' variant='primary' />
              </>
            )}
            <div className={classNames('mn-dtw-tile', { locked: isLocked })} onClick={handleTileClick}>
              {children}
              {documentCount != null && documentId && (
                <MnImportIcon
                  disabled={isLocked}
                  editable={editable && !isLocked}
                  hasDocuments={documentCount > 0}
                  onClick={() => setisImportPopinOpen(true)}
                />
              )}
              {actions}
            </div>
          </div>
        )}
      </MnFileUpload>
      {isImportPopinOpen && documentId && record && (
        <MultipleDocumentImportPopin
          fileIds={extractUploadAnswerFileIds(uploadFormAnswer).map((f) => f.fileId)}
          onCloseImport={() => setisImportPopinOpen(false)}
          open={isImportPopinOpen}
          selectedDocuments={[{ documentId, recordId: record.id, uploadFormAnswer: uploadFormAnswer ?? { value: {} } }]}
        />
      )}
    </>
  );
};

export { MnDocumentTileWrapper };
