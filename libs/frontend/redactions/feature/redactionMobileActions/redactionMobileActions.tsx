import {
  ActionIcon,
  Floating<PERSON>utton,
  Popover,
  <PERSON>over<PERSON><PERSON><PERSON>ist,
  <PERSON>over<PERSON>ontent,
  <PERSON>overTrigger,
  SheetPopin
} from '@mynotary/frontend/shared/ui';
import { ReactElement, useState } from 'react';

import { ContractProjectShare } from '@mynotary/frontend/operation-files/api';
import {
  CreateValidationRequest,
  DeleteValidationRequest,
  CancelContractValidation,
  ValidateContractButton
} from '@mynotary/frontend/contract-validations/api';
import { CreateReceiverShipAction } from '@mynotary/frontend/contracts-dashboard/api';
import { RedactionStartSignature } from '../actions/startSignatureButton/redactionStartSignatureButton';
import { CreateRegisteredLetter } from '../actions/create-registered-letter/create-registered-letter';

interface RedactionMobileActionProps {
  contractId: number;
  operationId: number;
}

export const RedactionMobileAction = ({ contractId, operationId }: RedactionMobileActionProps): ReactElement => {
  const [isOpen, setIsOpen] = useState(false);

  const commonRedactionProps = {
    contractId,
    onFinish: () => setIsOpen(false),
    operationId
  };

  return (
    <Popover onOpenChange={setIsOpen} open={isOpen}>
      <PopoverTrigger asChild={true}>
        <FloatingButton icon='/assets/images/pictos/icon/plus-light.svg' onClick={() => setIsOpen(true)} />
      </PopoverTrigger>
      <PopoverContent>
        <SheetPopin direction='bottom' isOpened={isOpen}>
          <PopoverActionList>
            <DeleteValidationRequest
              triggerElement={(props) => (
                <ActionIcon
                  icon='/assets/images/pictos/icon/error-light.svg'
                  label='Annuler la demande de validation'
                  {...props}
                />
              )}
              {...commonRedactionProps}
            />
            <RedactionStartSignature
              triggerElement={(props) => (
                <ActionIcon
                  icon='/assets/images/pictos/icon/contract-signature-light.svg'
                  label='Lancer la signature'
                  {...props}
                />
              )}
              {...commonRedactionProps}
              onLaunchingSignature={(status) => status === 'success' && setIsOpen(false)}
            />
            <CreateValidationRequest
              TriggerElement={(props) => (
                <ActionIcon
                  icon='/assets/images/pictos/icon/check-circle.svg'
                  label='Demander la validation'
                  {...props}
                />
              )}
              {...commonRedactionProps}
            />
            <ValidateContractButton
              triggerElement={(props) => (
                <ActionIcon
                  icon='/assets/images/pictos/icon/check-circle.svg'
                  label='Valider le projet & signer'
                  {...props}
                />
              )}
              {...commonRedactionProps}
            />
            <ContractProjectShare
              triggerElement={(props) => (
                <ActionIcon
                  icon='/assets/images/pictos/icon/send.svg'
                  label='Partager le projet de contrat'
                  {...props}
                />
              )}
              {...commonRedactionProps}
            />
            <CreateReceiverShipAction
              {...commonRedactionProps}
              renderItem={(onClick) => (
                <ActionIcon
                  icon='/assets/images/pictos/icon/clipboard-filled.svg'
                  label='Accéder au registre séquestre'
                  onClick={onClick}
                />
              )}
            />
            <CancelContractValidation
              {...commonRedactionProps}
              triggerElement={(props) => (
                <ActionIcon icon='/assets/images/pictos/icon/error-light.svg' label='Retour à l’édition' {...props} />
              )}
            />
            <CreateRegisteredLetter
              {...commonRedactionProps}
              triggerElement={(props) => (
                <ActionIcon icon='/assets/images/pictos/icon/send.svg' label='Envoyer en recommandé élec.' {...props} />
              )}
            />
          </PopoverActionList>
        </SheetPopin>
      </PopoverContent>
    </Popover>
  );
};
