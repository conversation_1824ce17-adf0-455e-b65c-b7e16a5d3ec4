import React, { ReactElement, useState } from 'react';
import { MnProps } from '@mynotary/frontend/shared/util';
import { useLaunchSignature } from '@mynotary/frontend/signature-creations/api';
import { useSelector } from 'react-redux';
import { selectContract } from '@mynotary/frontend/legals/api';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';

export type SignatureLaunchingStatus = 'idle' | 'loading' | 'success' | 'error';

interface TriggerElementProps {
  disabled?: boolean;
  onClick: () => void;
}
interface RedactionStartSignatureProps extends MnProps {
  contractId: number;
  onLaunchingSignature?: (status: SignatureLaunchingStatus) => void;
  operationId: number;
  triggerElement: React.FC<TriggerElementProps>;
}

export const RedactionStartSignature = ({
  contractId,
  onLaunchingSignature,
  operationId,
  triggerElement: TriggerElement
}: RedactionStartSignatureProps): ReactElement | null => {
  const [isLoading, setIsLoading] = useState(false);
  const { canLaunchSignature, openSignatureWorkflow } = useLaunchSignature({ contractId, operationId });

  const contract = useSelector(selectContract(contractId));

  const handleClick = async () => {
    setIsLoading(true);
    onLaunchingSignature?.('loading');
    await openSignatureWorkflow();
    onLaunchingSignature?.('success');
    setIsLoading(false);
  };

  if (!canLaunchSignature || contract.status !== ContractStatus.VALIDATED) {
    return null;
  }

  return <TriggerElement disabled={isLoading} onClick={handleClick} />;
};
