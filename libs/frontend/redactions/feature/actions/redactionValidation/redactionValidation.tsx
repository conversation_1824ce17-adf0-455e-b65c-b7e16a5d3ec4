import './redactionValidation.scss';
import { ReactElement, useState } from 'react';
import { useSelector } from 'react-redux';
import {
  RedactionStartSignature,
  SignatureLaunchingStatus
} from '../startSignatureButton/redactionStartSignatureButton';

import { selectContract } from '@mynotary/frontend/legals/api';
import { MnButton, MnButtonController } from '@mynotary/frontend/shared/ui';
import { CreateRegisteredLetter } from '../create-registered-letter/create-registered-letter';
import { ContractStatus } from '@mynotary/crossplatform/legals/api';
import {
  CreateValidationRequest,
  DeleteValidationRequest,
  CancelContractValidation,
  ValidateContractButton
} from '@mynotary/frontend/contract-validations/api';

type RedactionValidationProps = {
  contractId: number;
  operationId: number;
};

export const RedactionValidation = ({ contractId, operationId }: RedactionValidationProps): ReactElement => {
  const [launchingStatus, setLaunchingStatus] = useState<SignatureLaunchingStatus>('idle');
  const contract = useSelector(selectContract(contractId));

  const handleSignatureLaunch = (status: SignatureLaunchingStatus) => {
    setLaunchingStatus(status);
  };

  return (
    <div className='redaction-left-panel-signature'>
      <div className='rlps-primary-action'>
        <ValidateContractButton
          contractId={contractId}
          operationId={operationId}
          triggerElement={(props) => (
            <MnButton
              className='validate-contract-button'
              label='Valider le projet'
              shape='squircle'
              size='small'
              variant={'primary'}
              {...props}
            />
          )}
        />
        <CreateValidationRequest
          TriggerElement={(props) => (
            <MnButton
              label='Demander la validation'
              onClick={props.onClick}
              shape='squircle'
              size='small'
              variant={'primary'}
            />
          )}
          contractId={contractId}
          operationId={operationId}
        />
        <DeleteValidationRequest
          contractId={contractId}
          operationId={operationId}
          triggerElement={(props) => <MnButtonController label='Annuler la demande de validation' {...props} />}
        />
        {contract.status === ContractStatus.VALIDATED && (
          <>
            <CreateRegisteredLetter
              contractId={contractId}
              triggerElement={(props) => (
                <MnButton
                  className='rlps-button'
                  icon='/assets/images/pictos/icon/send.svg'
                  label='Envoyer en recommandé élec.'
                  labelPosition='left'
                  variant='secondary'
                  {...props}
                />
              )}
            />
            <CancelContractValidation
              contractId={contractId}
              operationId={operationId}
              triggerElement={(props) => (
                <MnButton
                  className='rlps-button'
                  disabled={launchingStatus === 'loading'}
                  label={`Retour à l’édition`}
                  variant='secondary'
                  {...props}
                />
              )}
            />
            <RedactionStartSignature
              contractId={contractId}
              onLaunchingSignature={handleSignatureLaunch}
              operationId={operationId}
              triggerElement={(props) => (
                <MnButton label='Lancer la signature' shape='squircle' size='small' variant='primary' {...props} />
              )}
            />
          </>
        )}
      </div>
    </div>
  );
};
