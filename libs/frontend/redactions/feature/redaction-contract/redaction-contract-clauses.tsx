import { ReactElement, useEffect } from 'react';
import { <PERSON>rrorBoundary, MnPortal, PopinHelpers } from '@mynotary/frontend/shared/ui';
import { MnProps } from '@mynotary/frontend/shared/util';
import {
  BalloonToolbar,
  FixedToolbar,
  Plate,
  PlateContent,
  PlateEditor,
  plateFixedToolbarDomId
} from '@mynotary/frontend/text-editor/api';
import { IS_CONTRACT_INTEGRATION_MODE } from '@mynotary/frontend/shared/environments-util';
import {
  OrganizationClauseConfirmationPopinDelete,
  OrganizationClauseConfirmationPopinSave
} from './editor/plugins/clause/organization-clause-confirmation-popin';
import { RedactionEditorFallback } from './redaction-editor-fallback';
import { CONTRACT_LOADER_ID } from './redaction-contract';
import { getRedactionEditorContext } from './editor/plugins/redaction-context-plugin';
import { EditingPermission } from '@mynotary/frontend/redactions/core';
import { scrollOnContractChange } from './scroll-on-contract-change';
import { contractClausesRootElementId } from '@mynotary/frontend/legals/api';

interface RedactionContractClausesProps extends MnProps {
  editor: PlateEditor;
  onRender?: () => void;
}

let observer: MutationObserver | null = null;

const editableProps = {
  scrollSelectionIntoView: () => undefined,
  spellCheck: false,
  style: { fontSize: '14px' }
};

export const RedactionContractClauses = ({ editor, onRender }: RedactionContractClausesProps): ReactElement | null => {
  const { editingPermission } = getRedactionEditorContext(editor);

  useEffect(() => {
    const el = document.getElementById(CONTRACT_LOADER_ID);

    if (el) {
      el.style.display = 'none';
      onRender?.();
    }
  }, [onRender]);

  useEffect(() => {
    /**
     * In integration mode, we don't want to observe the contract clauses changes to avoid scrolling while
     * integrating the contract.
     */
    if (IS_CONTRACT_INTEGRATION_MODE) {
      return;
    }

    if (observer != null) {
      observer.disconnect();
    }

    const element = document.getElementById(contractClausesRootElementId);
    if (element) {
      observer = new MutationObserver((mutations) => scrollOnContractChange(mutations, element));
      setTimeout(() => {
        if (observer != null) {
          observer.observe(element, { attributes: true, childList: true, subtree: true });
        }
      }, 3000)
    }

    return () => {
      if (observer != null) {
        observer.disconnect();
      }
    };
  }, []);

  return (
    <div className='redaction-contract-clauses'>
      <OrganizationClauseConfirmationPopinSave editor={editor} />
      <OrganizationClauseConfirmationPopinDelete editor={editor} />

      <ErrorBoundary FallbackComponent={<RedactionEditorFallback />}>
        <Plate {...editableProps} editor={editor} readOnly={editingPermission === EditingPermission.READ_ONLY}>
          <div id={PopinHelpers.PLATE_TOOLBAR} />
          <PlateContent id={contractClausesRootElementId} />
          <MnPortal targetDomId={plateFixedToolbarDomId}>
            <FixedToolbar />
          </MnPortal>
          <BalloonToolbar />
        </Plate>
      </ErrorBoundary>
    </div>
  );
};
