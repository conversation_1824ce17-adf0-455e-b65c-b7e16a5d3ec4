import React, { useState } from 'react';
import { useSelector } from 'react-redux';
import isHotkey from 'is-hotkey';
import { storeDispatch } from '@mynotary/frontend/front-mynotary/api';
import { JeffersonFlatNode, selectContractEditedClause, selectOrganizationClause } from '@mynotary/frontend/legals/api';

import { classNames } from '@mynotary/frontend/shared/util';
import {
  createPlatePlugin,
  isElement,
  NodeEntry,
  ParagraphPlugin,
  PlateElement,
  PlateElementProps,
  PointApi,
  TElement,
  useReadOnly,
  AnyPluginConfig
} from '@mynotary/frontend/text-editor/api';
import { MnSvg, MnTooltip } from '@mynotary/frontend/shared/ui';
import { getRedactionEditorContext } from '../redaction-context-plugin';
import { EditingLevel, useClauseEditingLevel } from './use-clause-editing-level';
import { ClauseActions } from './clause-actions';
import { EditingPermission } from '@mynotary/frontend/redactions/core';
import { ClauseTooltip } from './clause-tooltip';
import { ClauseNonEditableElement, ClauseNonEditablePlugin } from './clause-non-editable-plugin';
import { ClauseDetailsElement, ClauseDetailsPlugin } from './clause-details-plugin';
import { ClauseEditableElement, ClauseEditablePlugin } from './clause-editable-plugin';
import { setErrorMessage } from '@mynotary/frontend/snackbars/api';
import { ClausePluginChangeHandler } from './clause-plugin-change-handler';
import { isEmptyEditedClause } from './is-empty-edited-clause';
import { RedactionEditorElement } from '../../redaction-editor';

export const CLAUSE_ERROR_CLASSNAME = 'contract-clause-error';

const ClausePluginComponent = ({
  children,
  editor,
  element,
  path,
  ...props
}: PlateElementProps<ClauseElement, AnyPluginConfig>) => {
  const { frameNode } = element;
  const redactionEditorContext = getRedactionEditorContext(editor);
  const contractId = redactionEditorContext.contractId;
  const clauseId = frameNode.id;
  const editedClause = useSelector(selectContractEditedClause(contractId, clauseId));
  const orgaClause = useSelector(selectOrganizationClause(contractId, clauseId, frameNode.prefix));
  const editingLevel = useClauseEditingLevel({ editor, node: frameNode });
  const focusNode = editor.children[editor.selection?.focus.path[0] ?? -1] as ClauseElement;
  const isFocus = focusNode?.frameNode?.id === clauseId;
  const isBr = !editedClause && !orgaClause && frameNode.content === '\n------------------------\n\n';
  const isEmpty = isEmptyEditedClause({
    editedClauseContent: editedClause?.content,
    orgaClauseContent: orgaClause?.content
  });
  const isEdited = editingLevel === EditingLevel.EDITED || editingLevel === EditingLevel.DEFAULT_ORGANIZATION;

  const readOnly = useReadOnly();
  const [isHovered, setIsHovered] = useState(false);

  return (
    <PlateElement
      {...props}
      attributes={{
        ...props.attributes,
        'data-empty-clause': isEmpty,
        'data-slate-type': ClausePlugin.key,
        'data-testid': clauseId,
        'onMouseLeave': () => setIsHovered(false),
        'onMouseOver': () => setIsHovered(true)
      }}
      className={classNames('contract-clause', {
        canEditClauses: !readOnly,
        editedClause: isEdited,
        isFocus
      })}
      editor={editor}
      element={element}
      path={path}
    >
      {!isBr && <ClauseActions editor={editor} isClauseHovered={isHovered} node={frameNode} path={path} />}
      <div className={classNames('plate-editor-component', { isFocus })}>{children}</div>
      {(isEdited || orgaClause) && (
        <div className='contract-clause-flag'>
          <MnTooltip
            content={ClauseTooltip({
              isEdited,
              isOrgaClause: !!orgaClause,
              username: editedClause?.userName ?? orgaClause?.userName
            })}
            contentClassName='contract-clause-tooltip'
          >
            <MnSvg path='/assets/images/pictos/icon/flag.svg' size='small' variant='orange200' />
          </MnTooltip>
        </div>
      )}
    </PlateElement>
  );
};

/**
 * The `ClausePlugin` define the component and behavior of clauses within a contract when converting from a Jefferson
 * format.
 *
 * Plugin Structure:
 * - **Maximum Children**: The plugin can have a maximum of 2 children.
 *   - **First Child**: This will always be either a `ClauseEditablePlugin` or a `ClauseNonEditablePlugin` depending
 *   on the user's permissions.
 *   - **Second Child (Optional)**: This is used to add details to the clause when the user has restricted editing
 *   rights. In this case, the second child will be a `ClauseDetailsPlugin`.
 *
 * Handlers and Behavior:
 * 1. **Clause Deletion**: A clause should never be completely deleted. In such cases, there will always be at least one child with an empty text node in the last child of the tree.
 * 2. **Debounced Updates**: Clause updates should be debounced and should not be triggered by any operation.
 * 3. **Shortcut Handling**: The `cmd + a` shortcut should only select the children of the clause and not the entire editor.
 * 4. **Normalization**: The plugin normalizes the content structure of a clause to ensure consistency. It's particularly
 * important when content is copy-pasted from another clause.
 */
export const ClausePlugin = createPlatePlugin({
  handlers: {
    onChange: ({ editor, value }) => {
      const clausePluginChangeHandler = new ClausePluginChangeHandler(editor);

      /**
       * It's safe to cast the value to RedactionEditorElement[] because we know that the editor is a RedactionEditor.
       */
      clausePluginChangeHandler
        .handleChange(value as RedactionEditorElement[])
        .catch(() => storeDispatch(setErrorMessage('Une erreur est survenue lors de la sauvegarde de la clause.')));
    },
    onKeyDown: ({ editor, event }) => {
      const { editingPermission } = getRedactionEditorContext(editor);

      /**
       * We don't want to select all the content of the clause if the user can only add precisions to it.
       */
      if (editingPermission === EditingPermission.ADD_CLAUSE_DETAILS) {
        return;
      }

      if (isHotkey('mod+a', event)) {
        const currentFocus = editor.selection?.anchor.path[0];
        if (currentFocus) {
          const focusPath = [currentFocus];

          editor.tf.select(focusPath);

          event.preventDefault();
          event.stopPropagation();
        }
      }
    }
  },
  key: 'ELEMENT_CLAUSE',
  node: {
    component: ClausePluginComponent,
    isElement: true,
    type: 'ELEMENT_CLAUSE'
  },
  plugins: [ClauseEditablePlugin, ClauseNonEditablePlugin, ClauseDetailsPlugin]
}).overrideEditor(({ api: { end, nodes, start }, editor, tf: { deleteBackward, deleteForward, normalizeNode } }) => ({
  transforms: {
    deleteBackward: () => {
      const [clause] = nodes({ match: isClauseElement });
      if (clause) {
        const [, cellPath] = clause;
        const startPoint = start(cellPath);

        if (editor.selection && startPoint != null && PointApi.equals(editor.selection.anchor, startPoint)) {
          return;
        }
      }
      deleteBackward();
    },
    deleteForward: () => {
      const [clause] = nodes({ match: isClauseElement });
      if (clause) {
        const [, cellPath] = clause;
        const endPoint = end(cellPath);

        if (editor.selection && endPoint != null && PointApi.equals(editor.selection.anchor, endPoint)) {
          return;
        }
      }
      deleteForward();
    },
    normalizeNode: ([node, path]: NodeEntry) => {
      if (!isElement(node)) {
        return normalizeNode([node, path]);
      }

      if (node.type === ClausePlugin.key && path.length > 1) {
        editor.tf.setNodes({ type: ParagraphPlugin.key }, { at: path });
        return;
      }

      if (
        (node.type === ClauseEditablePlugin.key ||
          node.type === ClauseNonEditablePlugin.key ||
          node.type === ClauseDetailsPlugin.key) &&
        path.length > 2
      ) {
        editor.tf.setNodes({ type: ParagraphPlugin.key }, { at: path });
        return;
      }

      normalizeNode([node, path]);
    }
  }
}));

export interface ClauseElement extends TElement {
  children: Array<ClauseEditableElement | ClauseNonEditableElement | ClauseDetailsElement>;
  frameNode: JeffersonFlatNode;
  type: 'ELEMENT_CLAUSE';
}

export function isClauseElement(value: { type: string }): value is ClauseElement {
  return value.type === ClausePlugin.key;
}
