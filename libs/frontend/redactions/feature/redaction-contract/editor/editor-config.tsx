import { withPlaceholders } from './plugins/placeholder';
import { TableOfContentPlugin } from './plugins/tableOfContentPlugin';
import { ClausePlugin } from './plugins/clause/clause-plugin';
import { LineBreakPlugin } from './plugins/lineBreakPlugin';
import { PageBreakPlugin } from './plugins/pageBreakPlugin';

import {
  AlignPlugin,
  BoldPlugin,
  BulletedListPlugin,
  DebugPlugin,
  DocxPlugin,
  FontBackgroundColorPlugin,
  FontColorPlugin,
  HEADING_KEYS,
  HeadingElement,
  HeadingPlugin,
  ImageElement,
  ImagePlugin,
  IndentPlugin,
  ItalicPlugin,
  LinkElement,
  LinkFloatingToolbar,
  LinkPlugin,
  ListElement,
  ListItemPlugin,
  ListPlugin,
  NodeIdPlugin,
  NumberedListPlugin,
  ParagraphElement,
  ParagraphPlugin,
  PlateElement,
  PlateLeaf,
  ResetNodePlugin,
  SoftBreakPlugin,
  StrikethroughPlugin,
  TableCellElement,
  TableCellHeaderElement,
  TableCellHeaderPlugin,
  TableCellPlugin,
  TableElement,
  TablePlugin,
  TableRowElement,
  TableRowPlugin,
  UnderlinePlugin,
  withProps
} from '@mynotary/frontend/text-editor/api';
import { FlyLeafPlugin } from './plugins/flyLeaf/flyLeafPlugin';
import { DividerPlugin } from './plugins/dividerPlugin';
import { ExtraPdfPagePlugin } from './plugins/extra-pdf-page-plugin';
import { environment } from '@mynotary/frontend/shared/environments-util';
import { uploadPlateImage } from './plugins/image-plugin';
import { VariablePlugin } from './plugins/variable/variable-plugin';
import { FocusPlugin } from './plugins/focus-plugin';

export const textEditorComponents = withPlaceholders({
  [HEADING_KEYS.h1]: HeadingElement,
  [HEADING_KEYS.h2]: HeadingElement,
  [HEADING_KEYS.h3]: HeadingElement,
  [HEADING_KEYS.h4]: HeadingElement,
  [ImagePlugin.key]: ImageElement,
  [BulletedListPlugin.key]: withProps(ListElement, { variant: 'ul' }),
  [ListItemPlugin.key]: withProps(PlateElement, { as: 'li' }),
  [NumberedListPlugin.key]: withProps(ListElement, { variant: 'ol' }),
  [LinkPlugin.key]: LinkElement,
  [ParagraphPlugin.key]: ParagraphElement,
  [TablePlugin.key]: TableElement,
  [TableCellPlugin.key]: TableCellElement,
  [TableCellHeaderPlugin.key]: TableCellHeaderElement,
  [TableRowPlugin.key]: TableRowElement,
  [BoldPlugin.key]: withProps(PlateLeaf, { as: 'strong' }),
  [ItalicPlugin.key]: withProps(PlateLeaf, { as: 'em' }),
  [UnderlinePlugin.key]: withProps(PlateLeaf, { as: 'u' }),
  [StrikethroughPlugin.key]: withProps(PlateLeaf, { as: 's' })
});

export const textEditorPlugins = [
  DebugPlugin.configure({
    options: {
      isProduction: environment.production,
      logLevel: 'warn'
    }
  }),
  ParagraphPlugin,
  LinkPlugin.extend({
    render: { afterEditable: () => <LinkFloatingToolbar /> }
  }),
  ImagePlugin.configure({
    options: { uploadImage: uploadPlateImage }
  }),
  IndentPlugin.configure({
    inject: {
      targetPlugins: [ParagraphPlugin.key]
    }
  }),
  ListPlugin,
  /**
   * NodeIdPlugin is necessary for tables to work
   */
  NodeIdPlugin.configure({
    options: {
      normalizeInitialValue: true
    }
  }),
  ResetNodePlugin,
  TablePlugin,
  HeadingPlugin,
  AlignPlugin.configure({
    inject: {
      targetPlugins: [ParagraphPlugin.key, HEADING_KEYS.h2, HEADING_KEYS.h3]
    }
  }),
  BoldPlugin,
  ItalicPlugin,
  StrikethroughPlugin,
  UnderlinePlugin,
  SoftBreakPlugin,
  DocxPlugin,
  FontColorPlugin,
  FontBackgroundColorPlugin,
  // custom
  VariablePlugin,
  ClausePlugin,
  LineBreakPlugin,
  PageBreakPlugin,
  FlyLeafPlugin,
  DividerPlugin,
  TableOfContentPlugin,
  ExtraPdfPagePlugin,
  FocusPlugin
];
