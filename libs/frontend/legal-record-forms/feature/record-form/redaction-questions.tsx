import { isGelAvoirQuestion, isRegisterQuestion, QuestionsProps } from '@mynotary/frontend/shared/forms-util';
import React, { ReactElement } from 'react';
import { MnRegisterQuestion } from '@mynotary/frontend/registers/api';
import { useSelector } from 'react-redux';
import { Questions } from '@mynotary/frontend/legals/api';
import { selectFormsMode } from '@mynotary/frontend/legals/api';
import { GelAvoirQuestion } from '@mynotary/frontend/gel-avoirs/api';

export const RedactionQuestions = (props: QuestionsProps): ReactElement => {
  const formTypeMode = useSelector(selectFormsMode);
  const isDefaultQuestion = formTypeMode === 'DEFAULT_QUESTIONS';
  const isFormQuestionDisabled = !props.editable || !!props.answer?.locked;

  return (
    <>
      <Questions {...props} />
      {isRegisterQuestion(props.question) && (
        // MyNotary Register Workflow's question

        // TODO : passer plutôt par le V2, qui lui
        // <MnRegisterQuestion
        //   answer={props.answer}
        //   debounce={props.debounce}
        //   disabled={isFormQuestionDisabled || isDefaultQuestion}
        //   onChange={props.onChange}
        //   question={props.question}
        // />
      )}
      {isGelAvoirQuestion(props.question) && (
        <GelAvoirQuestion
          answerValue={props.answer?.value}
          className={props.className}
          disabled={isFormQuestionDisabled}
          onChange={props.onChange}
          question={props.question}
        />
      )}
    </>
  );
};
