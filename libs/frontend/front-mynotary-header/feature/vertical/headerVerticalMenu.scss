@use 'style/variables/colors' as *;
@use 'style/mixins/index' as *;

.header-vertical-menu-overlay {
  pointer-events: none;
  will-change: opacity;

  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;

  width: 100vw;
  height: 100vh;

  opacity: 0;
  background-color: $black;

  transition: opacity 0.25s ease;

  &.open {
    pointer-events: auto;
    opacity: 0.4;
  }
}

.header-vertical-menu {
  position: fixed;
  top: 0;
  right: 0;
  transform: translateX(360px);

  overflow: auto;

  width: 360px;
  max-width: 80vw;
  height: 100vh;

  background: white;

  transition: transform 0.25s ease;

  &.open {
    transform: translateX(0);
  }

  .header-vertical-menu-top {
    display: flex;
    flex-direction: row-reverse;
    align-items: center;
    justify-content: space-between;

    height: 60px;
    padding: 20px;

    background: $gray50;
  }

  .header-vertical-links {
    border-bottom: 1px solid $gray150;
  }

  .header-vertical-profile-name-container {
    display: flex;
    flex-direction: row;
    align-items: center;
  }

  .header-vertical-profile-name {
    font-weight: 500;
    color: $black;
  }

  .header-vertical-profile-icon {
    width: 36px;
    height: 36px;
    margin-right: 16px;
    vertical-align: middle;
  }

  .header-vertical-profile-name-container {
    display: flex;
    align-items: center;
    gap: 16px;
  }

  .header-vertical-menu-bottom-filler {
    height: 100px;
  }
}
