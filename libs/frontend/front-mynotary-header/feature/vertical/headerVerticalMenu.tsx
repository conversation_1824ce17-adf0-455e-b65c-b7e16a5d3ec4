import './headerVerticalMenu.scss';
import React, { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import {
  ContactAvatar,
  HeaderNavigationItem,
  MnButtonClose,
  NavBarItem,
  Separator
} from '@mynotary/frontend/shared/ui';
import { selectCurrentUser } from '@mynotary/frontend/user-session/api';
import { classNames, MnProps } from '@mynotary/frontend/shared/util';
import { NotificationIcon } from '@mynotary/frontend/notifications/api';
import { getPublicFile } from '@mynotary/frontend/files/api';

interface HeaderVerticalMenu extends MnProps {
  actionItems: NavBarItem[];
  navigationItems: NavBarItem[];
  onClose: () => void;
}

const HeaderVerticalMenu = ({ actionItems, className, navigationItems, onClose }: HeaderVerticalMenu): ReactElement => {
  const user = useSelector(selectCurrentUser);

  return (
    <>
      <div className={classNames('header-vertical-menu-overlay', className)} onClick={onClose} />
      <div className={classNames('header-vertical-menu', className)}>
        <div className='header-vertical-menu-top'>
          <MnButtonClose onClick={onClose} />
          <NotificationIcon />
          {user && (
            <div className='header-vertical-profile-name-container'>
              {user.profilePictureFileId ? (
                <ContactAvatar
                  firstname={user.firstname}
                  lastname={user.lastname}
                  photoUrl={getPublicFile(user.profilePictureFileId)}
                />
              ) : (
                <img
                  className='header-vertical-profile-icon'
                  src='/assets/images/pictos/illustrated/interlocutors/agent_H_blond.svg'
                />
              )}

              <span className='header-vertical-profile-name'>
                {user.firstname} {user.lastname}
              </span>
            </div>
          )}
        </div>
        <div className='header-vertical-links'>
          {navigationItems.map((item) => {
            if (!item.checkCondition || !!item.checkCondition?.()) {
              return (
                <div key={item.id}>
                  <HeaderNavigationItem className='header-vertical-link' item={item} onItemClick={onClose} />
                  {item.separator && <Separator />}
                </div>
              );
            }
            return undefined;
          })}
        </div>
        {actionItems.map((action) => (
          <div key={action.id}>
            <HeaderNavigationItem
              className='header-vertical-link'
              item={action}
              key={action.id}
              onItemClick={onClose}
            />
            {action.separator && <Separator />}
          </div>
        ))}
        <div className='header-vertical-menu-bottom-filler' />
      </div>
    </>
  );
};

export { HeaderVerticalMenu };
