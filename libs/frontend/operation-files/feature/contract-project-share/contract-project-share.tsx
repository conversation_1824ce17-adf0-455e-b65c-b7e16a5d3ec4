import { useContractProjectShare } from '../use-operation-contract-share';
import { useState } from 'react';
import { OperationContractFilePopin } from '../operation-contract-file-popin/operation-contract-file-popin';
import { Folder } from '@mynotary/crossplatform/files/api';

interface TriggerElementProps {
  disabled?: boolean;
  onClick: () => void;
}

interface ContractProjectShareProps {
  contractId: number;
  onFinish?: () => void;
  operationId: number;
  triggerElement: React.FC<TriggerElementProps>;
}

export const ContractProjectShare = ({
  contractId,
  onFinish,
  operationId,
  triggerElement: TriggerElement
}: ContractProjectShareProps) => {
  const [open, setOpen] = useState(false);

  const { canShareProject, isLoading, shareProject } = useContractProjectShare({ contractId, operationId });

  const handleShareProject = async (folder: Folder) => {
    await shareProject(folder);
    handleClose();
  };

  const handleClose = () => {
    setOpen(false);
    onFinish?.();
  };

  if (!canShareProject) {
    return null;
  }

  return (
    <>
      <TriggerElement disabled={isLoading} onClick={() => setOpen(true)} />
      {open && (
        <OperationContractFilePopin
          contractId={contractId}
          isLoading={isLoading}
          onClose={handleClose}
          onShareProject={handleShareProject}
        />
      )}
    </>
  );
};
