@use 'style/variables/colors' as *;
@use 'style/mixins' as *;

.admin-community-organization-creation {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;

  .acoc-validate {
    margin: 5px;
  }

  .acoc-subtitle {
    @include h4-font;

    margin: 10px 0;
    font-weight: 500;
    color: $black;
  }

  .acoc-actions-container {
    display: flex;
    justify-content: space-between;
    margin-top: 32px;
  }
}

.admin-organization-creation-popin {
  padding: 32px;
}

.admin-organization-creation-popin-actions {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin: 10px;

  .organization-creation-button {
    margin: 5px;
  }
}
