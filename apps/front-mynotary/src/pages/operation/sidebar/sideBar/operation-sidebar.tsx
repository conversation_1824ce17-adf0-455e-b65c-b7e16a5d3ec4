import './operation-sidebar.scss';
import { classNames, useBodyClass } from '@mynotary/frontend/shared/util';
import { ReactElement } from 'react';
import { useSelector } from 'react-redux';
import { MnButtonClose } from '@mynotary/frontend/shared/ui';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import {
  selectSidebarFeature,
  closeSidebar,
  SidebarActionType,
  openSidebarAction
} from '@mynotary/frontend/legals/store';
import { MnTask } from 'pages/operation/sidebar/tasks/task/task';
import { TaskEdition } from 'pages/operation/sidebar/tasks/edition/taskEdition';
import { SidebarMenu } from './sidebar-menu';
import { Tasks } from 'pages/operation/sidebar/tasks/tasks';
import { MnParticipants } from 'pages/operation/sidebar/participants/participants';
import { useOperationIdFromParams } from '@mynotary/frontend/routes/feature';
import { TaskCreationDefault, TaskCreationCustom, TaskCreationShareRecord } from '@mynotary/frontend/legals/feature';
import { TaskTypeAndReference } from '@mynotary/frontend/legals/core';
import { TaskType } from '@mynotary/crossplatform/legals/core';
import { OrdersOperation } from '@mynotary/frontend/orders/feature';
import { OperationEmails } from '@mynotary/frontend/emails/feature';
import { TaskCreationValidateContract } from '@mynotary/frontend/contract-validations/api';

export const OperationSidebar = (): ReactElement | null => {
  const { opened } = useSelector(selectSidebarFeature);
  const dispatch = useAsyncDispatch();

  useBodyClass('as-side-bar');
  useBodyClass(opened ? 'as-opened-side-bar' : 'as-closed-side-bar');

  const handleClose = () => dispatch(closeSidebar());

  return (
    <div className={classNames('mn-sidebar')}>
      <div className={classNames('sidebar-blur', { opened })} onClick={handleClose} />
      <SidebarMenu />
      <div className={classNames('sidebar-panel', { opened })}>
        <div className={classNames('panel-wrapper', { locked: false })}>
          <MnButtonClose className='panel-close' onClick={handleClose} testId={'close-side-panel'} />
          <SidebarAction />
        </div>
      </div>
    </div>
  );
};

const displayTaskCreation = ({
  onTaskCreated,
  operationId,
  typeAndReference
}: {
  onTaskCreated: () => void;
  operationId: number;
  typeAndReference: TaskTypeAndReference;
}) => {
  switch (typeAndReference.type) {
    case TaskType.VALIDATE_CONTRACT:
      return (
        <TaskCreationValidateContract
          onContractValidationRequested={onTaskCreated}
          operationId={operationId}
          reference={typeAndReference}
        />
      );
    case TaskType.CUSTOM:
      return <TaskCreationCustom onTaskCreated={onTaskCreated} operationId={operationId} />;
    case TaskType.SHARE_OPERATION_RECORD:
      return (
        <TaskCreationShareRecord
          onTaskCreated={onTaskCreated}
          operationId={operationId}
          reference={typeAndReference.reference}
        />
      );
    case TaskType.READ_PROJECT_CONTRACT:
      return (
        <TaskCreationDefault
          onTaskCreated={onTaskCreated}
          operationId={operationId}
          typeAndReference={typeAndReference}
        />
      );
    default:
      return (
        <TaskCreationDefault
          onTaskCreated={onTaskCreated}
          operationId={operationId}
          typeAndReference={typeAndReference}
        />
      );
  }
};

export const SidebarAction = () => {
  const dispatch = useAsyncDispatch();
  const action = useSelector(selectSidebarFeature).action;
  const handleClose = () => dispatch(closeSidebar());
  const openTasksList = () => dispatch(openSidebarAction({ type: SidebarActionType.TASK_LIST }));
  const operationId = useOperationIdFromParams();

  const displaySidebarAction = () => {
    switch (action?.type) {
      case SidebarActionType.TASK_CREATION:
        return displayTaskCreation({
          onTaskCreated: openTasksList,
          operationId: action.legalComponentId,
          typeAndReference: action.typeAndReference
        });
      case SidebarActionType.TASK_EDITION:
        return <TaskEdition onTaskValidation={openTasksList} task={action.task} />;
      case SidebarActionType.TASK_CONTENT:
        return <MnTask taskId={action.taskId} />;
      case SidebarActionType.TASK_LIST:
        return <Tasks legalComponentId={operationId} />;
      case SidebarActionType.PARTICIPANTS:
        return <MnParticipants operationId={operationId} />;
      case SidebarActionType.ORDER:
        return <OrdersOperation operationId={operationId} />;
      case SidebarActionType.EMAILS:
        return <OperationEmails operationId={operationId} />;
      default:
        return null;
    }
  };

  if (action == null) {
    return null;
  }

  return (
    <div className='panel-contextual-actions'>
      <div className='panel-contextual-action'>
        <div className='panel-close' onClick={handleClose} />
        {displaySidebarAction()}
      </div>
    </div>
  );
};
