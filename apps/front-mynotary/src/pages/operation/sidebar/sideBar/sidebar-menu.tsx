import './sidebar-menu.scss';
import { PermissionType } from '@mynotary/crossplatform/roles/api';
import { FeatureState, getFeatureTooltip } from '@mynotary/frontend/features/core';
import { useFeatureState } from '@mynotary/frontend/features/feature';
import {
  selectLegalComponentTemplate,
  selectOperation,
  selectOperationPermission
} from '@mynotary/frontend/legals/store';
import { PopinType } from '@mynotary/frontend/popins/core';
import { openPopin } from '@mynotary/frontend/popins/store';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { MnTooltip, MnSvg } from '@mynotary/frontend/shared/ui';
import { SidebarActionType, openSidebarAction, selectSidebarFeature } from '@mynotary/frontend/legals/store';
import classNames from 'classnames';
import { useMemo } from 'react';
import { useSelector } from 'react-redux';
import { useOperationIdFromParams } from '@mynotary/frontend/routes/feature';
import { useWhitelist } from '@mynotary/frontend/whitelist/api';
import { FeatureWhiteListed } from '@mynotary/crossplatform/feature-flags/api';
import { LegalOperationTemplate } from '@mynotary/crossplatform/legal-templates/api';
import { FeatureType } from '@mynotary/crossplatform/features/api';

export const SidebarMenu = () => {
  const dispatch = useAsyncDispatch();
  const operationId = useOperationIdFromParams();
  const operation = useSelector(selectOperation(operationId));
  const externalParticipantFeature = useFeatureState(FeatureType.EXTERNAL_PARTICIPANTS);
  const canSeeParticipants = !!useSelector(
    selectOperationPermission(PermissionType.CREATE_OPERATION_INVITATION, operationId)
  );
  const sidebar = useSelector(selectSidebarFeature);
  const isERPWhiteList = useWhitelist(FeatureWhiteListed.ERP);
  const operationTemplate = useSelector(selectLegalComponentTemplate<LegalOperationTemplate>(operationId));
  const legalRecordTemplateId = operationTemplate?.config.preEtatDate?.destinationLegalRecordTemplateId;

  const isOrderListAvailable = isERPWhiteList || legalRecordTemplateId;

  const sidebarItems = useMemo(() => {
    if (operation == null) {
      return [];
    }

    const items: SideBarItem[] = [
      {
        icon: '/assets/images/pictos/icon/clipboard-light.svg',
        id: SidebarActionType.TASK_LIST,
        isSelected: sidebar.action?.type.startsWith('TASK') === true,
        label: 'Tâches',
        onClick: () => dispatch(openSidebarAction({ type: SidebarActionType.TASK_LIST }))
      }
    ];

    if (externalParticipantFeature.featureState !== FeatureState.INACTIVE) {
      items.push({
        disabled: externalParticipantFeature.featureState === FeatureState.UPGRADABLE || !canSeeParticipants,
        icon: '/assets/images/pictos/icon/users-light.svg',
        id: SidebarActionType.PARTICIPANTS,
        isSelected: sidebar.action?.type === SidebarActionType.PARTICIPANTS,
        label: 'Intervenants',
        onClick:
          externalParticipantFeature.featureState === FeatureState.UPGRADABLE
            ? () =>
                dispatch(openPopin({ featureType: FeatureType.EXTERNAL_PARTICIPANTS, type: PopinType.SUBSCRIPTION }))
            : () => dispatch(openSidebarAction({ type: SidebarActionType.PARTICIPANTS })),
        tooltip: getFeatureTooltip({
          canUpgrade: externalParticipantFeature.canUpgrade,
          featurePermission: canSeeParticipants,
          featureState: externalParticipantFeature.featureState
        })
      });
    }

    if (isOrderListAvailable) {
      items.push({
        icon: '/assets/images/pictos/icon/truck.svg',
        id: SidebarActionType.ORDER,
        isSelected: sidebar.action?.type === SidebarActionType.ORDER,
        label: 'Commandes',
        onClick: () => dispatch(openSidebarAction({ type: SidebarActionType.ORDER }))
      });
    }

    items.push({
      icon: '/assets/images/pictos/icon/mail.svg',
      id: SidebarActionType.EMAILS,
      isSelected: sidebar.action?.type === SidebarActionType.EMAILS,
      label: 'Emails',
      onClick: () => dispatch(openSidebarAction({ type: SidebarActionType.EMAILS }))
    });

    return items;
  }, [
    operation,
    sidebar.action?.type,
    externalParticipantFeature.featureState,
    externalParticipantFeature.canUpgrade,
    isOrderListAvailable,
    dispatch,
    canSeeParticipants
  ]);

  return (
    <div className='sidebar-menu'>
      <div className='sidebar-items-wrapper'>
        {sidebarItems.map((item) => (
          <MnTooltip content={item.tooltip} key={item.id}>
            <div
              className={classNames('sidebar-item', {
                disabled: item.disabled,
                selected: item.isSelected
              })}
              data-testid={item.id}
              id={`${item.id}-sidebar-item`}
              onClick={item.onClick}
            >
              <div className={classNames('item-icon', { notification: !!item.notification })}>
                <MnSvg path={item.icon} size='small' variant={item.disabled ? 'gray500-gray700' : 'primary'} />
              </div>
              <span className='custom-title'>{item.label}</span>
            </div>
          </MnTooltip>
        ))}
      </div>
    </div>
  );
};

type SideBarItem = {
  disabled?: boolean;
  icon: string;
  id: SidebarActionType;
  isSelected: boolean;
  label: string;
  notification?: boolean;
  onClick: () => void;
  tooltip?: string;
};
