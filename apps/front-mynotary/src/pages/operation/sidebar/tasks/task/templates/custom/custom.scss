@use 'style/mixins' as *;
@use 'style/variables/colors' as *;

.custom-task-template {
  padding: 0 40px;
  text-align: center;

  .ctt-label {
    @include medium-font;

    margin-bottom: 24px;
    padding: 0 30px;
    color: $black;
    text-align: center;
  }

  .ctt-description-container {
    overflow: hidden;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    justify-content: center;

    margin-top: 8px;
  }

  .ctt-description-icon {
    flex-shrink: 0;

    width: 12px;
    height: 12px;
    margin-top: 3px;
    margin-right: 5px;
  }

  .ctt-description {
    @include small-font;

    flex: 1 1;

    max-width: 380px;

    color: $black;
    text-align: left;
    white-space: pre-wrap;
  }
}
