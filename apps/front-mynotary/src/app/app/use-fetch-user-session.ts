import { useEffect } from 'react';
import { useSelector } from 'react-redux';
import {
  getMembers,
  loadMemberIdFromLocalStorage,
  selectCurrentMember,
  selectUserSessionLoadingState,
  setUser,
  switchMember,
  updateUserSessionStatus,
  UserSessionStatus
} from '@mynotary/frontend/user-session/store';
import { getRoleWithPermission } from '@mynotary/frontend/roles/store';
import { routePaths } from '@mynotary/frontend/routes/api';
import { useAsyncDispatch } from '@mynotary/frontend/shared/redux-util';
import { getStatuses } from '@mynotary/frontend/operation-status/store';
import { getCachedOrganization } from '@mynotary/frontend/organizations/store';
import { logout, selectActiveTokenInfo } from '@mynotary/frontend/auth/store';
import { UsersClient } from '@mynotary/frontend/users/core';
import { useService } from '@mynotary/frontend/shared/injector-util';
import { isLoginWithSsoPending } from '@mynotary/frontend/users/feature';
import { hasLogAsParams } from '@mynotary/frontend/auth/api';
import { useNavigate } from 'react-router-dom';
import { displayGlobalLoader, hideGlobalLoader } from '@mynotary/frontend/shared/util';
import { setErrorMessage } from '@mynotary/frontend/snackbars/store';

/**
 * Fetch data related to the user session.
 * User session data are mandatory for all private routes (see: PrivateRoute).
 *
 * Key points:
 * - Redirect to login page is token is not found
 * - Redirect to verification page if the user is not verified
 * - Fetch User, Member, Organization and Role
 * - Session is not fetched if SSO or log as parameters are detected. Theses parameters are removed in useLogAs.ts hook
 */
export const useFetchUserSession = () => {
  const usersClient = useService(UsersClient);
  const dispatch = useAsyncDispatch();
  const tokenInfo = useSelector(selectActiveTokenInfo);
  const userId = tokenInfo?.userId;
  const userSessionLoadingState = useSelector(selectUserSessionLoadingState);
  const shouldWaitSso = isLoginWithSsoPending() || hasLogAsParams();
  const navigate = useNavigate();
  const currentMember = useSelector(selectCurrentMember);

  useEffect(() => {
    if (userId == null && !shouldWaitSso) {
      navigate(routePaths.public.login.path);
    }
  }, [navigate, shouldWaitSso, userId]);

  useEffect(() => {
    if (userSessionLoadingState !== UserSessionStatus.NOT_INITIALIZED || userId == null) {
      return;
    }

    const fetchUserSession = async () => {
      dispatch(updateUserSessionStatus(UserSessionStatus.PENDING));
      displayGlobalLoader();
      const user = await usersClient.getUserById(userId);
      dispatch(setUser(user));

      if (!user.verified) {
        dispatch(updateUserSessionStatus(UserSessionStatus.NOT_INITIALIZED));
        navigate(routePaths.public.codeVerification.path);
        hideGlobalLoader();
        return;
      }

      const members = await dispatch(getMembers(user.id));

      if (members.length > 0) {
        let currentMember = members[0];
        const lastMemberId = loadMemberIdFromLocalStorage(user.id);
        const previousMember = members.find((m) => parseInt(m.id) === lastMemberId);

        if (previousMember) {
          currentMember = previousMember;
        }
        dispatch(switchMember(parseInt(currentMember.id)));
      }
      dispatch(updateUserSessionStatus(UserSessionStatus.INITIALIZED));
    };

    fetchUserSession()
      .catch((e) => {
        console.log(e);
        dispatch(logout());
      })
      .finally(() => {
        hideGlobalLoader();
      });
  }, [dispatch, navigate, userId, userSessionLoadingState, usersClient]);

  useEffect(() => {
    const fetchMemberData = async () => {
      if (currentMember?.organizationId) {
        await dispatch(getCachedOrganization(parseInt(currentMember.organizationId)));
        await dispatch(getStatuses(parseInt(currentMember.organizationId)));
      }

      if (currentMember?.roleId) {
        await dispatch(getRoleWithPermission(currentMember.roleId));
      }
    };

    fetchMemberData().catch((e) => {
      dispatch(setErrorMessage(`Une erreur est survenue lors de la récupération des données de l'organisation`));
      console.error(e);
    });
  }, [currentMember?.roleId, currentMember?.organizationId, dispatch]);
};
