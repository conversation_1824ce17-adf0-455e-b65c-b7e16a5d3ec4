import { expect, test } from '../../shared/fixtures';
import { RedactionPage } from '../../pages/redaction.page';
import { authUserStoragePath } from '../../shared/utils';
import { getByVisibleTestId } from '../../utils/locators.utils';

test.use({ storageState: authUserStoragePath });
/* Serial is needed because we are updating default clauses which is global to organization */
test.describe.configure({ mode: 'serial' });

test('edit default clause', async ({ clients, page }) => {
  const redactionPage = new RedactionPage(page);
  const operation = await clients.operations.createVenteAncien();
  const operationId = operation.id;

  /* Create two contract */
  const contractId = await clients.contracts.createDip({ operationId });
  const anotherContractId = await clients.contracts.createDip({ operationId });

  /* Reset default clauses to prevent collision */
  await clients.contracts.resetDefaultClauses({ contractId, operationId });

  /* Navigate to contract */
  await redactionPage.goTo({ contractId, operationId });

  /* Save target clause locator in variable */
  const clause = redactionPage.getClause('client');

  /* Edit clause  */
  await clause.fill('test');
  await page.waitForResponse((resp) => resp.url().includes('/clauses'));
  await expect(clause.getByRole('heading')).toHaveText('test');

  /* Save the clause as default for organization */
  await clause.hover();
  await getByVisibleTestId({ page, testId: 'button-default-clause-save' }).click();
  await page.getByRole('button', { name: 'Confirmer' }).click();
  await page.waitForResponse((resp) => resp.url().includes('/clauses'));
  await expect(clause.getByRole('heading')).toHaveText('test');

  /* Navigate to another contract and check the value of the edited clause */
  await redactionPage.goTo({ contractId: anotherContractId, operationId });
  await expect(clause.getByRole('heading')).toHaveText('test');

  /* Reset clause value to default value */
  await clause.hover();
  await getByVisibleTestId({ page, testId: 'button-clause-reset' }).click();
  await page.getByRole('button', { name: 'Confirmer' }).click();
  await page.waitForResponse((resp) => resp.url().includes('/clauses'));
  await expect(clause.getByRole('heading')).toHaveText('Le client');

  /* Navigate to original contract and check the value has been reset */
  await redactionPage.goTo({ contractId, operationId });
  await expect(clause.getByRole('heading')).toHaveText('Le client');
});
