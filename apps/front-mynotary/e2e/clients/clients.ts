/* create a client who import all other clients */

import { RedactionsClient } from './redactions.client';
import { ContractsClient } from './contracts.client';
import { OperationE2E, OperationsClient } from './operations.client';
import { RecordsClient } from './records.client';
import { RegistersClient } from './registers.client';
import { CreditsClient } from './credits.client';
import { LettersClient } from './letters.client';
import { APIRequestContext } from '@playwright/test';
import { OrganizationsClient } from './organizations.client';
import { TestingEnv } from '../env';
import { TokensClient } from './tokens.client';

export class Clients {
  contracts: ContractsClient;
  operations: OperationsClient;
  records: RecordsClient;
  redactions: RedactionsClient;
  registers: RegistersClient;
  credits: CreditsClient;
  letters: LettersClient;
  organizations: OrganizationsClient;
  tokens: TokensClient;

  constructor(request: APIRequestContext, env: TestingEnv) {
    this.contracts = new ContractsClient(request, env);
    this.operations = new OperationsClient(request, env);
    this.records = new RecordsClient(request, env);
    this.redactions = new RedactionsClient(request, env);
    this.registers = new RegistersClient(request, env);
    this.credits = new CreditsClient(request, env);
    this.letters = new LettersClient(request, env);
    this.organizations = new OrganizationsClient(request, env);
    this.tokens = new TokensClient(request, env);
  }

  /**
   * Create a "vente ancien" operation with all the required records to validate a "mandat simplifie"
   */
  async createVenteAncienCompleted(): Promise<OperationE2E> {
    const { id: operationId } = await this.operations.createVenteAncien();
    const operation = await this.operations.getOperation(operationId);

    const record = await this.records.createHouse();
    const vendeurRecord = await this.records.createContact({ firstname: 'Foo', lastname: 'PLAYWRIGHT' });
    const agencyRecord = await this.records.createAgency();
    const intermediaire = await this.records.createIntermediaireImmo();

    await this.operations.linkRecord({
      linkId: operation.agenceLinkId,
      operationId,
      recordId: agencyRecord.id,
      type: 'AGENT_IMMOBILIER'
    });

    await this.operations.linkRecord({
      linkId: operation.bienVenduLinkId,
      operationId,
      recordId: record.id,
      type: 'BIEN_VENDU'
    });

    await this.operations.linkRecord({
      linkId: operation.vendeurLinkId,
      operationId,
      recordId: vendeurRecord.id,
      type: 'VENDEUR'
    });

    await this.operations.linkRecord({
      linkId: operation.intermediaireLinkId,
      operationId,
      recordId: intermediaire.id,
      type: 'MANDATAIRE'
    });

    return operation;
  }
}
