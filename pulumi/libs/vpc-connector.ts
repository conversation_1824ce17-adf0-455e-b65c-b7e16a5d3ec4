import * as gcp from '@pulumi/gcp';


/**
 * Vpc connector is used to allow cloud run service to use french IP address for outgoing requests.
 * It is necessary to prevent some services from blocking requests from non-european IP addresses (eg: Unis)
 * Mandatories resources have been created using this tutorial: https://cloud.google.com/run/docs/configuring/static-outbound-ip
 * - SUBNET_NAME: subnet-fr
 * - RANGE: **********/28
 * - NETWORK_NAME: vpc-network-fr
 * - REGION: europe-west9
 * - CONNECTOR_NAME: vpc-access-connector-fr
 * - PROJECT_ID: mynotary-preproduction
 * - ROUTER_NAME: cloud-router-fr
 * - ORIGIN_IP_NAME: ip-fr
 * - NAT_NAME: nat-fr
 */
export const linkToMyNotaryVpcConnector = () => {
  const name = 'vpc-access-connector-fr';
  const connector = gcp.vpcaccess.getConnectorOutput({ name });

  return connector.id;
};

export const linkToOrpiVpcConnector = () => {
  const name = 'vpc-connector-fr-orpi';
  const connector = gcp.vpcaccess.getConnectorOutput({ name });

  return connector.id;
};
