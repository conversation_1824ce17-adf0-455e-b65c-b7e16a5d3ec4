import { apiMynotaryService } from './api-mynotary';
import { createLoadBalancer } from './libs/load-balancer';
import { apiSignaturesService } from './api-signatures';
import { apiFilesService } from './api-files';
import { apiAuthService } from './api-auth';
import { apiEmailsService } from './api-emails';

createLoadBalancer();
export const mynotaryApiUrl = apiMynotaryService.name;
export const apiAuthUrl = apiAuthService.name;
export const apiSignatureApiUrl = apiSignaturesService.name;
export const apiFiles = apiFilesService.name;
export const apiEmailsUrl = apiEmailsService.name;

export * from './jobs';
